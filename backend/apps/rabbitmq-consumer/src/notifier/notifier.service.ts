import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Event } from '@shared/shared/common/constants/constants';
import { RideStatusUpdatedDto } from '@shared/shared/common/events/ride-status.update.event';
import { NotificationService } from '@shared/shared/common/notifications/engagespot/engagespot.service';
import { RabbitMQEventPublisher } from '@shared/shared/event-emitter/publishers/rabbitmq-event.publisher';
import { RideStatus } from '@shared/shared/modules/ride-matching/constants';
import { IDriverProfile } from '@shared/shared/modules/ride-matching/interfaces/driver.interface';

@Injectable()
export class NotifierService {
  private readonly logger = new Logger(NotifierService.name);

  constructor(
    private readonly rabbitmqEventPublisher: RabbitMQEventPublisher,
    private readonly engagespotService: NotificationService,
  ) {}

  @OnEvent(Event.RIDE_STATUS_UPDATED)
  async handleRideStatusUpdate(event: RideStatusUpdatedDto): Promise<void> {
    try {
      this.rabbitmqEventPublisher.publishWebSocketEvent(
        'websocket.ride.status.update',
        event,
      );
      console.log('Notifier Service - Ride Status Updated Event', event);
      if (
        event?.driver &&
        event.status === RideStatus.ACCEPTED &&
        event.riderId
      ) {
        this.logger.log(
          `Sending notification to rider ${event.riderId} about accepted ride ${event.rideId}`,
        );
        const driverData = event.driver as IDriverProfile;
        this.engagespotService.sendNotification(
          'ride_accepted_workflow',
          [
            {
              identifier: event.riderId,
              userType: 'RIDER',
            },
          ],
          {
            driverName: `${driverData.firstName} ${driverData.lastName}`,
            driverRating: driverData.driverRating,
            vehicleModel: driverData.vehicleModel,
            vehicleColor: driverData.vehicleColor,
            licensePlate: driverData.vehicleRegNumber,
            vehicleMake: driverData.vehicleModel,
            ...event,
          },
        );
      }

      this.logger.log(
        `Successfully published ride status update event for ride ${event.rideId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to publish ride status update event for ride ${event.rideId}:`,
        error,
      );
    }
  }

  /**
   * Handle driver offer created events
   */
  @OnEvent('driver.offer.created')
  async handleDriverOfferCreated(event: any): Promise<void> {
    try {
      await this.rabbitmqEventPublisher.publishWebSocketEvent(
        'websocket.driver.offer',
        event,
      );
      // Send notification to driver about new offer
      this.engagespotService.sendNotification(
        'ride_offer_workflow',
        [{ identifier: event.driverId, userType: 'DRIVER' }],
        {
          ...event,
        },
      );

      this.logger.log(
        `Successfully published driver offer event for driver ${event.driverId}, offer ${event.offerId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to publish driver offer event for driver ${event.driverId}:`,
        error,
      );
    }
  }

  /**
   * Handle driver offer timeout events
   */
  @OnEvent('driver.offer.timeout')
  async handleDriverOfferTimeout(event: any): Promise<void> {
    try {
      await this.rabbitmqEventPublisher.publishWebSocketEvent(
        'websocket.driver.offer.timeout',
        event,
      );

      this.logger.log(
        `Successfully published driver offer timeout event for driver ${event.driverId}, offer ${event.offerId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to publish driver offer timeout event for driver ${event.driverId}:`,
        error,
      );
    }
  }

  /**
   * Handle batch offers sent events
   */
  @OnEvent('driver.offers.batch.sent')
  async handleBatchOffersSent(event: any): Promise<void> {
    try {
      // Publish batch notification for tracking
      await this.rabbitmqEventPublisher.publishWebSocketEvent(
        'websocket.batch.offers.sent',
        event,
      );

      this.logger.log(
        `Successfully published batch offers sent event for ride ${event.rideId}, batch ${event.batchNumber}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to publish batch offers sent event for ride ${event.rideId}:`,
        error,
      );
    }
  }

  /**
   * Handle ride batch completed events
   */
  @OnEvent('ride.batch.completed')
  async handleRideBatchCompleted(event: any): Promise<void> {
    try {
      await this.rabbitmqEventPublisher.publishWebSocketEvent(
        'websocket.batch.completed',
        event,
      );

      this.logger.log(
        `Successfully published batch completed event for ride ${event.rideId}, batch ${event.batchNumber}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to publish batch completed event for ride ${event.rideId}:`,
        error,
      );
    }
  }

  @OnEvent('rider.ride.cancelled')
  async handleRideCancelled(event: any): Promise<void> {
    try {
      if (event.driverId) {
        this.engagespotService.sendNotification(
          'ride_cancelled_by_rider',
          [{ identifier: event.driverId, userType: 'DRIVER' }],
          {
            reason: event.metadata?.reason,
            cancelledBy: 'RIDER',
            rideId: event.rideId,
            ...event,
          },
        );
      }
      this.engagespotService.sendNotification(
        'ride_cancelled_by_rider_to_rider',
        [{ identifier: event.riderId, userType: 'RIDER' }],
        {
          reason: event.metadata?.reason,
          rideId: event.rideId,
          cancelledBy: 'RIDER',
        },
      );
    } catch (error) {
      this.logger.error(
        `Failed to publish ride cancelled event for ride ${event.rideId}`,
      );
    }
  }

  @OnEvent('driver.ride.cancelled')
  async handleDriverRideCancelled(event: any): Promise<void> {
    try {
      this.engagespotService.sendNotification(
        'ride_cancelled_by_driver',
        [{ identifier: event.riderId, userType: 'RIDER' }],
        {
          reason: event.metadata?.reason,
          rideId: event.rideId,
          cancelledBy: 'DRIVER',
        },
      );
    } catch (error) {
      this.logger.error(
        `Failed to publish driver ride cancelled event for ride ${event.rideId}`,
        error,
      );
    }
  }

  @OnEvent('ride.completed')
  async handleRideCompleted(event: any): Promise<void> {
    try {
      this.logger.log('Notifer Service - Ride Completed Event', event);
      this.engagespotService.sendNotification(
        'ride_completed_workflow',
        [
          {
            identifier: event.riderId,
            userType: 'RIDER',
          },
        ],
        {
          rideId: event.rideId,
          fare: event.finalFareSpec?.passengerFare || 0,
          paymentMethod: 'CASH',
          ...event,
        },
      );
      if (event.bookFor == 'other' && event.riderId == event.createdBy) {
        this.logger.log(
          'Notifer Service - Ride Completed Event to Other',
          event,
        );
        this.engagespotService.sendNotification(
          'ride_completed_workflow_to_other',
          [
            {
              identifier: event.phoneNumber,
              userType: 'OTHER',
              phoneNumber: event.phoneNumber,
            },
          ],
          {
            rideId: event.rideId,
            fare: event.finalFareSpec?.passengerFare || 0,
            paymentMethod: 'CASH',
            ...event,
          },
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to publish ride completed event for ride ${event.rideId}`,
        error,
      );
    }
  }

  @OnEvent('ride.started')
  async handleRideStarted(event: any): Promise<void> {
    try {
      this.engagespotService.sendNotification(
        'ride_started_workflow',
        [{ identifier: event.riderId, userType: 'RIDER' }],
        {
          rideId: event.rideId,
          ...event,
        },
      );
    } catch (error) {
      this.logger.error(
        `Failed to publish ride started event for ride ${event.rideId}`,
        error,
      );
    }
  }

  @OnEvent('ride.destination.reached')
  async handleRideDestinationReached(_event: any): Promise<void> {
    // const { rideId, matchedStop, ride } = event;
    // this.logger.log('Notifier Service - Ride Destination Reached Event', event);
    //     await this.engagespotService.sendNotification(
    //     'destination_reached_workflow',
    //     [{ identifier: ride.riderId, userType: 'RIDER' }],
    //     {
    //       rideId,
    //       stopAddress: matchedStop.address || 'Unknown address',
    //       reachedAt: new Date().toISOString(),
    //       ...event,
    //     },
    //   );
    //TODO: Implement notification for destination reached
  }

  @OnEvent('ride.accepted')
  async handleRideAccepted(event: any): Promise<void> {
    try {
      console.log('Notifier Service - Ride Accepted Event other', event);
      if (event.bookFor == 'other' && event.riderId == event.createdBy) {
        this.logger.log(
          'Notifer Service - Ride Accepted Event to Other',
          event,
        );
        this.engagespotService.sendNotification(
          'ride_accepted_workflow_other',
          [
            {
              identifier: event.phoneNumber,
              userType: 'OTHER',
              phoneNumber: event.phoneNumber,
            },
          ],
          {
            rideId: event.rideId,
            fare: event.finalFareSpec?.passengerFare || 0,
            paymentMethod: 'CASH',
            ...event,
          },
        );
      }

      this.logger.log('Notifier Service - Ride Accepted Event', event);
    } catch (error) {
      this.logger.error(
        `Failed to publish ride accepted event for ride ${event.rideId}`,
        error,
      );
    }
  }
  @OnEvent('ride.requested_rider')
  async handleRideRequested(event: any): Promise<void> {
    try {
      this.engagespotService.sendNotification(
        'ride_requested_workflow_rider',
        [
          {
            identifier: event.riderId,
            userType: 'RIDER',
          },
        ],
        {
          ...event,
        },
      );
    } catch (error) {
      this.logger.error(
        `Failed to process ride requested event for ride ${event.rideId}`,
        error,
      );
    }
  }
}

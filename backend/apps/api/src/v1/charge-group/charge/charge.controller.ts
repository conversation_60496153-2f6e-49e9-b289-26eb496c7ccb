import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { ChargeService } from '@shared/shared/modules/charge/charge.service';
import { UpdateChargeDto } from './dto/update-charge.dto';
import { ChargeResponseDto } from './dto/charge-response.dto';
import { CreateStandaloneChargeDto } from './dto/create-standalone-charge.dto';
import { ChargePaginationDto } from './dto/charge-pagination.dto';
import { ChargePaginatedResponseDto } from './dto/charge-paginated-response.dto';
import { AttachCommissionDto } from './dto/attach-commission.dto';
import { DetachCommissionDto } from './dto/detach-commission.dto';
import { AttachTaxGroupDto } from './dto/attach-tax-group.dto';
import { DetachTaxGroupDto } from './dto/detach-tax-group.dto';
import {
  ApiResponseDto,
  ApiErrorResponseDto,
} from '../../../docs/swagger/common-responses.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';

@ApiTags('Charges')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('charges')
export class ChargeController {
  constructor(private readonly chargeService: ChargeService) {}

  @Post()
  @ApiOperation({
    summary: 'Create standalone charge',
    description: 'Create a new charge that is not attached to any charge group',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Charge created successfully',
    type: ApiResponseDto<ChargeResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data or percentage-based charge attempted',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Charge with same identifier already exists',
    type: ApiErrorResponseDto,
  })
  async createStandalone(
    @Body() createStandaloneChargeDto: CreateStandaloneChargeDto,
  ): Promise<ApiResponseDto<ChargeResponseDto>> {
    const charge = await this.chargeService.createStandaloneCharge(
      createStandaloneChargeDto,
    );
    return {
      success: true,
      message: 'Charge created successfully',
      data: charge as ChargeResponseDto,
      timestamp: Date.now(),
    };
  }

  @Get()
  @ApiOperation({
    summary: 'Get paginated charges',
    description:
      'Retrieve charges with pagination, search, and filtering capabilities',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Charges retrieved successfully',
    type: ChargePaginatedResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Charge group not found (when filtering by chargeGroupId)',
    type: ApiErrorResponseDto,
  })
  async findAll(
    @Query() paginationDto: ChargePaginationDto,
  ): Promise<ChargePaginatedResponseDto> {
    const {
      search,
      chargeType,
      priceModel,
      minAmount,
      maxAmount,
      createdFrom,
      createdTo,
      chargeGroupId,
      includeUnattached,
      isCommon,
      taxGroupId,
      page = 1,
      limit = 10,
    } = paginationDto;

    const filters = {
      ...(search && { search: search.trim() }),
      ...(chargeType && { chargeType }),
      ...(priceModel && { priceModel }),
      ...(minAmount !== undefined && { minAmount }),
      ...(maxAmount !== undefined && { maxAmount }),
      ...(createdFrom && { createdFrom }),
      ...(createdTo && { createdTo }),
      ...(chargeGroupId && { chargeGroupId }),
      ...(includeUnattached !== undefined && { includeUnattached }),
      ...(isCommon !== undefined && { isCommon }),
      ...(taxGroupId && { taxGroupId }),
    };

    const result = await this.chargeService.findPaginatedCharges(
      page,
      limit,
      filters,
    );

    return {
      success: true,
      message: 'Charges retrieved successfully',
      data: {
        ...result,
        data: result.data as ChargeResponseDto[],
      },
      timestamp: Date.now(),
    };
  }

  @Get('charge-group/:chargeGroupId/:chargeId')
  @ApiOperation({
    summary: 'Get charge by ID within charge group',
    description: 'Retrieve a specific charge by its ID within a charge group',
  })
  @ApiParam({
    name: 'chargeGroupId',
    description: 'ID of the charge group',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiParam({
    name: 'chargeId',
    description: 'ID of the charge',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Charge retrieved successfully',
    type: ApiResponseDto<ChargeResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Charge or charge group not found',
    type: ApiErrorResponseDto,
  })
  async findOne(
    @Param('chargeId') chargeId: string,
  ): Promise<ApiResponseDto<ChargeResponseDto>> {
    const charge = await this.chargeService.findChargeById(chargeId);
    return {
      success: true,
      message: 'Charge retrieved successfully',
      data: charge as ChargeResponseDto,
      timestamp: Date.now(),
    };
  }

  @Get(':chargeId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get charge by ID',
    description: 'Retrieve a specific charge by its ID',
  })
  @ApiParam({
    name: 'chargeId',
    description: 'ID of the charge',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Charge retrieved successfully',
    type: ApiResponseDto<ChargeResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Charge not found',
    type: ApiErrorResponseDto,
  })
  async findById(
    @Param('chargeId') chargeId: string,
  ): Promise<ApiResponseDto<ChargeResponseDto>> {
    const charge = await this.chargeService.findChargeById(chargeId);
    return {
      success: true,
      message: 'Charge retrieved successfully',
      data: charge as ChargeResponseDto,
      timestamp: Date.now(),
    };
  }

  @Put(':chargeId')
  @ApiOperation({
    summary: 'Update charge',
    description: 'Update a specific charge within a charge group',
  })
  @ApiParam({
    name: 'chargeGroupId',
    description: 'ID of the charge group',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiParam({
    name: 'chargeId',
    description: 'ID of the charge',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Charge updated successfully',
    type: ApiResponseDto<ChargeResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Charge or charge group not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Charge with same name already exists',
    type: ApiErrorResponseDto,
  })
  async update(
    @Param('chargeId') chargeId: string,
    @Body() updateChargeDto: UpdateChargeDto,
  ): Promise<ApiResponseDto<ChargeResponseDto>> {
    const charge = await this.chargeService.updateChargeById(
      chargeId,
      updateChargeDto,
    );
    return {
      success: true,
      message: 'Charge updated successfully',
      data: charge as ChargeResponseDto,
      timestamp: Date.now(),
    };
  }

  @Put(':chargeId')
  @ApiOperation({
    summary: 'Update charge by ID only',
    description: 'Update a charge by its ID (across all charge groups)',
  })
  @ApiParam({
    name: 'chargeId',
    description: 'ID of the charge',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Charge updated successfully',
    type: ApiResponseDto<ChargeResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Charge not found',
    type: ApiErrorResponseDto,
  })
  async updateById(
    @Param('chargeId') chargeId: string,
    @Body() updateChargeDto: UpdateChargeDto,
  ): Promise<ApiResponseDto<ChargeResponseDto>> {
    const charge = await this.chargeService.updateChargeById(
      chargeId,
      updateChargeDto,
    );
    return {
      success: true,
      message: 'Charge updated successfully',
      data: charge as ChargeResponseDto,
      timestamp: Date.now(),
    };
  }

  @Delete(':chargeId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete charge by ID only',
    description: 'Delete a charge by its ID (across all charge groups)',
  })
  @ApiParam({
    name: 'chargeId',
    description: 'ID of the charge',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Charge deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Charge not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Charge is referenced by other charges',
    type: ApiErrorResponseDto,
  })
  async removeById(@Param('chargeId') chargeId: string): Promise<void> {
    await this.chargeService.deleteChargeById(chargeId);
  }

  @Post('attach-commission')
  @ApiOperation({
    summary: 'Attach commission to charge',
    description: 'Attach a commission to a specific charge',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Commission attached successfully',
    type: ApiResponseDto<ChargeResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Charge or commission not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Charge already has a commission attached',
    type: ApiErrorResponseDto,
  })
  async attachCommission(
    @Body() attachCommissionDto: AttachCommissionDto,
  ): Promise<ApiResponseDto<ChargeResponseDto>> {
    const charge = await this.chargeService.attachCommissionToCharge(
      attachCommissionDto.chargeId,
      attachCommissionDto.commissionId,
    );
    return {
      success: true,
      message: 'Commission attached successfully',
      data: charge as ChargeResponseDto,
      timestamp: Date.now(),
    };
  }

  @Post('detach-commission')
  @ApiOperation({
    summary: 'Detach commission from charge',
    description: 'Detach a commission from a specific charge',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Commission detached successfully',
    type: ApiResponseDto<ChargeResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Charge not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Charge does not have a commission attached',
    type: ApiErrorResponseDto,
  })
  async detachCommission(
    @Body() detachCommissionDto: DetachCommissionDto,
  ): Promise<ApiResponseDto<ChargeResponseDto>> {
    const charge = await this.chargeService.detachCommissionFromCharge(
      detachCommissionDto.chargeId,
    );
    return {
      success: true,
      message: 'Commission detached successfully',
      data: charge as ChargeResponseDto,
      timestamp: Date.now(),
    };
  }

  @Post('attach-taxGroup')
  @ApiOperation({
    summary: 'Attach tax group to charge',
    description: 'Attach a tax group to a specific charge',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tax group attached successfully',
    type: ApiResponseDto<ChargeResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Charge or tax group not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Charge already has a tax group attached',
    type: ApiErrorResponseDto,
  })
  async attachTaxGroup(
    @Body() attachTaxGroupDto: AttachTaxGroupDto,
  ): Promise<ApiResponseDto<ChargeResponseDto>> {
    const charge = await this.chargeService.attachTaxGroupToCharge(
      attachTaxGroupDto.chargeId,
      attachTaxGroupDto.taxGroupId,
    );
    return {
      success: true,
      message: 'Tax group attached successfully',
      data: charge as ChargeResponseDto,
      timestamp: Date.now(),
    };
  }

  @Post('detach-taxGroup')
  @ApiOperation({
    summary: 'Detach tax group from charge',
    description: 'Detach a tax group from a specific charge',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tax group detached successfully',
    type: ApiResponseDto<ChargeResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Charge not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Charge does not have a tax group attached',
    type: ApiErrorResponseDto,
  })
  async detachTaxGroup(
    @Body() detachTaxGroupDto: DetachTaxGroupDto,
  ): Promise<ApiResponseDto<ChargeResponseDto>> {
    const charge = await this.chargeService.detachTaxGroupFromCharge(
      detachTaxGroupDto.chargeId,
    );
    return {
      success: true,
      message: 'Tax group detached successfully',
      data: charge as ChargeResponseDto,
      timestamp: Date.now(),
    };
  }
}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class RiderContactResponseDto {
  @ApiProperty({
    description: 'Contact ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id!: string;

  @ApiProperty({
    description: 'Rider ID who owns this contact',
    example: '123e4567-e89b-12d3-a456-426614174001',
  })
  riderId!: string;

  @ApiPropertyOptional({
    description: 'Contact name',
    example: '<PERSON>',
  })
  name?: string | null;

  @ApiPropertyOptional({
    description: 'Contact phone number',
    example: '+1234567890',
  })
  phoneNumber?: string | null;

  @ApiPropertyOptional({
    description: 'Contact email address',
    example: '<EMAIL>',
  })
  email?: string | null;

  @ApiProperty({
    description: 'Contact creation timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  createdAt!: Date;

  @ApiProperty({
    description: 'Contact last update timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  updatedAt!: Date;

  @ApiPropertyOptional({
    description: 'Contact deletion timestamp (null if not deleted)',
    example: null,
  })
  deletedAt?: Date | null;
}

export class RiderContactListDataDto {
  @ApiProperty({
    description: 'Array of rider contacts',
    type: [RiderContactResponseDto],
  })
  data!: RiderContactResponseDto[];

  @ApiProperty({
    description: 'Total number of contacts',
    example: 25,
  })
  total!: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page!: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit!: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 3,
  })
  totalPages!: number;

  @ApiProperty({
    description: 'Whether there is a next page',
    example: true,
  })
  hasNext!: boolean;

  @ApiProperty({
    description: 'Whether there is a previous page',
    example: false,
  })
  hasPrev!: boolean;
}

export class CreateRiderContactResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Contact created successfully' })
  message!: string;

  @ApiProperty({ type: RiderContactResponseDto })
  data!: RiderContactResponseDto;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}

export class RiderContactDetailsResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Contact retrieved successfully' })
  message!: string;

  @ApiProperty({ type: RiderContactResponseDto })
  data!: RiderContactResponseDto;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}

export class UpdateRiderContactResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Contact updated successfully' })
  message!: string;

  @ApiProperty({ type: RiderContactResponseDto })
  data!: RiderContactResponseDto;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}

export class DeleteRiderContactResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Contact deleted successfully' })
  message!: string;

  @ApiProperty({ type: RiderContactResponseDto })
  data!: RiderContactResponseDto;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}

export class RiderContactListResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Contacts retrieved successfully' })
  message!: string;

  @ApiProperty({ type: RiderContactListDataDto })
  data!: RiderContactListDataDto;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}

import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEmail,
  IsNotEmpty,
  ValidateIf,
} from 'class-validator';

export class UpdateRiderContactDto {
  @ApiPropertyOptional({
    description: 'Contact name',
    example: '<PERSON>',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  name?: string;

  @ApiPropertyOptional({
    description: 'Contact phone number',
    example: '+1234567890',
    maxLength: 20,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  phoneNumber?: string;

  @ApiPropertyOptional({
    description: 'Contact email address',
    example: '<EMAIL>',
    maxLength: 255,
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  // Custom validation to ensure at least one field is provided for update
  @ValidateIf((o) => !o.name && !o.phoneNumber && !o.email)
  @IsNotEmpty({ message: 'At least one field must be provided for update' })
  _atLeastOneField?: any;
}

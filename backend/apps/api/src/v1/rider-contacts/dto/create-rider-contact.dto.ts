import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEmail,
  IsNotEmpty,
  ValidateIf,
} from 'class-validator';

export class CreateRiderContactDto {
  @ApiPropertyOptional({
    description: 'Contact name',
    example: '<PERSON>',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  name?: string;

  @ApiPropertyOptional({
    description: 'Contact phone number',
    example: '+1234567890',
    maxLength: 20,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  phoneNumber?: string;

  @ApiPropertyOptional({
    description: 'Contact email address',
    example: '<EMAIL>',
    maxLength: 255,
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  // Custom validation to ensure at least one field is provided
  @ValidateIf((o) => !o.name && !o.phoneNumber && !o.email)
  @IsNotEmpty({
    message:
      'At least one contact field (name, phoneNumber, or email) must be provided',
  })
  _atLeastOneField?: any;
}

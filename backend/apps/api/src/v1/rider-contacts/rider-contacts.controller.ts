import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  UseGuards,
  Req,
  UnauthorizedException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { Request } from 'express';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import { RiderContactService } from '@shared/shared/modules/rider-contact/rider-contact.service';
import { ApiErrorResponseDto } from '../../docs/swagger/common-responses.dto';
import {
  CreateRiderContactDto,
  UpdateRiderContactDto,
  RiderContactPaginationDto,
  CreateRiderContactResponseDto,
  RiderContactDetailsResponseDto,
  UpdateRiderContactResponseDto,
  DeleteRiderContactResponseDto,
  RiderContactListResponseDto,
} from './dto';

@ApiTags('Rider Contacts')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('rider-contacts')
export class RiderContactsController {
  constructor(private readonly riderContactService: RiderContactService) {}

  /**
   * Create a new rider contact
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new contact',
    description: `Creates a new contact for the authenticated rider.
    
    **Features:**
    - Only authenticated riders can create contacts
    - At least one contact field (name, phoneNumber, or email) must be provided
    - Contacts are automatically associated with the authenticated rider
    - Supports partial contact information (name only, phone only, etc.)
    
    **Validation:**
    - Email format validation if provided
    - Phone number format validation if provided
    - Name length validation if provided`,
  })
  @ApiBody({
    type: CreateRiderContactDto,
    description: 'Contact creation data',
  })
  @ApiResponse({
    status: 201,
    description: 'Contact created successfully',
    type: CreateRiderContactResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation failed',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid or missing token',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - not a rider',
    type: ApiErrorResponseDto,
  })
  async createContact(
    @Body() createDto: CreateRiderContactDto,
    @Req() req: Request,
  ): Promise<CreateRiderContactResponseDto> {
    // Get authenticated user ID
    const userId = (req.user as any)?.userId || (req.user as any)?.sub;
    if (!userId) {
      throw new UnauthorizedException('User ID not found in token');
    }

    // Get rider profile ID
    const riderId = (req.user as any)?.profileId;
    if (!riderId) {
      throw new UnauthorizedException('Rider profile ID not found in token');
    }

    const contact = await this.riderContactService.createContact(
      {
        riderId,
        name: createDto.name || null,
        phoneNumber: createDto.phoneNumber || null,
        email: createDto.email || null,
      },
      riderId,
    );

    return {
      success: true,
      message: 'Contact created successfully',
      data: contact as any,
      timestamp: Date.now(),
    };
  }

  /**
   * Get paginated list of contacts for authenticated rider
   */
  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get rider contacts',
    description: `Retrieves a paginated list of contacts for the authenticated rider.
    
    **Features:**
    - Pagination support with configurable page size
    - Search functionality across name, phone number, and email
    - Sorted by creation date (newest first)
    - Only returns contacts belonging to the authenticated rider
    
    **Query Parameters:**
    - page: Page number (default: 1)
    - limit: Items per page (default: 10, max: 100)
    - search: Search term to filter contacts`,
  })
  @ApiResponse({
    status: 200,
    description: 'Contacts retrieved successfully',
    type: RiderContactListResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid or missing token',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - not a rider',
    type: ApiErrorResponseDto,
  })
  async getContacts(
    @Query() query: RiderContactPaginationDto,
    @Req() req: Request,
  ): Promise<any> {
    // Get rider profile ID
    const riderId = (req.user as any)?.profileId;
    if (!riderId) {
      throw new UnauthorizedException('Rider profile ID not found in token');
    }

    const result = await this.riderContactService.getContactsList(
      riderId,
      query,
    );

    return {
      success: true,
      message: 'Contacts retrieved successfully',
      data: result.data,
      meta: {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNext: result.hasNext,
        hasPrev: result.hasPrev,
      },
      timestamp: Date.now(),
    };
  }

  /**
   * Get contact by ID
   */
  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get contact by ID',
    description:
      'Retrieves a specific contact by its ID. Only the owner can access their contacts.',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'Contact UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Contact retrieved successfully',
    type: RiderContactDetailsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid or missing token',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - not a rider',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Contact not found',
    type: ApiErrorResponseDto,
  })
  async getContactById(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: Request,
  ): Promise<RiderContactDetailsResponseDto> {
    // Get rider profile ID
    const riderId = (req.user as any)?.profileId;
    if (!riderId) {
      throw new UnauthorizedException('Rider profile ID not found in token');
    }

    const contact = await this.riderContactService.getContactById(id, riderId);

    return {
      success: true,
      message: 'Contact retrieved successfully',
      data: contact as any,
      timestamp: Date.now(),
    };
  }

  /**
   * Update contact by ID
   */
  @Put(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update contact',
    description: `Updates an existing contact with the provided data.

    **Features:**
    - Only the contact owner can update their contacts
    - Partial updates supported (update only specific fields)
    - At least one field must be provided for update
    - Email format validation if provided

    **Authorization:**
    - Contact must belong to the authenticated rider
    - Returns 404 if contact doesn't exist or doesn't belong to rider`,
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'Contact UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({
    type: UpdateRiderContactDto,
    description: 'Contact update data',
  })
  @ApiResponse({
    status: 200,
    description: 'Contact updated successfully',
    type: UpdateRiderContactResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation failed',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid or missing token',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - not a rider',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Contact not found',
    type: ApiErrorResponseDto,
  })
  async updateContact(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateRiderContactDto,
    @Req() req: Request,
  ): Promise<UpdateRiderContactResponseDto> {
    // Get rider profile ID
    const riderId = (req.user as any)?.profileId;
    if (!riderId) {
      throw new UnauthorizedException('Rider profile ID not found in token');
    }

    const contact = await this.riderContactService.updateContact(
      id,
      updateDto,
      riderId,
    );

    return {
      success: true,
      message: 'Contact updated successfully',
      data: contact as any,
      timestamp: Date.now(),
    };
  }

  /**
   * Delete contact by ID (soft delete)
   */
  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Delete contact',
    description: `Soft deletes a contact by marking it as deleted.

    **Features:**
    - Soft delete (contact is marked as deleted, not permanently removed)
    - Only the contact owner can delete their contacts
    - Returns the deleted contact data

    **Authorization:**
    - Contact must belong to the authenticated rider
    - Returns 404 if contact doesn't exist or doesn't belong to rider`,
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'Contact UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Contact deleted successfully',
    type: DeleteRiderContactResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid or missing token',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - not a rider',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Contact not found',
    type: ApiErrorResponseDto,
  })
  async deleteContact(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: Request,
  ): Promise<DeleteRiderContactResponseDto> {
    // Get rider profile ID
    const riderId = (req.user as any)?.profileId;
    if (!riderId) {
      throw new UnauthorizedException('Rider profile ID not found in token');
    }

    const contact = await this.riderContactService.deleteContact(id, riderId);

    return {
      success: true,
      message: 'Contact deleted successfully',
      data: contact as any,
      timestamp: Date.now(),
    };
  }
}

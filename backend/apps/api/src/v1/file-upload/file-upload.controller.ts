import {
  Controller,
  Post,
  Delete,
  Get,
  UploadedFile,
  UseInterceptors,
  Body,
  Query,
  HttpCode,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { FileUploadService } from '@shared/shared/common/file-upload/aws-s3/aws-file-upload.servie';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';

interface FileDetailsDto {
  key: string;
  url: string;
  contentType: string;
  size: number;
}

@ApiTags('File Upload')
@Controller('file-upload')
export class FileUploadController {
  constructor(private readonly fileUploadService: FileUploadService) {}

  @Post('upload')
  @ApiOperation({ summary: 'Upload a file to S3' })
  @ApiConsumes('multipart/form-data')
  @ApiQuery({
    name: 'isPublic',
    type: 'boolean',
    required: false,
    example: false,
    description:
      'Whether the file should be uploaded to a public bucket (returns static URL) or private bucket (returns signed URL)',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'File uploaded',
    schema: {
      example: {
        success: true,
        message: 'File uploaded',
        data: {
          key: 'uploads/123-file.png',
          url: 'https://...',
          contentType: 'image/png',
          size: 12345,
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  @HttpCode(HttpStatus.CREATED)
  async upload(
    @UploadedFile() file: Express.Multer.File,
    @Query('isPublic') isPublic?: string,
  ): Promise<{ success: boolean; message: string; data: FileDetailsDto }> {
    if (!file) throw new BadRequestException('No file uploaded');
    const isPublicFlag = isPublic === 'true';
    const result = await this.fileUploadService.uploadFile(file, isPublicFlag);
    return { success: true, message: 'File uploaded', data: result };
  }

  @Delete('delete')
  @ApiOperation({ summary: 'Delete a file from S3 by key' })
  @ApiQuery({
    name: 'isPublic',
    type: 'boolean',
    required: false,
    example: false,
    description: 'Whether the file is in the public bucket or private bucket',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: { key: { type: 'string', example: 'uploads/123-file.png' } },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'File deleted',
    schema: { example: { success: true, message: 'File deleted' } },
  })
  @HttpCode(HttpStatus.OK)
  async deleteFile(
    @Body('key') key: string,
    @Query('isPublic') isPublic?: string,
  ): Promise<{ success: boolean; message: string }> {
    if (!key) throw new BadRequestException('File key is required');
    const isPublicFlag = isPublic === 'true';
    await this.fileUploadService.deleteFile(key, isPublicFlag);
    return { success: true, message: 'File deleted' };
  }

  @Get('signed-url')
  @ApiOperation({
    summary:
      'Get a signed URL for a file in S3 (or static URL for public files)',
  })
  @ApiQuery({
    name: 'key',
    type: 'string',
    required: true,
    example: 'uploads/123-file.png',
  })
  @ApiQuery({
    name: 'expiresIn',
    type: 'number',
    required: false,
    example: 3600,
  })
  @ApiQuery({
    name: 'isPublic',
    type: 'boolean',
    required: false,
    example: false,
    description:
      'Whether the file is in the public bucket (returns static URL) or private bucket (returns signed URL)',
  })
  @ApiResponse({
    status: 200,
    description: 'URL generated',
    schema: { example: { success: true, url: 'https://...' } },
  })
  @HttpCode(HttpStatus.OK)
  async getSignedUrl(
    @Query('key') key: string,
    @Query('expiresIn') expiresIn?: string,
    @Query('isPublic') isPublic?: string,
  ): Promise<{ success: boolean; url: string }> {
    if (!key) throw new BadRequestException('File key is required');
    const isPublicFlag = isPublic === 'true';
    const url = await this.fileUploadService.getSignedUrl(
      key,
      expiresIn ? parseInt(expiresIn, 10) : 3600,
      isPublicFlag,
    );
    return { success: true, url };
  }
}

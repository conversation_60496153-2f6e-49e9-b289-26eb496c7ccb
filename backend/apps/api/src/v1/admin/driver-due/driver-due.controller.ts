import {
  Controller,
  Post,
  Put,
  Get,
  Body,
  Param,
  HttpCode,
  HttpStatus,
  UseGuards,
  ParseUUIDPipe,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { DriverDueService } from '@shared/shared/modules/driver-due/driver-due-service';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import { ApiErrorResponseDto } from '../../../docs/swagger';
import {
  SetDriverDueLimitDto,
  UpdateDriverDueLimitDto,
  DriverDueConfigResponseDto,
  AdminDueLimitResponseDto,
} from './dto/driver-due-config.dto';

@ApiTags('Admin - Driver Due Management')
@Controller('admin/config/driver-due-limit')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class DriverDueController {
  constructor(private readonly driverDueService: DriverDueService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Set driver due limit',
    description:
      "Set or create a maximum due amount limit for drivers in a specific city. Once a driver's balance exceeds this limit (becomes more negative), they will be restricted from going online.",
  })
  @ApiResponse({
    status: 201,
    description: 'Due limit configured successfully',
    type: AdminDueLimitResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input data',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
    type: ApiErrorResponseDto,
  })
  async setDueLimit(
    @Body() dto: SetDriverDueLimitDto,
  ): Promise<AdminDueLimitResponseDto> {
    const config = await this.driverDueService.setDueLimit(dto.cityId, {
      maxDueLimit: dto.maxDueLimit,
    });

    const responseDto: DriverDueConfigResponseDto = {
      id: config.id,
      cityId: config.cityId,
      maxDueLimit: Number(config.maxDueLimit),
      isActive: config.isActive,
      createdAt: config.createdAt.toISOString(),
      updatedAt: config.updatedAt.toISOString(),
    };

    return {
      success: true,
      message: 'Due limit configured successfully',
      data: responseDto,
      timestamp: Date.now(),
    };
  }

  @Put(':cityId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update driver due limit',
    description:
      'Update an existing driver due limit configuration for a city.',
  })
  @ApiParam({
    name: 'cityId',
    description: 'City ID',
    example: 'city-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Due limit updated successfully',
    type: AdminDueLimitResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Due limit configuration not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
    type: ApiErrorResponseDto,
  })
  async updateDueLimit(
    @Param('cityId', new ParseUUIDPipe()) cityId: string,
    @Body() dto: UpdateDriverDueLimitDto,
  ): Promise<AdminDueLimitResponseDto> {
    const config = await this.driverDueService.updateDueLimit(cityId, dto);

    const responseDto: DriverDueConfigResponseDto = {
      id: config.id,
      cityId: config.cityId,
      maxDueLimit: Number(config.maxDueLimit),
      isActive: config.isActive,
      createdAt: config.createdAt.toISOString(),
      updatedAt: config.updatedAt.toISOString(),
    };

    return {
      success: true,
      message: 'Due limit updated successfully',
      data: responseDto,
      timestamp: Date.now(),
    };
  }

  @Get(':cityId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get driver due limit configuration',
    description:
      'Retrieve the current due limit configuration for a specific city.',
  })
  @ApiParam({
    name: 'cityId',
    description: 'City ID',
    example: 'city-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Due limit configuration retrieved successfully',
    type: AdminDueLimitResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Due limit configuration not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
    type: ApiErrorResponseDto,
  })
  async getDueLimit(
    @Param('cityId', new ParseUUIDPipe()) cityId: string,
  ): Promise<AdminDueLimitResponseDto> {
    const config = await this.driverDueService.getDueConfig(cityId);

    if (!config) {
      throw new NotFoundException(
        `Due limit configuration not found for city ${cityId}`,
      );
    }

    const responseDto: DriverDueConfigResponseDto = {
      id: config.id,
      cityId: config.cityId,
      maxDueLimit: Number(config.maxDueLimit),
      isActive: config.isActive,
      createdAt: config.createdAt.toISOString(),
      updatedAt: config.updatedAt.toISOString(),
    };

    return {
      success: true,
      message: 'Due limit configuration retrieved successfully',
      data: responseDto,
      timestamp: Date.now(),
    };
  }
}

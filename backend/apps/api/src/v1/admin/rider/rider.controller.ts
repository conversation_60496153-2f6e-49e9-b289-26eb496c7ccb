import {
  <PERSON>,
  Get,
  Patch,
  Param,
  Query,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  Headers,
  Req,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiBearerAuth,
  ApiHeader,
  ApiParam,
} from '@nestjs/swagger';
import { RiderProfileService } from '@shared/shared/modules/user-profile/services/rider-profile.service';
import { RiderFilterDto } from './dto/rider-filter.dto';
import {
  RiderListResponseDto,
  RiderDetailsResponseDto,
} from './dto/rider-response.dto';
import { UpdateRiderDto, UpdateRiderResponseDto } from './dto/update-rider.dto';
import { ApiErrorResponseDto } from '../../../docs/swagger/common-responses.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import { AuthRole } from '@shared/shared/common/constants/constants';

@ApiTags('Admin - Riders')
@Controller('admin/riders')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class RiderController {
  constructor(private readonly riderProfileService: RiderProfileService) {}

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get paginated list of riders',
    description:
      'Retrieve riders with pagination and optional filters including search by name, email, phone number',
  })
  @ApiResponse({
    status: 200,
    type: RiderListResponseDto,
    description: 'Riders retrieved successfully',
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 401, type: ApiErrorResponseDto })
  @ApiResponse({ status: 403, type: ApiErrorResponseDto })
  @ApiHeader({
    name: 'x-app-type',
    description: 'Application type',
    required: true,
    enum: [AuthRole.SUPER_ADMIN],
  })
  async listRiders(
    @Query() filters: RiderFilterDto,
    @Headers('x-app-type') _role: AuthRole,
    @Req() _request: any,
  ): Promise<RiderListResponseDto> {
    const result = await this.riderProfileService.listRiders({
      ...(filters.search && { search: filters.search }),
      ...(filters.name && { name: filters.name }),
      ...(filters.email && { email: filters.email }),
      ...(filters.phoneNumber && { phoneNumber: filters.phoneNumber }),
      ...(filters.cityId && { cityId: filters.cityId }),
      ...(filters.status && { status: filters.status }),
      ...(filters.page && { page: filters.page }),
      ...(filters.limit && { limit: filters.limit }),
      ...(filters.sortBy && { sortBy: filters.sortBy }),
      ...(filters.sortOrder && {
        sortOrder: filters.sortOrder.toUpperCase() as 'ASC' | 'DESC',
      }),
    });

    return {
      success: true,
      message: 'Riders retrieved successfully',
      data: result.data as any,
      meta: result.meta,
      timestamp: Date.now(),
    };
  }

  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get rider by ID',
    description: 'Retrieve a specific rider by their profile ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Rider profile ID',
    example: 'rider-uuid-123',
  })
  @ApiResponse({
    status: 200,
    type: RiderDetailsResponseDto,
    description: 'Rider details retrieved successfully',
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 401, type: ApiErrorResponseDto })
  @ApiResponse({ status: 403, type: ApiErrorResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  @ApiHeader({
    name: 'x-app-type',
    description: 'Application type',
    required: true,
    enum: [AuthRole.SUPER_ADMIN],
  })
  async getRiderById(
    @Param('id') riderId: string,
    @Headers('x-app-type') _role: AuthRole,
    @Req() _request: any,
  ): Promise<RiderDetailsResponseDto> {
    const rider = await this.riderProfileService.getRiderById(riderId);

    return {
      success: true,
      message: 'Rider details retrieved successfully',
      data: rider as any,
      timestamp: Date.now(),
    };
  }

  @Patch(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update rider profile',
    description:
      'Update rider profile information including name, email, phone number, and other details',
  })
  @ApiParam({
    name: 'id',
    description: 'Rider profile ID',
    example: 'rider-uuid-123',
  })
  @ApiResponse({
    status: 200,
    type: UpdateRiderResponseDto,
    description: 'Rider updated successfully',
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 401, type: ApiErrorResponseDto })
  @ApiResponse({ status: 403, type: ApiErrorResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  @ApiResponse({ status: 409, type: ApiErrorResponseDto })
  @ApiHeader({
    name: 'x-app-type',
    description: 'Application type',
    required: true,
    enum: [AuthRole.SUPER_ADMIN],
  })
  async updateRider(
    @Param('id') riderId: string,
    @Body() updateRiderDto: UpdateRiderDto,
    @Headers('x-app-type') _role: AuthRole,
    @Req() _request: any,
  ): Promise<UpdateRiderResponseDto> {
    const updateData: any = {};
    if (updateRiderDto.firstName !== undefined)
      updateData.firstName = updateRiderDto.firstName;
    if (updateRiderDto.lastName !== undefined)
      updateData.lastName = updateRiderDto.lastName;
    if (updateRiderDto.email !== undefined)
      updateData.email = updateRiderDto.email;
    if (updateRiderDto.phoneNumber !== undefined)
      updateData.phoneNumber = updateRiderDto.phoneNumber;
    if (updateRiderDto.gender !== undefined)
      updateData.gender = updateRiderDto.gender;
    if (updateRiderDto.dob !== undefined) updateData.dob = updateRiderDto.dob;
    if (updateRiderDto.cityId !== undefined)
      updateData.cityId = updateRiderDto.cityId;
    if (updateRiderDto.profilePictureUrl !== undefined)
      updateData.profilePictureUrl = updateRiderDto.profilePictureUrl;
    if (updateRiderDto.status !== undefined)
      updateData.status = updateRiderDto.status;

    const updatedRider = await this.riderProfileService.updateRider(
      riderId,
      updateData,
    );

    return {
      success: true,
      message: 'Rider updated successfully',
      data: updatedRider as any,
      timestamp: Date.now(),
    };
  }
}

import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsEmail,
  IsEnum,
  IsDateString,
  IsU<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>th,
  <PERSON><PERSON>ength,
  Matches,
} from 'class-validator';
import { UserProfileStatus } from '@shared/shared/repositories/models/userProfile.model';

export class UpdateRiderDto {
  @ApiProperty({
    description: 'First name of the rider',
    example: 'John',
    required: false,
    minLength: 2,
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @MinLength(2, { message: 'First name must be at least 2 characters long' })
  @MaxLength(50, { message: 'First name must not exceed 50 characters' })
  firstName?: string;

  @ApiProperty({
    description: 'Last name of the rider',
    example: 'Doe',
    required: false,
    minLength: 2,
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @MinLength(2, { message: 'Last name must be at least 2 characters long' })
  @MaxLength(50, { message: 'Last name must not exceed 50 characters' })
  lastName?: string;

  @ApiProperty({
    description: 'Email address of the rider',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail({}, { message: 'Please provide a valid email address' })
  email?: string;

  @ApiProperty({
    description: 'Phone number of the rider',
    example: '+919876543210',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Matches(/^\+[1-9]\d{1,14}$/, {
    message:
      'Phone number must be in international format (e.g., +919876543210)',
  })
  phoneNumber?: string;

  @ApiProperty({
    description: 'Gender of the rider',
    example: 'male',
    required: false,
    enum: ['male', 'female', 'other'],
  })
  @IsOptional()
  @IsString()
  @IsEnum(['male', 'female', 'other'], {
    message: 'Gender must be one of: male, female, other',
  })
  gender?: string;

  @ApiProperty({
    description: 'Date of birth in ISO format',
    example: '1990-01-15',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Date of birth must be a valid date' })
  dob?: string;

  @ApiProperty({
    description: 'City ID where the rider is located',
    example: 'city-uuid-123',
    required: false,
  })
  @IsOptional()
  @IsUUID(4, { message: 'City ID must be a valid UUID' })
  cityId?: string;

  @ApiProperty({
    description: 'Profile picture URL',
    example: 'https://example.com/profile.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  profilePictureUrl?: string;

  @ApiProperty({
    description: 'Rider status',
    enum: UserProfileStatus,
    example: UserProfileStatus.ACTIVE,
    required: false,
  })
  @IsOptional()
  @IsEnum(UserProfileStatus, {
    message: `Status must be one of: ${Object.values(UserProfileStatus).join(', ')}`,
  })
  status?: UserProfileStatus;
}

export class UpdateRiderResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Rider updated successfully',
  })
  message!: string;

  @ApiProperty({
    description: 'Updated rider details',
  })
  data!: any;

  @ApiProperty({
    description: 'Response timestamp',
    example: 1640995200000,
  })
  timestamp!: number;
}

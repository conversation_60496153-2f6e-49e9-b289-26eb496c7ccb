import { Test, TestingModule } from '@nestjs/testing';
import { RiderController } from '../rider.controller';
import { RiderProfileService } from '@shared/shared/modules/user-profile/services/rider-profile.service';
import { AuthRole } from '@shared/shared/common/constants/constants';
import { UserProfileStatus } from '@shared/shared/repositories/models/userProfile.model';

describe('RiderController', () => {
  let controller: RiderController;
  let riderProfileService: jest.Mocked<RiderProfileService>;

  const mockRiderData = {
    id: 'rider-123',
    userId: 'user-123',
    roleId: 'role-123',
    firstName: 'John',
    lastName: 'Doe',
    fullName: '<PERSON>',
    referralCode: 'REF123',
    status: UserProfileStatus.ACTIVE,
    createdAt: new Date(),
    updatedAt: new Date(),
    user: {
      id: 'user-123',
      phoneNumber: '+919876543210',
      email: '<EMAIL>',
      phoneVerifiedAt: new Date(),
      emailVerifiedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    city: {
      id: 'city-123',
      name: 'Mumbai',
      code: 'MUM',
    },
  };

  beforeEach(async () => {
    const mockRiderProfileService = {
      listRiders: jest.fn(),
      getRiderById: jest.fn(),
      updateRider: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [RiderController],
      providers: [
        {
          provide: RiderProfileService,
          useValue: mockRiderProfileService,
        },
      ],
    }).compile();

    controller = module.get<RiderController>(RiderController);
    riderProfileService = module.get(RiderProfileService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('listRiders', () => {
    it('should return paginated list of riders', async () => {
      const mockResult = {
        data: [mockRiderData],
        meta: {
          total: 1,
          page: 1,
          limit: 10,
          totalPages: 1,
        },
      };

      riderProfileService.listRiders.mockResolvedValue(mockResult);

      const filters = {
        page: 1,
        limit: 10,
        search: 'john',
      };

      const result = await controller.listRiders(
        filters as any,
        AuthRole.SUPER_ADMIN,
        {},
      );

      expect(result.success).toBe(true);
      expect(result.message).toBe('Riders retrieved successfully');
      expect(result.data).toEqual(mockResult.data);
      expect(result.meta).toEqual(mockResult.meta);
      expect(riderProfileService.listRiders).toHaveBeenCalledWith({
        search: 'john',
        page: 1,
        limit: 10,
      });
    });
  });

  describe('getRiderById', () => {
    it('should return rider details', async () => {
      riderProfileService.getRiderById.mockResolvedValue(mockRiderData as any);

      const result = await controller.getRiderById(
        'rider-123',
        AuthRole.SUPER_ADMIN,
        {},
      );

      expect(result.success).toBe(true);
      expect(result.message).toBe('Rider details retrieved successfully');
      expect(result.data).toEqual(mockRiderData);
      expect(riderProfileService.getRiderById).toHaveBeenCalledWith(
        'rider-123',
      );
    });
  });

  describe('updateRider', () => {
    it('should update rider and return updated data', async () => {
      const updateDto = {
        firstName: 'Jane',
        email: '<EMAIL>',
      };

      const updatedRider = {
        ...mockRiderData,
        firstName: 'Jane',
        fullName: 'Jane Doe',
        user: {
          ...mockRiderData.user,
          email: '<EMAIL>',
        },
      };

      riderProfileService.updateRider.mockResolvedValue(updatedRider as any);

      const result = await controller.updateRider(
        'rider-123',
        updateDto as any,
        AuthRole.SUPER_ADMIN,
        {},
      );

      expect(result.success).toBe(true);
      expect(result.message).toBe('Rider updated successfully');
      expect(result.data).toEqual(updatedRider);
      expect(riderProfileService.updateRider).toHaveBeenCalledWith(
        'rider-123',
        {
          firstName: 'Jane',
          email: '<EMAIL>',
        },
      );
    });
  });
});

import {
  Controller,
  Post,
  Get,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  Res,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { Response } from 'express';
import { ReportsService } from './reports.service';
import {
  GenerateReportDto,
  ReportResponseDto,
} from './dto/generate-report.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import { ApiErrorResponseDto } from '../../../docs/swagger/common-responses.dto';

@ApiTags('Admin Reports')
@Controller('admin/reports')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ReportsController {
  constructor(private readonly reportsService: ReportsService) {}

  /**
   * Generate rides report as CSV
   */
  @Post('rides/generate')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Generate rides report as CSV',
    description:
      'Generate a CSV report of rides with detailed fare, commission, and tax breakdown. Supports filtering by date range and city product.',
  })
  @ApiResponse({
    status: 200,
    description: 'CSV report generated successfully',
    type: ReportResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
    type: ApiErrorResponseDto,
  })
  async generateRidesReport(
    @Body() filters: GenerateReportDto,
    @Res() res: Response,
  ) {
    const report = await this.reportsService.generateRidesReport(filters);

    // Set response headers for CSV download
    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="${report.fileName}"`,
    );

    // Send CSV content
    res.send(report.content);
  }

  /**
   * Generate rides report as JSON (for API response)
   */
  @Post('rides/generate-json')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Generate rides report as JSON',
    description:
      'Generate a rides report in JSON format with CSV content as base64 string',
  })
  @ApiResponse({
    status: 200,
    description: 'Report generated successfully',
    type: ReportResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters',
    type: ApiErrorResponseDto,
  })
  async generateRidesReportJson(@Body() filters: GenerateReportDto) {
    const report = await this.reportsService.generateRidesReport(filters);

    // Convert CSV to base64
    const base64Content = Buffer.from(report.content).toString('base64');

    return {
      success: true,
      message: 'Report generated successfully',
      data: {
        fileName: report.fileName,
        content: base64Content,
        rowCount: report.rowCount,
      },
      timestamp: Date.now(),
    };
  }

  /**
   * Get report metadata (available filters, counts, etc.)
   */
  @Get('metadata')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get report metadata',
    description:
      'Get available filters and metadata for generating reports (cities, city products, etc.)',
  })
  @ApiResponse({
    status: 200,
    description: 'Metadata retrieved successfully',
  })
  async getReportMetadata() {
    const metadata = await this.reportsService.getReportMetadata();

    return {
      success: true,
      message: 'Metadata retrieved successfully',
      data: metadata,
      timestamp: Date.now(),
    };
  }
}

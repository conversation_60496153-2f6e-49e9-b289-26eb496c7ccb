import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsDateString } from 'class-validator';

export class GenerateReportDto {
  @ApiProperty({
    description: 'Start date for report (ISO 8601 format)',
    example: '2025-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  fromDate?: string;

  @ApiProperty({
    description: 'End date for report (ISO 8601 format)',
    example: '2025-01-31',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  toDate?: string;

  @ApiProperty({
    description: 'City Product ID to filter rides',
    example: '413ee80b-fc79-4017-9487-846f433c3690',
    required: false,
  })
  @IsOptional()
  @IsString()
  cityProductId?: string;

  @ApiProperty({
    description: 'Ride status to filter (e.g., completed, cancelled)',
    example: 'completed',
    required: false,
  })
  @IsOptional()
  @IsString()
  status?: string;
}

export class ReportResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Report generated successfully',
  })
  message!: string;

  @ApiProperty({
    description: 'CSV file content as base64 or download URL',
  })
  data!: {
    fileName: string;
    content?: string; // base64 encoded CSV
    downloadUrl?: string;
  };

  @ApiProperty({
    description: 'Timestamp of response',
  })
  timestamp!: number;
}

/**
 * CSV Column definitions for ride reports
 * Maps column names to their descriptions and data extraction logic
 */

export interface CSVColumn {
  name: string;
  description: string;
  extractor: (ride: any) => string | number | null;
}

export const CSV_COLUMNS: CSVColumn[] = [
  {
    name: 'Date',
    description: 'Trip Start Date',
    extractor: (ride) =>
      ride.createdAt
        ? new Date(ride.createdAt).toISOString().split('T')[0]
        : '',
  },
  {
    name: 'Trip ID',
    description: 'Unique identifier assigned to each trip',
    extractor: (ride) => ride.id || '',
  },
  {
    name: 'Rider ID',
    description: 'Unique ID of the rider who booked the trip',
    extractor: (ride) => ride.riderId || '',
  },
  {
    name: 'Rider Name',
    description: 'Full name of the rider',
    extractor: (ride) => {
      if (ride.rider?.firstName && ride.rider?.lastName) {
        return `${ride.rider.firstName} ${ride.rider.lastName}`;
      }
      return ride.rider?.firstName || '';
    },
  },
  {
    name: 'Driver ID',
    description: 'Unique ID of the driver assigned to the trip',
    extractor: (ride) => ride.driverId || '',
  },
  {
    name: 'Driver Name',
    description: 'Full name of the driver',
    extractor: (ride) => {
      if (ride.driver?.firstName && ride.driver?.lastName) {
        return `${ride.driver.firstName} ${ride.driver.lastName}`;
      }
      return ride.driver?.firstName || '';
    },
  },
  {
    name: 'City',
    description: 'City in which the trip took place',
    extractor: (ride) => ride.cityProduct?.city?.name || '',
  },
  {
    name: 'Product Type',
    description: 'Type of Tukxi product used (e.g., Tukxi Go, Tukxi XL)',
    extractor: (ride) => ride.product?.name || '',
  },
  {
    name: 'Pickup Location',
    description: 'Starting point of the trip',
    extractor: (ride) => {
      const location = ride.pickupLocation;
      if (location?.address) return location.address;
      if (location?.lat && location?.lng)
        return `${location.lat}, ${location.lng}`;
      return '';
    },
  },
  {
    name: 'Drop Location',
    description: 'Ending point of the trip',
    extractor: (ride) => {
      const location = ride.destinationLocation;
      if (location?.address) return location.address;
      if (location?.lat && location?.lng)
        return `${location.lat}, ${location.lng}`;
      return '';
    },
  },
  {
    name: 'Trip Distance (km)',
    description: 'Total distance travelled in kilometers',
    extractor: (ride) => (ride.distance ? ride.distance / 1000 : 0),
  },
  {
    name: 'Trip Duration (min)',
    description: 'Total duration of the trip in minutes',
    extractor: (ride) =>
      ride.actualDuration ? Math.round(ride.actualDuration / 60) : 0,
  },
  {
    name: 'Payment Method',
    description: 'Payment type for the ride (Cash / Online)',
    extractor: (ride) => ride.payment?.paymentType || '',
  },
  {
    name: 'Ride Status',
    description: 'Current status of the trip (Completed / Cancelled)',
    extractor: (ride) => ride.status || '',
  },
  {
    name: 'Total Charge',
    description: 'Sum of all charges',
    extractor: (ride) => ride.fareSpec?.subtotal || 0,
  },
  {
    name: 'Total Tax on Charge',
    description: 'Sum of all taxes on charges',
    extractor: (ride) => ride.fareSpec?.totalTaxes || 0,
  },
  {
    name: 'Total Fare (₹)',
    description: 'Total fare collected from the rider',
    extractor: (ride) => ride.fareSpec?.passengerFare || 0,
  },
  {
    name: 'Total Commission (₹)',
    description: 'Combined total of all commissions',
    extractor: (ride) => {
      const commissions = ride.fareSpec?.commissionBreakdown || [];
      return commissions.reduce(
        (sum: number, c: any) => sum + (c.netCommissionAmount || 0),
        0,
      );
    },
  },
  {
    name: 'Total Tax on Commission',
    description: 'Sum of all taxes on commission',
    extractor: (ride) => {
      const commissions = ride.fareSpec?.commissionBreakdown || [];
      return commissions.reduce(
        (sum: number, c: any) => sum + (c.commissionTaxAmount || 0),
        0,
      );
    },
  },
  {
    name: 'Net Driver Earnings (₹)',
    description: 'Driver earnings after all deductions',
    extractor: (ride) => ride.fareSpec?.driverEarnings || 0,
  },
  {
    name: 'Toll Fee (₹)',
    description: 'Toll or highway charge, if applicable (non-taxable)',
    extractor: (ride) => {
      const charges = ride.fareSpec?.chargeBreakdown || [];
      const tollCharge = charges.find((c: any) =>
        c.chargeName?.toLowerCase().includes('toll'),
      );
      return tollCharge?.baseAmount || 0;
    },
  },
];

/**
 * Get dynamic charge columns from fareSpec
 */
export function getDynamicChargeColumns(fareSpec: any): CSVColumn[] {
  const columns: CSVColumn[] = [];
  const charges = fareSpec?.chargeBreakdown || [];

  charges.forEach((charge: any, index: number) => {
    const chargeNum = index + 1;
    const chargeName = charge.chargeName || `Charge ${chargeNum}`;

    // Base charge amount
    columns.push({
      name: `${chargeName} (₹)`,
      description: `${chargeName} before tax`,
      extractor: () => charge.baseAmount || 0,
    });

    // CGST
    const cgst = charge.appliedTaxes?.[0]?.subcategoryResults?.find(
      (s: any) => s.subcategoryName === 'CGST',
    );
    columns.push({
      name: `${chargeName} CGST (₹)`,
      description: `Central GST applied to ${chargeName}`,
      extractor: () => cgst?.taxAmount || 0,
    });

    // SGST
    const sgst = charge.appliedTaxes?.[0]?.subcategoryResults?.find(
      (s: any) => s.subcategoryName === 'SGST',
    );
    columns.push({
      name: `${chargeName} SGST (₹)`,
      description: `State GST applied to ${chargeName}`,
      extractor: () => sgst?.taxAmount || 0,
    });

    // Total tax
    columns.push({
      name: `Total Tax on ${chargeName} (₹)`,
      description: `${chargeName} CGST + ${chargeName} SGST`,
      extractor: () => (cgst?.taxAmount || 0) + (sgst?.taxAmount || 0),
    });
  });

  return columns;
}

/**
 * Get dynamic commission columns from fareSpec
 */
export function getDynamicCommissionColumns(fareSpec: any): CSVColumn[] {
  const columns: CSVColumn[] = [];
  const commissions = fareSpec?.commissionBreakdown || [];

  commissions.forEach((commission: any, index: number) => {
    const commNum = index + 1;
    const commName = commission.commissionName || `Commission ${commNum}`;

    // Base commission
    columns.push({
      name: `${commName} (₹)`,
      description: `${commName} before tax`,
      extractor: () => commission.netCommissionAmount || 0,
    });

    // CGST
    const cgst = commission.taxCalculation?.subcategoryResults?.find(
      (s: any) => s.subcategoryName === 'CGST',
    );
    columns.push({
      name: `${commName} CGST (₹)`,
      description: `Central GST applied to ${commName}`,
      extractor: () => cgst?.taxAmount || 0,
    });

    // SGST
    const sgst = commission.taxCalculation?.subcategoryResults?.find(
      (s: any) => s.subcategoryName === 'SGST',
    );
    columns.push({
      name: `${commName} SGST (₹)`,
      description: `State GST applied to ${commName}`,
      extractor: () => sgst?.taxAmount || 0,
    });

    // Total tax
    columns.push({
      name: `Total Tax on ${commName} (₹)`,
      description: `${commName} CGST + ${commName} SGST`,
      extractor: () => (cgst?.taxAmount || 0) + (sgst?.taxAmount || 0),
    });
  });

  return columns;
}

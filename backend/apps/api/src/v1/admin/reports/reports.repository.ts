import { Injectable } from '@nestjs/common';
import { PrismaService } from '@shared/shared/database/prisma/prisma.service';

export interface ReportFilters {
  fromDate?: Date;
  toDate?: Date;
  cityProductId?: string;
  status?: string;
}

@Injectable()
export class ReportsRepository {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Get rides for report generation with all necessary relations
   */
  async getRidesForReport(filters: ReportFilters) {
    const whereClause: any = {};

    // Date range filter
    if (filters.fromDate || filters.toDate) {
      whereClause.createdAt = {};
      if (filters.fromDate) {
        whereClause.createdAt.gte = filters.fromDate;
      }
      if (filters.toDate) {
        whereClause.createdAt.lte = filters.toDate;
      }
    }

    // City Product filter
    if (filters.cityProductId) {
      whereClause.cityProductId = filters.cityProductId;
    }

    // Status filter
    if (filters.status) {
      whereClause.status = filters.status;
    }

    // Only include completed or cancelled rides for reports
    if (!filters.status) {
      whereClause.status = {
        in: ['trip_completed', 'cancelled'],
      };
    }

    return this.prisma.ride.findMany({
      where: whereClause,
      include: {
        rider: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
        driver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
        product: {
          select: {
            id: true,
            name: true,
          },
        },
        cityProduct: {
          select: {
            id: true,
            city: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        payment: {
          select: {
            id: true,
            paymentType: true,
            amount: true,
          },
        },
        driverVehicle: {
          select: {
            id: true,
            vehicleNumber: true,
            vehicleType: {
              select: {
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  /**
   * Get rides count for report
   */
  async getRidesCountForReport(filters: ReportFilters): Promise<number> {
    const whereClause: any = {};

    if (filters.fromDate || filters.toDate) {
      whereClause.createdAt = {};
      if (filters.fromDate) {
        whereClause.createdAt.gte = filters.fromDate;
      }
      if (filters.toDate) {
        whereClause.createdAt.lte = filters.toDate;
      }
    }

    if (filters.cityProductId) {
      whereClause.cityProductId = filters.cityProductId;
    }

    if (filters.status) {
      whereClause.status = filters.status;
    } else {
      whereClause.status = {
        in: ['trip_completed', 'cancelled'],
      };
    }

    return this.prisma.ride.count({
      where: whereClause,
    });
  }

  /**
   * Get available cities for filtering
   */
  async getAvailableCities() {
    return this.prisma.city.findMany({
      select: {
        id: true,
        name: true,
      },
      orderBy: {
        name: 'asc',
      },
    });
  }

  /**
   * Get available city products for filtering
   */
  async getAvailableCityProducts() {
    return this.prisma.cityProduct.findMany({
      select: {
        id: true,
        city: {
          select: {
            name: true,
          },
        },
        product: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }
}

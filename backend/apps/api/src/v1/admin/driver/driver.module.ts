import { Modu<PERSON> } from '@nestjs/common';
import { DriverController } from './driver.controller';
import { UserProfileModule } from '@shared/shared/modules/user-profile/user-profile.module';
import { AuthModule } from '@shared/shared/modules/auth/auth.module';
import { LocationIngestorModule } from '@shared/shared/modules/location/location-injestor.module';
import { RealtimeModule } from '../../realtime/realtime.module';

@Module({
  imports: [
    AuthModule,
    UserProfileModule,
    LocationIngestorModule,
    RealtimeModule,
  ],
  controllers: [DriverController],
  providers: [],
  exports: [],
})
export class ApiDriverModule {}

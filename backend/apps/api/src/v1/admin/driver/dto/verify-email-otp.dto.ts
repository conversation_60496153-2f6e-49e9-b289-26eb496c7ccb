import { ApiProperty } from '@nestjs/swagger';
import { IsE<PERSON>, IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class VerifyEmailOtpDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'User profile ID',
  })
  @IsUUID()
  @IsNotEmpty()
  userProfileId!: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address being verified',
  })
  @IsEmail()
  @IsNotEmpty()
  email!: string;

  @ApiProperty({
    example: '1234',
    description: 'OTP code received via email',
  })
  @IsString()
  @IsNotEmpty()
  otp!: string;
}

import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUUID, IsEnum } from 'class-validator';
import { PaginationDto } from '../../../../common/dto/pagination.dto';
import { DriverStatus } from './change-driver-status.dto';

export class DriverFilterDto extends PaginationDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Filter by city ID',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  cityId?: string;

  @ApiProperty({
    example: '<PERSON>',
    description: 'Filter by driver name (searches in firstName and lastName)',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Filter by email address',
    required: false,
  })
  @IsOptional()
  @IsString()
  email?: string;

  @ApiProperty({
    example: '+************',
    description: 'Filter by phone number',
    required: false,
  })
  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @ApiProperty({
    enum: DriverStatus,
    example: DriverStatus.ACTIVE,
    description: 'Filter by driver status',
    required: false,
  })
  @IsOptional()
  @IsEnum(DriverStatus)
  status?: DriverStatus;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Filter by vehicle type ID',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  vehicleTypeId?: string;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Filter by driver ID',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  driverId?: string;
}

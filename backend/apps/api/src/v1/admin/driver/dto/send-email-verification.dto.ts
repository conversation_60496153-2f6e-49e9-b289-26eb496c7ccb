import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsUUID } from 'class-validator';

export class SendEmailVerificationDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'User profile ID',
  })
  @IsUUID()
  @IsNotEmpty()
  userProfileId!: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address to verify',
  })
  @IsEmail()
  @IsNotEmpty()
  email!: string;
}

import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsEmail,
  IsEnum,
  IsDateString,
  IsOptional,
  IsUUID,
  IsPhoneNumber,
  <PERSON>Length,
  MaxLength,
  IsNotEmpty,
} from 'class-validator';
import { Gender } from '@shared/shared/repositories/models/userProfile.model';

export class CreateDriverDto {
  @ApiProperty({
    description: 'User ID from registration',
    example: 'uuid-string',
  })
  @IsNotEmpty()
  @IsString()
  userId!: string;

  @ApiProperty({
    example: 'John',
    description: 'Driver first name',
    minLength: 2,
    maxLength: 50,
  })
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  firstName!: string;

  @ApiProperty({
    example: 'Doe',
    description: 'Driver last name',
    minLength: 2,
    maxLength: 50,
  })
  @IsString()
  // @MinLength(2)
  @MaxLength(50)
  lastName!: string;

  @ApiProperty({
    example: '+************',
    description: 'Driver mobile number in international format',
  })
  @IsPhoneNumber()
  mobileNumber!: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Driver email address',
  })
  @IsEmail()
  email!: string;

  @ApiProperty({
    enum: Gender,
    example: Gender.MALE,
    description: 'Driver gender',
  })
  @IsEnum(Gender)
  gender!: Gender;

  @ApiProperty({
    example: '1990-01-15',
    description: 'Driver date of birth in YYYY-MM-DD format',
  })
  @IsDateString()
  dob!: string;

  @ApiProperty({
    example: 'https://example.com/profile.jpg',
    description: 'Profile picture URL (uploaded via file upload API)',
    required: false,
  })
  @IsOptional()
  @IsString()
  profilePictureUrl?: string;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'City ID where the driver is located',
  })
  @IsOptional()
  @IsUUID()
  cityId!: string;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Language ID for the driver',
  })
  @IsOptional()
  @IsUUID()
  languageId!: string;
}

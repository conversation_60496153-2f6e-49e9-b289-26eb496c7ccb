import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsEnum,
  IsBoolean,
  IsUUID,
} from 'class-validator';
import {
  DiscountType,
  ApplicabilityType,
} from '@shared/shared/repositories/models/coupon.model';
import { PaginationDto } from 'apps/api/src/common/dto/pagination.dto';
import { Type } from 'class-transformer';

export class CouponFilterDto extends PaginationDto {
  @ApiPropertyOptional({
    description: 'Search by coupon name, code, or description',
    example: 'new year',
  })
  @IsOptional()
  @IsString()
  declare search?: string;

  @ApiPropertyOptional({
    description: 'Filter by applicability type',
    enum: ApplicabilityType,
    example: ApplicabilityType.CITY,
  })
  @IsOptional()
  @IsEnum(ApplicabilityType)
  applicabilityType?: ApplicabilityType;

  @ApiPropertyOptional({
    description: 'Filter by discount type',
    enum: DiscountType,
    example: DiscountType.PERCENTAGE,
  })
  @IsOptional()
  @IsEnum(DiscountType)
  discountType?: DiscountType;

  @ApiPropertyOptional({
    description: 'Filter by active status',
    example: true,
    type: Boolean,
  })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Filter by city ID (for city-specific coupons)',
    example: 'city-uuid-123',
  })
  @IsOptional()
  @IsUUID()
  cityId?: string;

  @ApiPropertyOptional({
    description: 'Filter by product ID (for product-specific coupons)',
    example: 'product-uuid-123',
  })
  @IsOptional()
  @IsUUID()
  productId?: string;

  @ApiPropertyOptional({
    description: 'Filter by expiration status',
    example: false,
    type: Boolean,
  })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  isExpired?: boolean;
}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsNumber,
  IsBoolean,
  IsArray,
  IsUUID,
  IsObject,
  Min,
  MinLength,
  MaxLength,
  ValidateNested,
  ArrayMinSize,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import {
  DiscountType,
  ApplicabilityType,
} from '@shared/shared/repositories/models/coupon.model';

export class CreateCouponDto {
  @ApiProperty({
    description: 'Display name of the coupon',
    example: 'New Year Special',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(2)
  @MaxLength(100)
  name!: string;

  @ApiProperty({
    description: 'Unique coupon code that users will enter',
    example: 'NEWYEAR2024',
    minLength: 3,
    maxLength: 20,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(20)
  @Transform(({ value }) => value?.toUpperCase())
  code!: string;

  @ApiPropertyOptional({
    description: 'Image URL for the coupon',
    example: 'https://example.com/coupon-image.jpg',
  })
  @IsOptional()
  @IsString()
  thumbnail?: string;

  @ApiPropertyOptional({
    description: 'Detailed description of the coupon offer',
    example: 'Get 20% off on your next ride. Valid for all products.',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiProperty({
    description: 'When the coupon becomes active (ISO 8601 format)',
    example: '2024-01-01T00:00:00.000Z',
    type: 'string',
    format: 'date-time',
  })
  @IsNotEmpty()
  startDate!: Date;

  @ApiProperty({
    description: 'When the coupon expires (ISO 8601 format)',
    example: '2024-12-31T23:59:59.999Z',
    type: 'string',
    format: 'date-time',
  })
  @IsNotEmpty()
  endDate!: Date;

  @ApiProperty({
    description: 'Type of discount',
    enum: DiscountType,
    example: DiscountType.PERCENTAGE,
  })
  @IsEnum(DiscountType)
  discountType!: DiscountType;

  @ApiProperty({
    description: 'The discount amount or percentage',
    example: 20,
    minimum: 0.01,
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01)
  discountValue!: number;

  @ApiPropertyOptional({
    description: 'Maximum discount cap (useful for percentage discounts)',
    example: 100,
    minimum: 0.01,
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01)
  maxDiscountLimit?: number;

  @ApiPropertyOptional({
    description: 'Minimum fare required to apply the coupon',
    example: 50,
    minimum: 0.01,
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01)
  minFareCondition?: number;

  @ApiProperty({
    description:
      'Total number of times this coupon can be used across all users',
    example: 1000,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  usageLimit!: number;

  @ApiProperty({
    description: 'Applicability type for the coupon',
    enum: ApplicabilityType,
    example: ApplicabilityType.CITY,
  })
  @IsEnum(ApplicabilityType)
  applicabilityType!: ApplicabilityType;

  @ApiPropertyOptional({
    description: 'JSONLogic rules for advanced conditions',
    example: {
      and: [
        { '>': [{ var: 'fare' }, 100] },
        { '==': [{ var: 'day' }, 'friday'] },
      ],
    },
  })
  @IsOptional()
  @IsObject()
  applyConditionLogic?: any;

  @ApiPropertyOptional({
    description: 'Whether the coupon is active',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean = true;
}

export class CouponApplicabilityDto {
  @ApiPropertyOptional({
    description: 'Zone IDs for city-specific coupons',
    example: ['zone-uuid-1', 'zone-uuid-2'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  @ArrayMinSize(1)
  zoneIds?: string[];

  @ApiPropertyOptional({
    description: 'Product IDs for product-specific coupons',
    example: ['product-uuid-1', 'product-uuid-2'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  @ArrayMinSize(1)
  productIds?: string[];

  @ApiPropertyOptional({
    description: 'City-Product IDs for city-product-specific coupons',
    example: ['city-product-uuid-1', 'city-product-uuid-2'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  @ArrayMinSize(1)
  cityProductIds?: string[];

  @ApiPropertyOptional({
    description: 'User Profile IDs for user-specific coupons',
    example: ['user-uuid-1', 'user-uuid-2'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  @ArrayMinSize(1)
  userIds?: string[];
}

export class CreateCouponWithApplicabilityDto {
  @ApiProperty({
    description: 'Coupon details',
    type: CreateCouponDto,
  })
  @ValidateNested()
  @Type(() => CreateCouponDto)
  @IsNotEmpty()
  coupon!: CreateCouponDto;

  @ApiProperty({
    description: 'Applicability rules for the coupon',
    type: CouponApplicabilityDto,
  })
  @ValidateNested()
  @Type(() => CouponApplicabilityDto)
  @IsNotEmpty()
  applicability!: CouponApplicabilityDto;
}

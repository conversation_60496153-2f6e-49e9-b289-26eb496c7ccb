import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  DiscountType,
  ApplicabilityType,
} from '@shared/shared/repositories/models/coupon.model';

export class CouponZoneDto {
  @ApiProperty({ example: 'zone-uuid-123' })
  id!: string;

  @ApiProperty({ example: 'zone-uuid-123' })
  zoneId!: string;

  @ApiPropertyOptional({
    description: 'Zone details',
    example: {
      id: 'zone-uuid-123',
      name: 'Downtown',
      city: { id: 'city-uuid-123', name: 'Kochi' },
    },
  })
  zone?: any;
}

export class CouponProductDto {
  @ApiProperty({ example: 'coupon-product-uuid-123' })
  id!: string;

  @ApiProperty({ example: 'product-uuid-123' })
  productId!: string;

  @ApiPropertyOptional({
    description: 'Product details',
    example: {
      id: 'product-uuid-123',
      name: 'Tukxi Go',
      icon: 'https://example.com/icon.png',
    },
  })
  product?: any;
}

export class CouponCityProductDto {
  @ApiProperty({ example: 'coupon-city-product-uuid-123' })
  id!: string;

  @ApiProperty({ example: 'city-product-uuid-123' })
  cityProductId!: string;

  @ApiPropertyOptional({
    description: 'City-Product details',
    example: {
      id: 'city-product-uuid-123',
      city: { id: 'city-uuid-123', name: 'Kochi' },
      product: { id: 'product-uuid-123', name: 'Tukxi Go' },
    },
  })
  cityProduct?: any;
}

export class CouponUserDto {
  @ApiProperty({ example: 'coupon-user-uuid-123' })
  id!: string;

  @ApiProperty({ example: 'user-profile-uuid-123' })
  userId!: string;

  @ApiPropertyOptional({
    description: 'User profile details',
    example: {
      id: 'user-profile-uuid-123',
      firstName: 'John',
      lastName: 'Doe',
      user: { phoneNumber: '+************' },
    },
  })
  userProfile?: any;
}

export class CouponResponseDto {
  @ApiProperty({ example: 'coupon-uuid-123' })
  id!: string;

  @ApiProperty({ example: 'New Year Special' })
  name!: string;

  @ApiProperty({ example: 'NEWYEAR2024' })
  code!: string;

  @ApiPropertyOptional({ example: 'https://example.com/coupon-image.jpg' })
  thumbnail?: string | null;

  @ApiPropertyOptional({ example: 'Get 20% off on your next ride' })
  description?: string | null;

  @ApiProperty({ example: '2024-01-01T00:00:00.000Z' })
  startDate!: Date;

  @ApiProperty({ example: '2024-12-31T23:59:59.999Z' })
  endDate!: Date;

  @ApiProperty({ enum: DiscountType, example: DiscountType.PERCENTAGE })
  discountType!: DiscountType;

  @ApiProperty({ example: 20 })
  discountValue!: number;

  @ApiPropertyOptional({ example: 100 })
  maxDiscountLimit?: number | null;

  @ApiPropertyOptional({ example: 50 })
  minFareCondition?: number | null;

  @ApiProperty({ example: 1000 })
  usageLimit!: number;

  @ApiProperty({ example: 150 })
  usageCount!: number;

  @ApiProperty({ enum: ApplicabilityType, example: ApplicabilityType.CITY })
  applicabilityType!: ApplicabilityType;

  @ApiPropertyOptional({
    description: 'JSONLogic rules for advanced conditions',
    example: { and: [{ '>': [{ var: 'fare' }, 100] }] },
  })
  applyConditionLogic?: any;

  @ApiProperty({ example: true })
  isActive!: boolean;

  @ApiProperty({ example: '2024-01-01T10:00:00.000Z' })
  createdAt!: Date;

  @ApiProperty({ example: '2024-01-01T10:00:00.000Z' })
  updatedAt!: Date;

  @ApiPropertyOptional({
    description: 'Applicable zones (for city-specific coupons)',
    type: [CouponZoneDto],
  })
  couponZones?: CouponZoneDto[];

  @ApiPropertyOptional({
    description: 'Applicable products (for product-specific coupons)',
    type: [CouponProductDto],
  })
  couponProducts?: CouponProductDto[];

  @ApiPropertyOptional({
    description: 'Applicable city-product combinations',
    type: [CouponCityProductDto],
  })
  couponCityProducts?: CouponCityProductDto[];

  @ApiPropertyOptional({
    description: 'Applicable users (for user-specific coupons)',
    type: [CouponUserDto],
  })
  couponUsers?: CouponUserDto[];

  @ApiPropertyOptional({
    description: 'Human-readable applicability summary',
    example: 'Applicable to: Zones in Kochi, Trivandrum',
  })
  applicabilitySummary?: string;
}

export class CouponListResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Coupons retrieved successfully' })
  message!: string;

  @ApiProperty({ type: [CouponResponseDto] })
  data!: CouponResponseDto[];

  @ApiProperty({
    example: {
      total: 100,
      page: 1,
      limit: 10,
      totalPages: 10,
      hasNextPage: true,
      hasPreviousPage: false,
    },
  })
  meta!: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}

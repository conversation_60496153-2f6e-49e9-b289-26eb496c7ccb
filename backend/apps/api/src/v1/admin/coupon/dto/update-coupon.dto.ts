import { ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateCouponDto, CouponApplicabilityDto } from './create-coupon.dto';

export class UpdateCouponDto extends PartialType(CreateCouponDto) {}

export class UpdateCouponWithApplicabilityDto {
  @ApiPropertyOptional({
    description: 'Coupon details to update',
    type: UpdateCouponDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateCouponDto)
  coupon?: UpdateCouponDto;

  @ApiPropertyOptional({
    description: 'Updated applicability rules for the coupon',
    type: CouponApplicabilityDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CouponApplicabilityDto)
  applicability?: CouponApplicabilityDto;
}

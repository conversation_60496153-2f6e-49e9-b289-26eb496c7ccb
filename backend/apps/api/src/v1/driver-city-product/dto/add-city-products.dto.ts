import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID, ArrayNotEmpty } from 'class-validator';

export class AddCityProductsToDriverDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Driver vehicle ID to associate city products with',
  })
  @IsUUID('4')
  driverVehicleId!: string;

  @ApiProperty({
    example: [
      '550e8400-e29b-41d4-a716-************',
      '550e8400-e29b-41d4-a716-************',
    ],
    description: 'Array of city product IDs to add to the driver vehicle',
    type: [String],
  })
  @IsArray()
  @ArrayNotEmpty()
  @IsUUID('4', { each: true })
  cityProductIds!: string[];
}

import {
  Controller,
  Post,
  Get,
  Patch,
  Param,
  Body,
  Query,
  HttpCode,
  HttpStatus,
  // Req,
  UseGuards,
  ParseUUIDPipe,
  ParseIntPipe,
  // BadRequestException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { DriverCityProductService } from '../../../../../libs/shared/src/modules/driver-city-product/driver-city-product.service';
import { AddCityProductsToDriverDto } from './dto/add-city-products.dto';
import { RemoveCityProductsFromDriverDto } from './dto/remove-city-products.dto';
import { UpdateCityProductPrimaryStatusDto } from './dto/update-primary-status.dto';
import {
  DriverCityProductListResponseDto,
  AddCityProductsResponseDto,
  RemoveCityProductsResponseDto,
} from './dto/driver-city-product-response.dto';
import {
  ApiResponseDto,
  ApiErrorResponseDto,
} from '../../docs/swagger/common-responses.dto';
import { JwtAuthGuard } from '@shared/shared';

@ApiTags('Driver City Products')
@Controller('driver-city-products')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class DriverCityProductController {
  constructor(
    private readonly driverCityProductService: DriverCityProductService,
  ) {}

  @Post('add-city-products')
  @ApiOperation({
    summary: 'Add city products to a driver vehicle',
    description: 'Add multiple city products to a specific driver vehicle',
  })
  @ApiResponse({
    status: 201,
    description: 'City products added successfully',
    type: ApiResponseDto<AddCityProductsResponseDto>,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input or city products not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Driver vehicle not found',
    type: ApiErrorResponseDto,
  })
  async addCityProductsToDriver(@Body() dto: AddCityProductsToDriverDto) {
    const result =
      await this.driverCityProductService.addCityProductsToDriverVehicle(
        dto.driverVehicleId,
        dto.cityProductIds,
      );

    return {
      success: true,
      message: 'City products added successfully to driver vehicle',
      data: result,
    };
  }

  @Post('remove-city-products')
  @ApiOperation({
    summary: 'Remove city products from a driver vehicle',
    description: 'Remove multiple city products from a specific driver vehicle',
  })
  @ApiResponse({
    status: 200,
    description: 'City products removed successfully',
    type: ApiResponseDto<RemoveCityProductsResponseDto>,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - No city products found for removal',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Driver vehicle not found',
    type: ApiErrorResponseDto,
  })
  async removeCityProductsFromDriver(
    @Body() dto: RemoveCityProductsFromDriverDto,
  ) {
    const result =
      await this.driverCityProductService.removeCityProductsFromDriverVehicle(
        dto.driverVehicleId,
        dto.cityProductIds,
      );

    return {
      success: true,
      message: result.message,
      data: null,
    };
  }

  @Get('drivers/:driverId/products')
  @ApiOperation({
    summary: 'Get city products for a driver',
    description:
      'Get paginated list of city products assigned to a specific driver with optional product name search',
  })
  @ApiParam({
    name: 'driverId',
    description: 'Driver user profile ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'productName',
    required: false,
    description: 'Product name search term',
    example: 'taxi',
  })
  @ApiResponse({
    status: 200,
    description: 'Driver city products retrieved successfully',
    type: ApiResponseDto<DriverCityProductListResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'Driver not found',
    type: ApiErrorResponseDto,
  })
  async getDriverCityProducts(
    @Param('driverId', ParseUUIDPipe) driverId: string,
    @Query('page', new ParseIntPipe({ optional: true })) page = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit = 10,
    @Query('productName') productName?: string,
  ) {
    const result = await this.driverCityProductService.getDriverCityProducts(
      driverId,
      page,
      limit,
      productName,
    );

    return {
      success: true,
      message: 'Driver city products retrieved successfully',
      data: result,
    };
  }

  @Patch('update-primary-status')
  @ApiOperation({
    summary: 'Update primary status of city product for driver vehicle',
    description:
      'Update the primary status of a city product for a specific driver vehicle. Only one city product can be primary per driver vehicle.',
  })
  @ApiResponse({
    status: 200,
    description: 'Primary status updated successfully',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Driver vehicle or city product not found',
    type: ApiErrorResponseDto,
  })
  async updateCityProductPrimaryStatus(
    @Body() dto: UpdateCityProductPrimaryStatusDto,
  ) {
    const result =
      await this.driverCityProductService.updateCityProductPrimaryStatus(
        dto.driverVehicleId,
        dto.cityProductId,
        dto.isPrimary,
      );

    return {
      success: true,
      message: 'Primary status updated successfully',
      data: result,
      timestamp: Date.now(),
    };
  }

  @Get('driver-vehicle/:driverVehicleId/city-products')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get city products for a driver vehicle',
    description:
      'Get paginated list of city products assigned to a specific driver vehicle',
  })
  @ApiParam({
    name: 'driverVehicleId',
    description: 'Driver vehicle ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Items per page',
    example: 10,
  })
  @ApiResponse({
    status: 200,
    description: 'Driver vehicle city products retrieved successfully',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Driver vehicle not found',
    type: ApiErrorResponseDto,
  })
  async getDriverVehicleCityProducts(
    @Param('driverVehicleId', ParseUUIDPipe) driverVehicleId: string,
    @Query('page', new ParseIntPipe({ optional: true })) page = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit = 10,
  ) {
    const result =
      await this.driverCityProductService.getDriverVehicleCityProducts(
        driverVehicleId,
        page,
        limit,
      );

    return {
      success: true,
      message: 'Driver vehicle city products retrieved successfully',
      data: result.data,
      meta: {
        page: result.meta.page,
        limit: result.meta.limit,
        total: result.meta.total,
        totalPages: result.meta.totalPages,
        hasNextPage: result.meta.hasNextPage,
        hasPreviousPage: result.meta.hasPrevPage,
      },
      timestamp: Date.now(),
    };
  }
}

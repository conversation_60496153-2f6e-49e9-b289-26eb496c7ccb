import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsEnum,
  IsUUI<PERSON>,
  IsInt,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CommissionType } from '@shared/shared/repositories/models/commission.model';

export class CommissionPaginationDto {
  @ApiProperty({
    example: 1,
    description: 'Page number',
    minimum: 1,
    default: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Page must be an integer' })
  @Min(1, { message: 'Page must be at least 1' })
  page?: number = 1;

  @ApiProperty({
    example: 10,
    description: 'Number of items per page',
    minimum: 1,
    maximum: 100,
    default: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Limit must be an integer' })
  @Min(1, { message: 'Limit must be at least 1' })
  @Max(100, { message: 'Limit cannot exceed 100' })
  limit?: number = 10;

  @ApiProperty({
    example: 'driver',
    description: 'Search term to filter commissions by name or description',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Search must be a string' })
  search?: string;

  @ApiProperty({
    example: 'percentage',
    description: 'Filter by commission type',
    enum: CommissionType,
    required: false,
  })
  @IsOptional()
  @IsEnum(CommissionType, {
    message: 'Type must be either "percentage" or "flat"',
  })
  type?: CommissionType;

  @ApiProperty({
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
    description: 'Filter by tax group ID',
    required: false,
  })
  @IsOptional()
  @IsUUID(4, { message: 'Tax group ID must be a valid UUID' })
  taxGroupId?: string;
}

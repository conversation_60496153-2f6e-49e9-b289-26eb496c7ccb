import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  MaxLength,
  IsOptional,
  IsEnum,
  IsNumber,
  Min,
  Max,
  IsUUID,
  ValidateIf,
} from 'class-validator';
import { CommissionType } from '@shared/shared/repositories/models/commission.model';

export class UpdateCommissionDto {
  @ApiProperty({
    example: 'Driver Commission',
    description: 'Name of the commission',
    maxLength: 100,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Commission name must be a string' })
  @IsNotEmpty({ message: 'Commission name cannot be empty' })
  @MaxLength(100, { message: 'Commission name cannot exceed 100 characters' })
  name?: string;

  @ApiProperty({
    example: 'Commission for driver services',
    description: 'Description of the commission',
    maxLength: 500,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Commission description must be a string' })
  @MaxLength(500, {
    message: 'Commission description cannot exceed 500 characters',
  })
  description?: string;

  @ApiProperty({
    example: 'percentage',
    description: 'Type of commission calculation',
    enum: CommissionType,
    required: false,
  })
  @IsOptional()
  @IsEnum(CommissionType, {
    message: 'Commission type must be either "percentage" or "flat"',
  })
  type?: CommissionType;

  @ApiProperty({
    example: 15.5,
    description: 'Percentage value (required when type is "percentage")',
    minimum: 0.01,
    maximum: 100,
    required: false,
  })
  @ValidateIf(
    (o) =>
      o.type === CommissionType.PERCENTAGE ||
      (!o.type && o.percentageValue !== undefined),
  )
  @IsNumber(
    { maxDecimalPlaces: 2 },
    {
      message:
        'Percentage value must be a number with at most 2 decimal places',
    },
  )
  @Min(0.01, { message: 'Percentage value must be greater than 0' })
  @Max(100, { message: 'Percentage value cannot exceed 100' })
  @IsOptional()
  percentageValue?: number;

  @ApiProperty({
    example: 25.0,
    description: 'Flat amount value (required when type is "flat")',
    minimum: 0.01,
    required: false,
  })
  @ValidateIf(
    (o) =>
      o.type === CommissionType.FLAT || (!o.type && o.flatValue !== undefined),
  )
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: 'Flat value must be a number with at most 2 decimal places' },
  )
  @Min(0.01, { message: 'Flat value must be greater than 0' })
  @IsOptional()
  flatValue?: number;

  @ApiProperty({
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
    description: 'Tax group ID to apply tax to this commission',
    required: false,
  })
  @IsUUID(4, { message: 'Tax group ID must be a valid UUID' })
  @IsOptional()
  taxGroupId?: string;
}

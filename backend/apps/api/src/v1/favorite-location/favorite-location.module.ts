import { Module } from '@nestjs/common';
import { FavoriteLocationController } from './favorite-location.controller';
import { FavoriteLocationModule as SharedFavoriteLocationModule } from '../../../../../libs/shared/src/modules/favorite-location/favorite-location.module';

@Module({
  imports: [SharedFavoriteLocationModule],
  controllers: [FavoriteLocationController],
})
export class ApiFavoriteLocationModule {}

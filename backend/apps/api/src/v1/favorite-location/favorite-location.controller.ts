import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  Query,
  Req,
  HttpCode,
  HttpStatus,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { FavoriteLocationService } from '../../../../../libs/shared/src/modules/favorite-location/favorite-location.service';
import { CreateFavoriteLocationDto } from './dto/create-favorite-location.dto';
import { UpdateFavoriteLocationDto } from './dto/update-favorite-location.dto';
import { FavoriteLocationPaginationDto } from './dto/favorite-location-pagination.dto';
import {
  CreateFavoriteLocationResponseDto,
  FavoriteLocationDetailsResponseDto,
  FavoriteLocationPaginatedResponseDto,
} from './dto/favorite-location-response.dto';
import { ApiErrorResponseDto } from '../../docs/swagger/common-responses.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';

@ApiTags('Favorite Locations')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('favorite-locations')
export class FavoriteLocationController {
  constructor(
    private readonly favoriteLocationService: FavoriteLocationService,
  ) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new favorite location',
    description: 'Create a new favorite location for the authenticated user',
  })
  @ApiResponse({
    status: 201,
    description: 'Favorite location created successfully',
    type: CreateFavoriteLocationResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input data',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Favorite location name already exists',
    type: ApiErrorResponseDto,
  })
  async create(
    @Body() createFavoriteLocationDto: CreateFavoriteLocationDto,
    @Req() req: any,
  ): Promise<CreateFavoriteLocationResponseDto> {
    const userProfileId = req.user?.profileId;

    if (!userProfileId) {
      throw new Error('User profile not found in authentication token');
    }

    const data = await this.favoriteLocationService.createFavoriteLocation({
      userProfileId,
      ...createFavoriteLocationDto,
    });

    return {
      success: true,
      message: 'Favorite location created successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get()
  @ApiOperation({
    summary: 'Get favorite locations',
    description:
      'Get paginated list of favorite locations for the authenticated user with search functionality',
  })
  @ApiResponse({
    status: 200,
    description: 'Favorite locations retrieved successfully',
    type: FavoriteLocationPaginatedResponseDto,
  })
  async findAll(
    @Query() paginationDto: FavoriteLocationPaginationDto,
    @Req() req: any,
  ): Promise<FavoriteLocationPaginatedResponseDto> {
    const userProfileId = req.user?.profileId;

    if (!userProfileId) {
      throw new Error('User profile not found in authentication token');
    }

    const result = await this.favoriteLocationService.getFavoriteLocations(
      userProfileId,
      paginationDto,
    );

    return {
      success: true,
      message: 'Favorite locations retrieved successfully',
      data: result,
      timestamp: Date.now(),
    };
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get favorite location by ID',
    description:
      'Get a specific favorite location by ID for the authenticated user',
  })
  @ApiParam({
    name: 'id',
    description: 'Favorite location ID',
    example: 'favorite-location-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Favorite location retrieved successfully',
    type: FavoriteLocationDetailsResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Favorite location not found',
    type: ApiErrorResponseDto,
  })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: any,
  ): Promise<FavoriteLocationDetailsResponseDto> {
    const userProfileId = req.user?.profileId;

    if (!userProfileId) {
      throw new Error('User profile not found in authentication token');
    }

    const data = await this.favoriteLocationService.getFavoriteLocationById(
      id,
      userProfileId,
    );

    return {
      success: true,
      message: 'Favorite location retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'Update favorite location',
    description:
      'Update a specific favorite location for the authenticated user',
  })
  @ApiParam({
    name: 'id',
    description: 'Favorite location ID',
    example: 'favorite-location-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Favorite location updated successfully',
    type: FavoriteLocationDetailsResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Favorite location not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Favorite location name already exists',
    type: ApiErrorResponseDto,
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateFavoriteLocationDto: UpdateFavoriteLocationDto,
    @Req() req: any,
  ): Promise<FavoriteLocationDetailsResponseDto> {
    const userProfileId = req.user?.profileId;

    if (!userProfileId) {
      throw new Error('User profile not found in authentication token');
    }

    const data = await this.favoriteLocationService.updateFavoriteLocation(
      id,
      userProfileId,
      updateFavoriteLocationDto,
    );

    return {
      success: true,
      message: 'Favorite location updated successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete favorite location',
    description:
      'Delete a specific favorite location for the authenticated user',
  })
  @ApiParam({
    name: 'id',
    description: 'Favorite location ID',
    example: 'favorite-location-uuid-123',
  })
  @ApiResponse({
    status: 204,
    description: 'Favorite location deleted successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Favorite location not found',
    type: ApiErrorResponseDto,
  })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: any,
  ): Promise<any> {
    const userProfileId = req.user?.profileId;

    if (!userProfileId) {
      throw new Error('User profile not found in authentication token');
    }

    await this.favoriteLocationService.deleteFavoriteLocation(
      id,
      userProfileId,
    );
    return {
      success: true,
      message: 'Favorite location deleted successfully',
      timestamp: Date.now(),
    };
  }
}

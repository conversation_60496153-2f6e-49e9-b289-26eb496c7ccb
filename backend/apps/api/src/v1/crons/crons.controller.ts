import { Controller, Post, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { RidesCronService } from './rides.cron.service';

@ApiTags('Crons')
@Controller('crons')
export class CronsController {
  constructor(private readonly ridesCronService: RidesCronService) {}

  /**
   * Manual trigger for scheduled rides processing (for testing/debugging)
   */
  @Post('trigger-scheduled-rides')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Manually trigger scheduled rides processing',
    description:
      'Manually trigger the processing of scheduled rides. This is useful for testing and debugging purposes.',
  })
  @ApiResponse({
    status: 200,
    description: 'Scheduled rides processing completed',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Scheduled rides processing completed',
        },
        data: {
          type: 'object',
          properties: {
            processed: { type: 'number', example: 5 },
            failed: { type: 'number', example: 0 },
            errors: { type: 'array', items: { type: 'string' }, example: [] },
          },
        },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async triggerScheduledRidesProcessing(): Promise<{
    success: boolean;
    message: string;
    data: {
      processed: number;
      failed: number;
      errors: string[];
    };
    timestamp: number;
  }> {
    const result =
      await this.ridesCronService.triggerScheduledRidesProcessing();

    return {
      success: true,
      message: 'Scheduled rides processing completed',
      data: result,
      timestamp: Date.now(),
    };
  }
}

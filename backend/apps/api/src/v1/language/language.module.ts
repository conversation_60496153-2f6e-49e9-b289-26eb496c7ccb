import { Module } from '@nestjs/common';
import { LanguageController } from './language.controller';
import { LanguageService } from '../../../../../libs/shared/src/modules/language/language.service';
import { LanguageModule as SharedLanguageModule } from '@shared/shared/modules/language/language.module';
import { CityModule } from '@shared/shared/modules/city/city.module';
import { CityAdminRepository } from '@shared/shared/repositories/city-admin.repository';

@Module({
  imports: [SharedLanguageModule, CityModule],
  controllers: [LanguageController],
  providers: [LanguageService, CityAdminRepository],
})
export class ApiLanguageModule {}

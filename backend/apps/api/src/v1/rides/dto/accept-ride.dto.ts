import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  ValidateNested,
  IsNumber,
  Min,
  Max,
  IsNotEmpty,
} from 'class-validator';
import { Type } from 'class-transformer';

export class DriverLocationDto {
  @ApiProperty({
    example: 12.9716,
    description: 'Driver Latitude coordinate',
    minimum: -90,
    maximum: 90,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(-90)
  @Max(90)
  lat!: number;

  @ApiProperty({
    example: 77.5946,
    description: 'Driver Longitude coordinate',
    minimum: -180,
    maximum: 180,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(-180)
  @Max(180)
  lng!: number;
}

export class AcceptRideDto {
  @ApiProperty({
    description: 'Driver vehicle ID (optional)',
    example: 'vehicle-uuid-123',
    required: false,
  })
  @IsOptional()
  @IsString()
  driverVehicleId?: string;

  @ApiProperty({
    type: DriverLocationDto,
    description: 'Current driver location for calculating distance to pickup',
    example: { lat: 12.9716, lng: 77.5946 },
  })
  @ValidateNested()
  @IsOptional()
  @Type(() => DriverLocationDto)
  location!: DriverLocationDto;
}

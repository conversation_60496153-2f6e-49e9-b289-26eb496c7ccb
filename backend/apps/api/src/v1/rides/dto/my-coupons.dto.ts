import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsN<PERSON>ber,
  IsNotEmpty,
  IsOptional,
  IsPositive,
  Min,
  Max,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class MyCouponsQueryDto {
  @ApiProperty({
    description: 'Current latitude',
    example: 12.9716,
    type: Number,
  })
  @IsNumber({}, { message: 'Latitude must be a valid number' })
  @IsNotEmpty({ message: 'Latitude is required' })
  @Transform(({ value }) => parseFloat(value))
  lat!: number;

  @ApiProperty({
    description: 'Current longitude',
    example: 77.5946,
    type: Number,
  })
  @IsNumber({}, { message: 'Longitude must be a valid number' })
  @IsNotEmpty({ message: 'Longitude is required' })
  @Transform(({ value }) => parseFloat(value))
  lng!: number;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    default: 1,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Page must be a valid number' })
  @IsPositive({ message: 'Page must be a positive number' })
  @Min(1, { message: 'Page must be at least 1' })
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of coupons per page',
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 50,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: 'Limit must be a valid number' })
  @IsPositive({ message: 'Limit must be a positive number' })
  @Min(1, { message: 'Limit must be at least 1' })
  @Max(50, { message: 'Limit cannot exceed 50' })
  limit?: number = 10;
}

export class MyCouponsResponseDto {
  @ApiProperty({
    description: 'Coupon ID',
    example: 'coupon-123',
  })
  id!: string;

  @ApiProperty({
    description: 'Coupon code',
    example: 'SAVE20',
  })
  code!: string;

  @ApiProperty({
    description: 'Coupon title',
    example: 'Save 20% on your ride',
  })
  title!: string;

  @ApiProperty({
    description: 'Coupon description',
    example: 'Get 20% discount on your next ride',
  })
  description!: string;

  @ApiProperty({
    description: 'Discount type',
    example: 'PERCENTAGE',
    enum: ['FLAT', 'PERCENTAGE'],
  })
  discountType!: string;

  @ApiProperty({
    description: 'Discount value',
    example: 20,
  })
  discountValue!: number;

  @ApiProperty({
    description: 'Maximum discount amount (for percentage coupons)',
    example: 100,
    required: false,
  })
  maxDiscountAmount?: number;

  @ApiProperty({
    description: 'Minimum order value required',
    example: 50,
    required: false,
  })
  minOrderValue?: number;

  @ApiProperty({
    description: 'Coupon expiry date',
    example: '2024-12-31T23:59:59.000Z',
  })
  expiryDate!: Date;

  @ApiProperty({
    description: 'Whether the user has already used this coupon',
    example: false,
  })
  isUsed!: boolean;

  @ApiProperty({
    description: 'Remaining usage count for this user',
    example: 2,
  })
  remainingUsages!: number;
}

export class MyCouponsPaginatedResponseDto {
  @ApiProperty({
    description: 'Array of coupons',
    type: [MyCouponsResponseDto],
  })
  data!: MyCouponsResponseDto[];

  @ApiProperty({
    description: 'Pagination metadata',
    example: {
      total: 25,
      page: 1,
      limit: 10,
      totalPages: 3,
      hasNextPage: true,
      hasPreviousPage: false,
    },
  })
  meta!: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

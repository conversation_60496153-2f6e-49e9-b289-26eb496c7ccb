import { Injectable, Logger } from '@nestjs/common';
import { DriverEarningsService } from '@shared/shared/modules/driver-earnings/driver-earnings.service';

/**
 * CSV Generator utility for driver earnings reports
 */
class DriverEarningsCSVGenerator {
  /**
   * Escape CSV field values to handle commas, quotes, and newlines
   */
  private static escapeCSVField(field: any): string {
    if (field === null || field === undefined) {
      return '';
    }

    const stringValue = String(field);

    // If field contains comma, quote, or newline, wrap in quotes and escape quotes
    if (
      stringValue.includes(',') ||
      stringValue.includes('"') ||
      stringValue.includes('\n')
    ) {
      return `"${stringValue.replace(/"/g, '""')}"`;
    }

    return stringValue;
  }

  /**
   * Generate CSV for aggregated driver earnings
   */
  static generateAggregatedEarningsCSV(earnings: any[]): string {
    if (earnings.length === 0) {
      return this.getAggregatedHeaders();
    }

    const headers = [
      'Driver ID',
      'Driver Name',
      'City',
      'Total Fare (₹)',
      'Total Taxes on Charge (₹)',
      'Total Commission (₹)',
      'Total Tax on Commission (₹)',
      'Net Driver Earnings (₹)',
      'Completed Rides',
    ];

    const headerRow = headers.map((h) => this.escapeCSVField(h)).join(',');

    const dataRows = earnings.map((earning) => {
      const driverName =
        `${earning.driverFirstName} ${earning.driverLastName}`.trim();
      return [
        earning.driverId,
        driverName,
        earning.city,
        earning.totalFare,
        earning.totalTaxesOnCharge,
        earning.totalCommission,
        earning.totalTaxOnCommission,
        earning.netDriverEarnings,
        earning.completedRides,
      ]
        .map((field) => this.escapeCSVField(field))
        .join(',');
    });

    return [headerRow, ...dataRows].join('\n');
  }

  /**
   * Generate CSV for daily driver earnings
   */
  static generateDailyEarningsCSV(earnings: any[]): string {
    if (earnings.length === 0) {
      return this.getDailyHeaders();
    }

    const headers = [
      'Date',
      'Total Fare (₹)',
      'Total Taxes on Charge (₹)',
      'Total Commission (₹)',
      'Total Tax on Commission (₹)',
      'Net Driver Earnings (₹)',
      'Completed Rides',
    ];

    const headerRow = headers.map((h) => this.escapeCSVField(h)).join(',');

    const dataRows = earnings.map((earning) => {
      return [
        earning.date,
        earning.totalFare,
        earning.totalTaxesOnCharge,
        earning.totalCommission,
        earning.totalTaxOnCommission,
        earning.netDriverEarnings,
        earning.completedRides,
      ]
        .map((field) => this.escapeCSVField(field))
        .join(',');
    });

    return [headerRow, ...dataRows].join('\n');
  }

  /**
   * Get aggregated earnings headers only
   */
  private static getAggregatedHeaders(): string {
    const headers = [
      'Driver ID',
      'Driver Name',
      'City',
      'Total Fare (₹)',
      'Total Taxes on Charge (₹)',
      'Total Commission (₹)',
      'Total Tax on Commission (₹)',
      'Net Driver Earnings (₹)',
      'Completed Rides',
    ];
    return headers.map((h) => this.escapeCSVField(h)).join(',');
  }

  /**
   * Get daily earnings headers only
   */
  private static getDailyHeaders(): string {
    const headers = [
      'Date',
      'Total Fare (₹)',
      'Total Taxes on Charge (₹)',
      'Total Commission (₹)',
      'Total Tax on Commission (₹)',
      'Net Driver Earnings (₹)',
      'Completed Rides',
    ];
    return headers.map((h) => this.escapeCSVField(h)).join(',');
  }

  /**
   * Convert CSV string to Buffer
   */
  static csvToBuffer(csvContent: string): Buffer {
    return Buffer.from(csvContent, 'utf-8');
  }

  /**
   * Generate filename with timestamp
   */
  static generateFileName(prefix: string = 'driver-earnings-report'): string {
    const timestamp = new Date()
      .toISOString()
      .replace(/[:.]/g, '-')
      .split('T')[0];
    return `${prefix}-${timestamp}.csv`;
  }
}

@Injectable()
export class DriverEarningsReportService {
  private readonly logger = new Logger(DriverEarningsReportService.name);

  constructor(private readonly driverEarningsService: DriverEarningsService) {}

  /**
   * Generate aggregated driver earnings report as CSV
   */
  async generateAggregatedEarningsReport(
    fromDate?: Date,
    toDate?: Date,
    driverId?: string,
    cityId?: string,
  ): Promise<{ fileName: string; content: string }> {
    this.logger.log('Generating aggregated driver earnings report');

    try {
      // Fetch all aggregated earnings (without pagination for CSV export)
      const result = await this.driverEarningsService.getAggregatedEarnings(
        1,
        10000, // Large limit to get all data
        fromDate,
        toDate,
        driverId,
        cityId,
      );

      const csvContent =
        DriverEarningsCSVGenerator.generateAggregatedEarningsCSV(result.data);
      const fileName = DriverEarningsCSVGenerator.generateFileName(
        'aggregated-driver-earnings',
      );

      this.logger.log(
        `Generated aggregated earnings report with ${result.data.length} records`,
      );

      return {
        fileName,
        content: csvContent,
      };
    } catch (error) {
      this.logger.error(
        'Failed to generate aggregated earnings report:',
        error,
      );
      throw error;
    }
  }

  /**
   * Generate daily driver earnings report as CSV
   */
  async generateDailyEarningsReport(
    driverId: string,
    fromDate?: Date,
    toDate?: Date,
  ): Promise<{ fileName: string; content: string }> {
    this.logger.log(`Generating daily earnings report for driver ${driverId}`);

    try {
      // Fetch all daily earnings (without pagination for CSV export)
      const result = await this.driverEarningsService.getDailyEarningsForDriver(
        driverId,
        1,
        10000, // Large limit to get all data
        fromDate,
        toDate,
      );

      const csvContent = DriverEarningsCSVGenerator.generateDailyEarningsCSV(
        result.data,
      );
      const fileName = DriverEarningsCSVGenerator.generateFileName(
        `daily-earnings-${driverId}`,
      );

      this.logger.log(
        `Generated daily earnings report with ${result.data.length} records`,
      );

      return {
        fileName,
        content: csvContent,
      };
    } catch (error) {
      this.logger.error('Failed to generate daily earnings report:', error);
      throw error;
    }
  }
}

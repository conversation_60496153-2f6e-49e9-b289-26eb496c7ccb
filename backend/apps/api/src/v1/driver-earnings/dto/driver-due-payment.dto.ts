import { ApiProperty } from '@nestjs/swagger';
import { DriverAccountTransaction } from '@shared/shared/repositories/models/driverAccount.model';
import { IsNumber, IsOptional, Min } from 'class-validator';

export class InitiateDuePaymentDto {
  @ApiProperty({
    description: 'Amount to pay in ₹',
    example: 500,
    type: 'number',
  })
  @IsNumber()
  @Min(1)
  amount!: number;
}

export class DuePaymentCallbackDto {
  @ApiProperty({
    description: 'Cashfree transaction ID',
    example: 'txn_123456789',
  })
  transactionId!: string;

  @ApiProperty({
    description: 'Payment status from Cashfree',
    example: 'SUCCESS',
  })
  paymentStatus!: string;

  @ApiProperty({
    description: 'Additional metadata from payment gateway',
    required: false,
  })
  @IsOptional()
  metadata?: Record<string, any>;
}

/**
 * Cashfree webhook payload structure
 * This DTO handles the nested structure from Cashfree payment gateway
 */
export class CashfreeWebhookPayloadDto {
  @ApiProperty({
    description: 'Webhook event data',
    type: 'object',
    additionalProperties: true,
  })
  data!: Record<string, any>;

  @ApiProperty({
    description: 'Event timestamp',
    example: '2025-10-22T15:33:14+05:30',
  })
  event_time!: string;

  @ApiProperty({
    description: 'Event type',
    example: 'PAYMENT_SUCCESS_WEBHOOK',
  })
  type!: string;
}

export class DueTransactionDto {
  @ApiProperty({
    description: 'Transaction ID',
    example: 'txn-uuid-123',
  })
  id!: string;

  @ApiProperty({
    description: 'Driver ID',
    example: 'driver-uuid-123',
  })
  driverId!: string;

  @ApiProperty({
    description: 'City ID',
    example: 'city-uuid-123',
  })
  cityId!: string;

  @ApiProperty({
    description: 'Payment gateway transaction ID',
    example: 'cashfree_txn_123',
  })
  transactionId!: string;

  @ApiProperty({
    description: 'Payment amount in ₹',
    example: 500,
    type: 'number',
  })
  amount!: number;

  @ApiProperty({
    description: 'Payment method',
    example: 'CASHFREE',
  })
  paymentMethod!: string;

  @ApiProperty({
    description: 'Payment status',
    example: 'SUCCESS',
  })
  paymentStatus!: string;

  @ApiProperty({
    description: 'Balance before payment in ₹',
    example: -750,
    type: 'number',
  })
  balanceBefore!: number;

  @ApiProperty({
    description: 'Balance after payment in ₹',
    example: -250,
    type: 'number',
  })
  balanceAfter!: number;

  @ApiProperty({
    description: 'Transaction creation timestamp',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt!: string;

  @ApiProperty({
    description: 'Transaction update timestamp',
    example: '2024-01-01T00:05:00.000Z',
  })
  updatedAt!: string;
}

export class InitiateDuePaymentResponseDto {
  @ApiProperty({
    description: 'API response success status',
    example: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Payment initiated successfully',
  })
  message!: string;

  @ApiProperty({
    description: 'Payment initiation data',
    type: 'object',
    properties: {
      transactionId: { type: 'string', example: 'txn-uuid-123' },
      paymentLink: {
        type: 'string',
        example: 'https://cashfree.com/pay/...',
        nullable: true,
      },
      amount: { type: 'number', example: 500 },
    },
  })
  data!: {
    transactionId: string;
    paymentLink?: string | undefined;
    amount: number;
  };

  @ApiProperty({
    description: 'Response timestamp',
    example: *************,
  })
  timestamp!: number;
}

export class DueTransactionHistoryResponseDto {
  @ApiProperty({
    description: 'API response success status',
    example: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Transaction history retrieved successfully',
  })
  message!: string;

  @ApiProperty({
    description: 'List of transactions',
    type: [DueTransactionDto],
  })
  data!: DueTransactionDto[];

  @ApiProperty({
    description: 'Pagination metadata',
    type: 'object',
    properties: {
      page: { type: 'number', example: 1 },
      limit: { type: 'number', example: 10 },
      total: { type: 'number', example: 25 },
      totalPages: { type: 'number', example: 3 },
    },
  })
  meta!: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };

  @ApiProperty({
    description: 'Response timestamp',
    example: *************,
  })
  timestamp!: number;
}

export class DriverDueStatusDto {
  @ApiProperty({
    description: 'Whether driver due is exceeded',
    example: true,
  })
  isDueExceeded!: boolean;

  @ApiProperty({
    description: 'Current balance in ₹',
    example: -750,
    type: 'number',
  })
  currentBalance!: number;

  @ApiProperty({
    description: 'Due limit in ₹',
    example: 750,
    type: 'number',
  })
  dueLimit!: number;

  @ApiProperty({
    description: 'Remaining due amount in ₹',
    example: 0,
    type: 'number',
  })
  remainingDue!: number;

  @ApiProperty({
    description: 'Message for driver',
    example: 'Please clear your due amount to continue receiving rides.',
  })
  message!: string;

  @ApiProperty({
    description: 'List of transactions',
  })
  transactions?: DriverAccountTransaction[];
}

export class DriverBalanceWithDueStatusResponseDto {
  @ApiProperty({
    description: 'API response success status',
    example: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Driver balance retrieved successfully',
  })
  message!: string;

  @ApiProperty({
    description: 'Balance and due status data',
    type: 'object',
    properties: {
      balance: { type: 'number', example: -750 },
      dueStatus: { type: DriverDueStatusDto },
    },
  })
  data!: {
    balance: number;
    dueStatus: DriverDueStatusDto;
  };

  @ApiProperty({
    description: 'Response timestamp',
    example: *************,
  })
  timestamp!: number;
}

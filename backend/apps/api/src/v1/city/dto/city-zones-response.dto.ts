import { ApiProperty } from '@nestjs/swagger';
import { ZoneResponseDtoClass } from '@shared/shared/modules/zone/zone-response.dto';
import { ZoneResponseDto } from '@shared/shared/modules/zone/zone-response.dto';

export class CityZonesListResponseDto {
  @ApiProperty({
    description: 'Array of zones in the city',
    type: [ZoneResponseDtoClass],
  })
  data!: ZoneResponseDto[];
}

export class CityZonesApiResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'City zones fetched successfully' })
  message!: string;

  @ApiProperty({ type: [ZoneResponseDtoClass] })
  data!: ZoneResponseDto[];

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}

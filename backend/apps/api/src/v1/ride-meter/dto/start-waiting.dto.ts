import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsUUID } from 'class-validator';

/**
 * Enum for waiting meter types
 */
export enum WaitingMeterType {
  PICKUP_WAIT_TIME = 'pickup_wait_time',
  TRIP_WAIT_TIME = 'trip_wait_time',
}

/**
 * DTO for starting a waiting period
 */
export class StartWaitingDto {
  @ApiProperty({
    description: 'The ID of the ride',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  rideId!: string;

  @ApiProperty({
    description: 'The type of waiting meter to start',
    enum: WaitingMeterType,
    example: WaitingMeterType.PICKUP_WAIT_TIME,
  })
  @IsEnum(WaitingMeterType)
  meterType!: WaitingMeterType;
}

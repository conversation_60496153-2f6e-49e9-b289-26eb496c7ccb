import { Module } from '@nestjs/common';
import { CountryController } from './country.controller';
import { CountryModule as SharedCountryModule } from '@shared/shared/modules/country/country.module';
import { CityModule } from '@shared/shared/modules/city/city.module';
import { CityAdminRepository } from '@shared/shared/repositories/city-admin.repository';

@Module({
  imports: [SharedCountryModule, CityModule],
  providers: [CityAdminRepository],
  controllers: [CountryController],
})
export class ApiCountryModule {}

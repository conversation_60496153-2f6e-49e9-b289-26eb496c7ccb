import {
  Controller,
  Get,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
  Req,
  UnauthorizedException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { PermissionService } from '@shared/shared/modules/permission/permission.service';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import { PermissionListResponseDto } from './dto/permission-response.dto';
import { ApiErrorResponseDto } from '../../docs/swagger/common-responses.dto';

@ApiTags('Permissions')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('permissions')
export class PermissionsController {
  constructor(private readonly permissionService: PermissionService) {}

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get list of all permissions',
    description:
      "Retrieve all permissions in the system. If roleId is provided, includes isAddedRole field indicating whether each permission is assigned to the rider's role for that ride.",
  })
  @ApiQuery({
    name: 'roleId',
    description:
      "Optional ride ID to check permission status for the rider's role",
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Permissions retrieved successfully',
    type: PermissionListResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid query parameters',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Ride not found (when roleId is provided)',
    type: ApiErrorResponseDto,
  })
  async getPermissions(@Query('roleId') roleId?: string): Promise<any> {
    let permissions;

    permissions = await this.permissionService.getAllPermissions(roleId);

    return {
      success: true,
      message: 'Permissions retrieved successfully',
      data: permissions,
      timestamp: Date.now(),
    };
  }

  @Get('my-permissions')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get permissions assigned to the authenticated user via roles',
    description:
      'Retrieve all unique permissions for the current authenticated user based on their assigned roles.',
  })
  @ApiResponse({
    status: 200,
    description: 'User permissions retrieved successfully',
    type: [PermissionListResponseDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - User not authenticated',
    type: ApiErrorResponseDto,
  })
  async getMyPermissions(@Req() req: any): Promise<any> {
    const userId = (req.user as any)?.userId;
    if (!userId) {
      throw new UnauthorizedException('User not authenticated');
    }

    const permissions = await this.permissionService.getUserPermissions(userId);

    return {
      success: true,
      message: 'User permissions retrieved successfully',
      data: permissions,
      timestamp: Date.now(),
    };
  }
}

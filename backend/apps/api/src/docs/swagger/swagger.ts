import { INestApplication } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppConfigService } from '@shared/shared/config';

export function setupSwagger(app: INestApplication): void {
  const configService = app.get(AppConfigService);

  const config = new DocumentBuilder()
    .setTitle('Tukxi API')
    .setDescription('The Tukxi API documentation')
    .setVersion('1.0')
    .addServer(
      `http://localhost:${configService.coreApiPort}`,
      'Development server',
    )
    .addServer(`https://api.tukxi.com/api`, 'Production server')
    .addServer(`https://api.tukxi.alpha.logidots.com`, 'staging server')
    .addServer(`http://************:3000`, 'staging server 2')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth', // This name here is important for matching up with @ApiBearerAuth() in your controller!
    )
    .addApiKey(
      {
        type: 'apiKey',
        name: 'X-API-KEY',
        in: 'header',
        description: 'API Key for authentication',
      },
      'API-KEY',
    )
    .addTag('Health', 'Health check endpoints')
    .addTag('Users', 'User management endpoints')
    .addTag('Roles', 'Role management endpoints')
    .addTag('Permissions', 'Permission management endpoints')
    .addTag('Cities', 'City management endpoints')
    .addTag('Countries', 'Country management endpoints')
    .addTag(
      'Zones',
      'Zone management endpoints - Create, update, and manage geographic zones with H3 indexing',
    )
    .addTag(
      'Zone Types',
      'Zone type configuration endpoints - Define and configure different types of zones with algorithms',
    )
    .build();

  const document = SwaggerModule.createDocument(app, config, {
    operationIdFactory: (_controllerKey: string, methodKey: string) =>
      methodKey,
  });

  // Setup Swagger UI
  SwaggerModule.setup('docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      docExpansion: 'none',
      filter: true,
      showRequestDuration: true,
      tryItOutEnabled: true,
    },
    customSiteTitle: 'Tukxi API Documentation',
    customfavIcon: '/favicon.ico',
    customCss: `
      .swagger-ui .topbar { display: none; }
      .swagger-ui .info { margin: 20px 0; }
      .swagger-ui .info .title { color: #3b82f6; }
    `,
  });

  // Also setup JSON endpoint
  SwaggerModule.setup('docs-json', app, document);
}

model RideMeter {
  id     String @id @default(uuid()) @map("id") @db.Uuid
  rideId String @map("ride_id") @db.Uuid
  name   String @map("name") // The meter/metric name (e.g., "distance", "duration", "wait_time")
  value  Float  @map("value") // The measured value
  unit   String @map("unit") // The unit of measurement (e.g., "km", "minutes", "seconds")

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  ride          Ride           @relation(fields: [rideId], references: [id], onDelete: Cascade)
  rideMeterLogs RideMeterLog[] // Logs for ride meters

  @@index([rideId], name: "idx_ride_meter_ride_id")
  @@index([name], name: "idx_ride_meter_name")
  @@index([rideId, name], name: "idx_ride_meter_ride_name")
  @@index([createdAt], name: "idx_ride_meter_created_at")
  @@map("ride_meters")
}

-- CreateEnum
CREATE TYPE "CouponUsageStatus" AS ENUM ('APPLIED', 'CANCELLED', 'REFUNDED');

-- CreateTable
CREATE TABLE "coupon_usage" (
    "id" UUID NOT NULL,
    "coupon_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "ride_id" UUID NOT NULL,
    "original_fare" DECIMAL(10,2) NOT NULL,
    "discount_amount" DECIMAL(10,2) NOT NULL,
    "final_fare" DECIMAL(10,2) NOT NULL,
    "status" "CouponUsageStatus" NOT NULL DEFAULT 'APPLIED',
    "applied_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "cancelled_at" TIMESTAMPTZ,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "coupon_usage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_coupon_usage_limits" (
    "id" UUID NOT NULL,
    "coupon_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "usage_count" INTEGER NOT NULL DEFAULT 0,
    "usage_limit" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "user_coupon_usage_limits_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "idx_coupon_usage_coupon_id" ON "coupon_usage"("coupon_id");

-- CreateIndex
CREATE INDEX "idx_coupon_usage_user_id" ON "coupon_usage"("user_id");

-- CreateIndex
CREATE INDEX "idx_coupon_usage_ride_id" ON "coupon_usage"("ride_id");

-- CreateIndex
CREATE INDEX "idx_coupon_usage_status" ON "coupon_usage"("status");

-- CreateIndex
CREATE INDEX "idx_coupon_usage_applied_at" ON "coupon_usage"("applied_at");

-- CreateIndex
CREATE UNIQUE INDEX "coupon_usage_coupon_id_ride_id_key" ON "coupon_usage"("coupon_id", "ride_id");

-- CreateIndex
CREATE INDEX "idx_user_coupon_limit_coupon_id" ON "user_coupon_usage_limits"("coupon_id");

-- CreateIndex
CREATE INDEX "idx_user_coupon_limit_user_id" ON "user_coupon_usage_limits"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "user_coupon_usage_limits_coupon_id_user_id_key" ON "user_coupon_usage_limits"("coupon_id", "user_id");

-- AddForeignKey
ALTER TABLE "coupon_usage" ADD CONSTRAINT "coupon_usage_coupon_id_fkey" FOREIGN KEY ("coupon_id") REFERENCES "coupons"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "coupon_usage" ADD CONSTRAINT "coupon_usage_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user_profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "coupon_usage" ADD CONSTRAINT "coupon_usage_ride_id_fkey" FOREIGN KEY ("ride_id") REFERENCES "rides"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_coupon_usage_limits" ADD CONSTRAINT "user_coupon_usage_limits_coupon_id_fkey" FOREIGN KEY ("coupon_id") REFERENCES "coupons"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_coupon_usage_limits" ADD CONSTRAINT "user_coupon_usage_limits_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user_profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

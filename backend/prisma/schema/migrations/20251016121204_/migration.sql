-- CreateTable
CREATE TABLE "ride_meter_logs" (
    "id" UUID NOT NULL,
    "ride_id" UUID NOT NULL,
    "ride_meter_id" UUID NOT NULL,
    "from_time" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "to_time" TIMESTAMPTZ,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "ride_meter_logs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "idx_ride_meter_log_ride_id" ON "ride_meter_logs"("ride_id");

-- CreateIndex
CREATE INDEX "idx_ride_meter_log_ride_meter_id" ON "ride_meter_logs"("ride_meter_id");

-- AddForeignKey
ALTER TABLE "ride_meter_logs" ADD CONSTRAINT "ride_meter_logs_ride_id_fkey" FOREIGN KEY ("ride_id") REFERENCES "rides"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ride_meter_logs" ADD CONSTRAINT "ride_meter_logs_ride_meter_id_fkey" FOREIGN KEY ("ride_meter_id") REFERENCES "ride_meters"("id") ON DELETE CASCADE ON UPDATE CASCADE;

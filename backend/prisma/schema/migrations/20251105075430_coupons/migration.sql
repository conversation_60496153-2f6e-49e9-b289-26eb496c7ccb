-- Create<PERSON>num
CREATE TYPE "DiscountType" AS ENUM ('FLAT', 'PERCENTAGE');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "ApplicabilityType" AS ENUM ('CITY', 'PRODUCT', 'CITY_PRODUCT', 'USER');

-- CreateTable
CREATE TABLE "coupons" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "thumbnail" TEXT,
    "description" TEXT,
    "start_date" TIMESTAMPTZ NOT NULL,
    "end_date" TIMESTAMPTZ NOT NULL,
    "discount_type" "DiscountType" NOT NULL,
    "discount_value" DECIMAL(10,2) NOT NULL,
    "max_discount_limit" DECIMAL(10,2),
    "min_fare_condition" DECIMAL(10,2),
    "usage_limit" INTEGER NOT NULL,
    "usage_count" INTEGER NOT NULL DEFAULT 0,
    "applicability_type" "ApplicabilityType" NOT NULL,
    "apply_condition_logic" JSONB,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "coupons_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "coupon_zones" (
    "id" UUID NOT NULL,
    "coupon_id" UUID NOT NULL,
    "zone_id" UUID NOT NULL,

    CONSTRAINT "coupon_zones_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "coupon_products" (
    "id" UUID NOT NULL,
    "coupon_id" UUID NOT NULL,
    "product_id" UUID NOT NULL,

    CONSTRAINT "coupon_products_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "coupon_city_products" (
    "id" UUID NOT NULL,
    "coupon_id" UUID NOT NULL,
    "city_product_id" UUID NOT NULL,

    CONSTRAINT "coupon_city_products_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "coupon_users" (
    "id" UUID NOT NULL,
    "coupon_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,

    CONSTRAINT "coupon_users_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "coupons_code_key" ON "coupons"("code");

-- CreateIndex
CREATE INDEX "idx_coupon_code" ON "coupons"("code");

-- CreateIndex
CREATE INDEX "idx_coupon_applicability_type" ON "coupons"("applicability_type");

-- CreateIndex
CREATE INDEX "idx_coupon_is_active" ON "coupons"("is_active");

-- CreateIndex
CREATE INDEX "idx_coupon_date_range" ON "coupons"("start_date", "end_date");

-- CreateIndex
CREATE INDEX "idx_coupon_usage" ON "coupons"("usage_count", "usage_limit");

-- CreateIndex
CREATE INDEX "idx_coupon_zone_coupon_id" ON "coupon_zones"("coupon_id");

-- CreateIndex
CREATE INDEX "idx_coupon_zone_zone_id" ON "coupon_zones"("zone_id");

-- CreateIndex
CREATE UNIQUE INDEX "coupon_zones_coupon_id_zone_id_key" ON "coupon_zones"("coupon_id", "zone_id");

-- CreateIndex
CREATE INDEX "idx_coupon_product_coupon_id" ON "coupon_products"("coupon_id");

-- CreateIndex
CREATE INDEX "idx_coupon_product_product_id" ON "coupon_products"("product_id");

-- CreateIndex
CREATE UNIQUE INDEX "coupon_products_coupon_id_product_id_key" ON "coupon_products"("coupon_id", "product_id");

-- CreateIndex
CREATE INDEX "idx_coupon_city_product_coupon_id" ON "coupon_city_products"("coupon_id");

-- CreateIndex
CREATE INDEX "idx_coupon_city_product_city_product_id" ON "coupon_city_products"("city_product_id");

-- CreateIndex
CREATE UNIQUE INDEX "coupon_city_products_coupon_id_city_product_id_key" ON "coupon_city_products"("coupon_id", "city_product_id");

-- CreateIndex
CREATE INDEX "idx_coupon_user_coupon_id" ON "coupon_users"("coupon_id");

-- CreateIndex
CREATE INDEX "idx_coupon_user_user_id" ON "coupon_users"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "coupon_users_coupon_id_user_id_key" ON "coupon_users"("coupon_id", "user_id");

-- AddForeignKey
ALTER TABLE "coupon_zones" ADD CONSTRAINT "coupon_zones_coupon_id_fkey" FOREIGN KEY ("coupon_id") REFERENCES "coupons"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "coupon_zones" ADD CONSTRAINT "coupon_zones_zone_id_fkey" FOREIGN KEY ("zone_id") REFERENCES "zones"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "coupon_products" ADD CONSTRAINT "coupon_products_coupon_id_fkey" FOREIGN KEY ("coupon_id") REFERENCES "coupons"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "coupon_products" ADD CONSTRAINT "coupon_products_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "coupon_city_products" ADD CONSTRAINT "coupon_city_products_coupon_id_fkey" FOREIGN KEY ("coupon_id") REFERENCES "coupons"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "coupon_city_products" ADD CONSTRAINT "coupon_city_products_city_product_id_fkey" FOREIGN KEY ("city_product_id") REFERENCES "city_products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "coupon_users" ADD CONSTRAINT "coupon_users_coupon_id_fkey" FOREIGN KEY ("coupon_id") REFERENCES "coupons"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "coupon_users" ADD CONSTRAINT "coupon_users_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

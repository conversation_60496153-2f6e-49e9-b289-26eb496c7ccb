-- Create<PERSON>num
CREATE TYPE "payment_type" AS ENUM ('CASH', 'ONLINE');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "transaction_type" AS ENUM ('CREDIT', 'DEBIT');

-- CreateTable
CREATE TABLE "driver_accounts" (
    "id" UUID NOT NULL,
    "driver_id" UUID NOT NULL,
    "available_balance" DECIMAL(10,2) NOT NULL DEFAULT 0.0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "driver_accounts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "driver_account_transactions" (
    "id" UUID NOT NULL,
    "driver_id" UUID NOT NULL,
    "ride_id" UUID,
    "amount" DECIMAL(10,2) NOT NULL,
    "transaction_type" "transaction_type" NOT NULL,
    "reason" TEXT NOT NULL,
    "balance_after" DECIMAL(10,2) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "driver_account_transactions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "payments" (
    "id" UUID NOT NULL,
    "ride_id" UUID NOT NULL,
    "rider_id" UUID NOT NULL,
    "driver_id" UUID NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL,
    "payment_type" "payment_type",
    "received_at" TIMESTAMPTZ,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "payments_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "driver_accounts_driver_id_key" ON "driver_accounts"("driver_id");

-- CreateIndex
CREATE INDEX "idx_driver_account_driver_id" ON "driver_accounts"("driver_id");

-- CreateIndex
CREATE INDEX "idx_driver_account_balance" ON "driver_accounts"("available_balance");

-- CreateIndex
CREATE INDEX "idx_driver_account_transaction_driver_id" ON "driver_account_transactions"("driver_id");

-- CreateIndex
CREATE INDEX "idx_driver_account_transaction_ride_id" ON "driver_account_transactions"("ride_id");

-- CreateIndex
CREATE INDEX "idx_driver_account_transaction_type" ON "driver_account_transactions"("transaction_type");

-- CreateIndex
CREATE INDEX "idx_driver_account_transaction_created_at" ON "driver_account_transactions"("created_at");

-- CreateIndex
CREATE UNIQUE INDEX "payments_ride_id_key" ON "payments"("ride_id");

-- CreateIndex
CREATE INDEX "idx_payment_ride_id" ON "payments"("ride_id");

-- CreateIndex
CREATE INDEX "idx_payment_rider_id" ON "payments"("rider_id");

-- CreateIndex
CREATE INDEX "idx_payment_driver_id" ON "payments"("driver_id");

-- CreateIndex
CREATE INDEX "idx_payment_type" ON "payments"("payment_type");

-- CreateIndex
CREATE INDEX "idx_payment_received_at" ON "payments"("received_at");

-- AddForeignKey
ALTER TABLE "driver_accounts" ADD CONSTRAINT "driver_accounts_driver_id_fkey" FOREIGN KEY ("driver_id") REFERENCES "user_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "driver_account_transactions" ADD CONSTRAINT "driver_account_transaction_driver_fkey" FOREIGN KEY ("driver_id") REFERENCES "user_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "driver_account_transactions" ADD CONSTRAINT "driver_account_transactions_ride_id_fkey" FOREIGN KEY ("ride_id") REFERENCES "rides"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "driver_account_transactions" ADD CONSTRAINT "driver_account_transactions_driver_id_fkey" FOREIGN KEY ("driver_id") REFERENCES "driver_accounts"("driver_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payments" ADD CONSTRAINT "payments_ride_id_fkey" FOREIGN KEY ("ride_id") REFERENCES "rides"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payments" ADD CONSTRAINT "payments_rider_id_fkey" FOREIGN KEY ("rider_id") REFERENCES "user_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payments" ADD CONSTRAINT "payments_driver_id_fkey" FOREIGN KEY ("driver_id") REFERENCES "user_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

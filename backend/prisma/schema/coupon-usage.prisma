// Coupon usage tracking models

model CouponUsage {
  id       String @id @default(uuid()) @db.Uuid
  couponId String @map("coupon_id") @db.Uuid
  userId   String @map("user_id") @db.Uuid
  rideId   String @map("ride_id") @db.Uuid

  // Discount information
  originalFare   Decimal @map("original_fare") @db.Decimal(10, 2)
  discountAmount Decimal @map("discount_amount") @db.Decimal(10, 2)
  finalFare      Decimal @map("final_fare") @db.Decimal(10, 2)

  // Status tracking
  status      CouponUsageStatus @default(APPLIED) @map("status")
  appliedAt   DateTime          @default(now()) @map("applied_at")
  cancelledAt DateTime?         @map("cancelled_at") @db.Timestamptz

  // Metadata
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  coupon Coupon      @relation(fields: [couponId], references: [id])
  user   UserProfile @relation(fields: [userId], references: [id])
  ride   Ride        @relation(fields: [rideId], references: [id])

  @@unique([couponId, rideId], name: "unique_coupon_per_ride")
  @@index([couponId], name: "idx_coupon_usage_coupon_id")
  @@index([userId], name: "idx_coupon_usage_user_id")
  @@index([rideId], name: "idx_coupon_usage_ride_id")
  @@index([status], name: "idx_coupon_usage_status")
  @@index([appliedAt], name: "idx_coupon_usage_applied_at")
  @@map("coupon_usage")
}

model UserCouponUsageLimit {
  id         String @id @default(uuid()) @db.Uuid
  couponId   String @map("coupon_id") @db.Uuid
  userId     String @map("user_id") @db.Uuid
  usageCount Int    @default(0) @map("usage_count")
  usageLimit Int?   @map("usage_limit") // Per-user limit (optional)

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  coupon Coupon      @relation(fields: [couponId], references: [id])
  user   UserProfile @relation(fields: [userId], references: [id])

  @@unique([couponId, userId], name: "unique_user_coupon_limit")
  @@index([couponId], name: "idx_user_coupon_limit_coupon_id")
  @@index([userId], name: "idx_user_coupon_limit_user_id")
  @@map("user_coupon_usage_limits")
}

enum CouponUsageStatus {
  APPLIED
  CANCELLED
  REFUNDED
}

model DriverEarnings {
  id                   String    @id @default(uuid()) @map("id") @db.Uuid
  driverId             String    @map("driver_id") @db.Uuid
  earningsDate         DateTime  @map("earnings_date") @db.Date
  totalFareAmount      Decimal   @default(0.0) @map("total_fare_amount") @db.Decimal(10, 2)
  completedRides       Int       @default(0) @map("completed_rides")
  totalTaxesOnCharge   Decimal   @default(0.0) @map("total_taxes_on_charge") @db.Decimal(10, 2)
  totalCommission      Decimal   @default(0.0) @map("total_commission") @db.Decimal(10, 2)
  totalTaxOnCommission Decimal   @default(0.0) @map("total_tax_on_commission") @db.Decimal(10, 2)
  createdAt            DateTime  @default(now()) @map("created_at")
  updatedAt            DateTime  @updatedAt @map("updated_at")
  deletedAt            DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  driver UserProfile @relation("DriverEarnings", fields: [driverId], references: [id], onDelete: Cascade)

  @@unique([driverId, earningsDate], name: "unique_driver_earnings_date")
  @@index([driverId], name: "idx_driver_earnings_driver_id")
  @@index([earningsDate], name: "idx_driver_earnings_date")
  @@index([driverId, earningsDate], name: "idx_driver_earnings_driver_date")
  @@map("driver_earnings")
}

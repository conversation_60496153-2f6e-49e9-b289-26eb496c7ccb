model RideFare {
  id                String  @id @default(uuid()) @map("id") @db.Uuid
  rideId            String  @map("ride_id") @db.Uuid
  chargeId          String? @map("charge_id") @db.Uuid
  fare              Decimal @map("fare") @db.Decimal(10, 2) // Individual fare component
  taxId             String? @map("tax_id") @db.Uuid
  commissionId      String? @map("commission_id") @db.Uuid
  totalFare         Decimal @map("total_fare") @db.Decimal(10, 2) // Final calculated fare
  currency          String  @default("INR") @map("currency")
  cityProductId     String? @map("city_product_id") @db.Uuid
  cityProductFareId String? @map("city_product_fare_id") @db.Uuid

  // Fare breakdown details
  subtotal         Decimal @map("subtotal") @db.Decimal(10, 2) // Sum of all charges before taxes
  totalTaxes       Decimal @map("total_taxes") @db.Decimal(10, 2) // Sum of all taxes
  totalCommissions Decimal @map("total_commissions") @db.Decimal(10, 2) // Sum of all commissions
  passengerFare    Decimal @map("passenger_fare") @db.Decimal(10, 2) // What customer pays
  driverEarnings   Decimal @map("driver_earnings") @db.Decimal(10, 2) // What driver receives
  platformRevenue  Decimal @map("platform_revenue") @db.Decimal(10, 2) // What platform earns

  // Calculation metadata
  calculatedAt  DateTime @map("calculated_at") @db.Timestamptz
  fareBreakdown Json?    @map("fare_breakdown") @db.JsonB // Detailed breakdown

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  ride            Ride             @relation(fields: [rideId], references: [id], onDelete: Cascade)
  charge          Charge?          @relation(fields: [chargeId], references: [id], onDelete: SetNull)
  cityProduct     CityProduct?     @relation(fields: [cityProductId], references: [id], onDelete: SetNull)
  cityProductFare CityProductFare? @relation(fields: [cityProductFareId], references: [id], onDelete: SetNull)

  @@index([rideId], name: "idx_ride_fare_ride_id")
  @@index([chargeId], name: "idx_ride_fare_charge_id")
  @@index([taxId], name: "idx_ride_fare_tax_id")
  @@index([commissionId], name: "idx_ride_fare_commission_id")
  @@index([cityProductId], name: "idx_ride_fare_city_product_id")
  @@index([cityProductFareId], name: "idx_ride_fare_city_product_fare_id")
  @@index([calculatedAt], name: "idx_ride_fare_calculated_at")
  @@index([createdAt], name: "idx_ride_fare_created_at")
  @@map("ride_fares")
}

model RiderContact {
  id          String    @id @default(uuid()) @map("id") @db.Uuid
  riderId     String    @map("rider_id") @db.Uuid
  name        String?   @map("name")
  phoneNumber String?   @map("phone_number")
  email       String?   @map("email")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at") @db.Timestamptz

  @@map("rider_contacts")
}

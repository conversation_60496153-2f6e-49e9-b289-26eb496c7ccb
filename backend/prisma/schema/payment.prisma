model Payment {
  id          String       @id @default(uuid()) @map("id") @db.Uuid
  rideId      String       @unique @map("ride_id") @db.Uuid
  riderId     String       @map("rider_id") @db.Uuid
  driverId    String       @map("driver_id") @db.Uuid
  amount      Decimal      @map("amount") @db.Decimal(10, 2)
  paymentType PaymentType? @map("payment_type")
  receivedAt  DateTime?    @map("received_at") @db.Timestamptz
  createdAt   DateTime     @default(now()) @map("created_at")
  updatedAt   DateTime     @updatedAt @map("updated_at")
  deletedAt   DateTime?    @map("deleted_at") @db.Timestamptz

  // Relations
  ride   Ride        @relation(fields: [rideId], references: [id], onDelete: Cascade)
  rider  UserProfile @relation("PaymentRider", fields: [riderId], references: [id], onDelete: Cascade)
  driver UserProfile @relation("PaymentDriver", fields: [driverId], references: [id], onDelete: Cascade)

  @@index([rideId], name: "idx_payment_ride_id")
  @@index([riderId], name: "idx_payment_rider_id")
  @@index([driverId], name: "idx_payment_driver_id")
  @@index([paymentType], name: "idx_payment_type")
  @@index([receivedAt], name: "idx_payment_received_at")
  @@map("payments")
}

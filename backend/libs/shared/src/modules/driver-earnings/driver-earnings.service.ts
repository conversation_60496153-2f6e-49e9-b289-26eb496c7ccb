import { Injectable, Logger } from '@nestjs/common';
import { DriverEarningsRepository } from '../../repositories/driverEarnings.repository';
import {
  DriverEarnings,
  DriverEarningsAggregation,
  DailyEarnings,
} from '../../repositories/models/driverEarnings.model';

export interface CreateEarningsData {
  driverId: string;
  rideId: string;
  fareAmount: number;
  completedAt: Date;
  totalTaxesOnCharge?: number;
  totalCommission?: number;
  totalTaxOnCommission?: number;
}

export interface EarningsQueryOptions {
  fromDate?: Date | undefined;
  toDate?: Date | undefined;
  page?: number | undefined;
  limit?: number | undefined;
}

@Injectable()
export class DriverEarningsService {
  private readonly logger = new Logger(DriverEarningsService.name);

  constructor(
    private readonly driverEarningsRepository: DriverEarningsRepository,
  ) {}

  /**
   * Record earnings when a ride is completed
   */
  async recordRideEarnings(data: CreateEarningsData): Promise<DriverEarnings> {
    this.logger.log(
      `Recording earnings for driver ${data.driverId}, ride ${data.rideId}, amount: ${data.fareAmount}`,
    );

    try {
      const earnings = await this.driverEarningsRepository.upsertDriverEarnings(
        data.driverId,
        data.completedAt,
        data.fareAmount,
        1, // One completed ride
        data.totalTaxesOnCharge || 0,
        data.totalCommission || 0,
        data.totalTaxOnCommission || 0,
      );

      this.logger.log(
        `Successfully recorded earnings for driver ${data.driverId} on ${data.completedAt.toISOString().split('T')[0]}`,
      );

      return earnings;
    } catch (error) {
      this.logger.error(
        `Failed to record earnings for driver ${data.driverId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get driver earnings for a specific date range
   */
  async getDriverEarnings(
    driverId: string,
    options: EarningsQueryOptions = {},
  ): Promise<{
    earnings: DailyEarnings[];
    total: number;
    totalPages: number;
    page: number;
    limit: number;
  }> {
    const { fromDate, toDate, page = 1, limit = 10 } = options;

    this.logger.log(
      `Getting earnings for driver ${driverId} from ${fromDate?.toISOString()} to ${toDate?.toISOString()}`,
    );

    return this.driverEarningsRepository.getDailyEarningsWithPagination(
      driverId,
      page,
      limit,
      fromDate,
      toDate,
    );
  }

  /**
   * Get aggregated earnings summary for a driver
   */
  async getDriverEarningsSummary(
    driverId: string,
    fromDate?: Date,
    toDate?: Date,
  ): Promise<DriverEarningsAggregation | null> {
    this.logger.log(
      `Getting earnings summary for driver ${driverId} from ${fromDate?.toISOString()} to ${toDate?.toISOString()}`,
    );

    return this.driverEarningsRepository.getDriverEarningsAggregation(
      driverId,
      fromDate,
      toDate,
    );
  }

  /**
   * Get earnings for a specific date
   */
  async getEarningsForDate(
    driverId: string,
    date: Date,
  ): Promise<DriverEarnings | null> {
    this.logger.log(
      `Getting earnings for driver ${driverId} on ${date.toISOString().split('T')[0]}`,
    );

    return this.driverEarningsRepository.findEarningsByDate(driverId, date);
  }

  /**
   * Get top earning drivers
   */
  async getTopEarningDrivers(
    limit: number = 10,
    fromDate?: Date,
    toDate?: Date,
  ): Promise<DriverEarningsAggregation[]> {
    this.logger.log(
      `Getting top ${limit} earning drivers from ${fromDate?.toISOString()} to ${toDate?.toISOString()}`,
    );

    return this.driverEarningsRepository.getTopEarningDrivers(
      limit,
      fromDate,
      toDate,
    );
  }

  /**
   * Get earnings statistics for a driver
   */
  async getDriverEarningsStats(
    driverId: string,
    fromDate?: Date,
    toDate?: Date,
  ): Promise<{
    totalEarnings: number;
    totalRides: number;
    averageEarningsPerRide: number;
    averageEarningsPerDay: number;
    bestDay: { date: Date; earnings: number } | null;
    worstDay: { date: Date; earnings: number } | null;
    daysWorked: number;
  }> {
    this.logger.log(`Getting earnings statistics for driver ${driverId}`);

    const [aggregation, dailyEarnings] = await Promise.all([
      this.getDriverEarningsSummary(driverId, fromDate, toDate),
      this.driverEarningsRepository.findDriverEarningsByDateRange(
        driverId,
        fromDate,
        toDate,
      ),
    ]);

    if (!aggregation || dailyEarnings.length === 0) {
      return {
        totalEarnings: 0,
        totalRides: 0,
        averageEarningsPerRide: 0,
        averageEarningsPerDay: 0,
        bestDay: null,
        worstDay: null,
        daysWorked: 0,
      };
    }

    // Find best and worst days
    let bestDay: { date: Date; earnings: number } | null = null;
    let worstDay: { date: Date; earnings: number } | null = null;

    for (const earning of dailyEarnings) {
      const earningsAmount = Number(earning.totalFareAmount);

      if (!bestDay || earningsAmount > bestDay.earnings) {
        bestDay = { date: earning.earningsDate, earnings: earningsAmount };
      }

      if (!worstDay || earningsAmount < worstDay.earnings) {
        worstDay = { date: earning.earningsDate, earnings: earningsAmount };
      }
    }

    const daysWorked = dailyEarnings.length;
    const averageEarningsPerDay =
      daysWorked > 0 ? aggregation.totalEarnings / daysWorked : 0;

    return {
      totalEarnings: aggregation.totalEarnings,
      totalRides: aggregation.totalRides,
      averageEarningsPerRide: aggregation.averageEarningsPerRide,
      averageEarningsPerDay,
      bestDay,
      worstDay,
      daysWorked,
    };
  }

  /**
   * Validate date range
  //  */
  // private validateDateRange(fromDate?: Date, toDate?: Date): void {
  //   if (fromDate && toDate && fromDate > toDate) {
  //     throw new Error('From date cannot be greater than to date');
  //   }
  // }

  /**
   * Get aggregated earnings for all drivers
   */
  async getAggregatedEarnings(
    page: number = 1,
    limit: number = 10,
    fromDate?: Date,
    toDate?: Date,
    driverId?: string,
    cityId?: string,
    phoneNumber?: string,
  ): Promise<{
    data: any[];
    total: number;
    totalPages: number;
    page: number;
    limit: number;
  }> {
    this.logger.log(
      `Getting aggregated earnings from ${fromDate?.toISOString()} to ${toDate?.toISOString()}`,
    );

    return this.driverEarningsRepository.getAggregatedDriverEarnings(
      page,
      limit,
      fromDate,
      toDate,
      driverId,
      cityId,
      phoneNumber,
    );
  }

  /**
   * Get daily earnings for a specific driver
   */
  async getDailyEarningsForDriver(
    driverId: string,
    page: number = 1,
    limit: number = 10,
    fromDate?: Date,
    toDate?: Date,
  ): Promise<{
    data: any[];
    total: number;
    totalPages: number;
    page: number;
    limit: number;
  }> {
    this.logger.log(
      `Getting daily earnings for driver ${driverId} from ${fromDate?.toISOString()} to ${toDate?.toISOString()}`,
    );

    return this.driverEarningsRepository.getDailyEarningsForDriver(
      driverId,
      page,
      limit,
      fromDate,
      toDate,
    );
  }

  /**
   * Parse date string to Date object
   */
  parseDate(dateString: string): Date {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      throw new Error(`Invalid date format: ${dateString}`);
    }
    return date;
  }

  /**
   * Get date range for common periods
   */
  getDateRangeForPeriod(period: 'today' | 'week' | 'month' | 'year'): {
    fromDate: Date;
    toDate: Date;
  } {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    switch (period) {
      case 'today':
        return {
          fromDate: today,
          toDate: new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1),
        };

      case 'week':
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        return {
          fromDate: weekStart,
          toDate: now,
        };

      case 'month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        return {
          fromDate: monthStart,
          toDate: now,
        };

      case 'year':
        const yearStart = new Date(today.getFullYear(), 0, 1);
        return {
          fromDate: yearStart,
          toDate: now,
        };

      default:
        throw new Error(`Invalid period: ${period}`);
    }
  }
}

import { Module } from '@nestjs/common';
import { DriverEarningsService } from './driver-earnings.service';
import { DriverEarningsRepository } from '../../repositories/driverEarnings.repository';
import { PrismaModule } from '../../database/prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  providers: [DriverEarningsService, DriverEarningsRepository],
  exports: [DriverEarningsService, DriverEarningsRepository],
})
export class DriverEarningsModule {}

import { Module } from '@nestjs/common';
import { RideMeterService } from './ride-meter.service';
import { RideMeterLogService } from '../ride/services/ride-meter-log.service';
import { RideMeterRepository } from '@shared/shared/repositories/ride-meter.repository';
import { RideMeterLogRepository } from '@shared/shared/repositories/ride-meter-log.repository';
import { RideRepository } from '@shared/shared/repositories/ride.repository';
import { PrismaModule } from '@shared/shared/database/prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  providers: [
    RideMeterService,
    RideMeterLogService,
    RideMeterRepository,
    RideMeterLogRepository,
    RideRepository,
  ],
  exports: [
    RideMeterService,
    RideMeterLogService,
    RideMeterRepository,
    RideMeterLogRepository,
  ],
})
export class RideMeterModule {}

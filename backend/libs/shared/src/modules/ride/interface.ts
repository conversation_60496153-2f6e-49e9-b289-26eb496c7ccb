import { DriverMetadata } from '@shared/shared/repositories/models/redis/driverLocation.model';
import { Product } from '@shared/shared/repositories/models/product.model';
import { ProductService as ProductServiceModel } from '@shared/shared/repositories/models/productService.model';
import { VehicleType } from '@shared/shared/repositories/models/vehicleType.model';
import { CityProduct } from '@shared/shared/repositories/models/cityProduct.model';

// Enums for better type safety
export enum ServiceType {
  INTERCITY = 'intercity',
  CITY_RENTAL = 'city_rental',
  CITY_RIDE = 'city_ride',
}

export enum RideBookingType {
  NOW = 'now',
  LATER = 'later',
}

// Type alias for backward compatibility with controller
export type RideSearchType = RideBookingType;

// Input/Output interfaces
export interface SearchRideParams {
  pickup: {
    lat: number;
    lng: number;
  };
  destination: {
    lat: number;
    lng: number;
  };
  pickupType?: RideBookingType | undefined;
  pickupTime?: string | undefined;
  stops?: { lat: number; lng: number }[] | undefined;
  couponCode?: string | undefined;
  userId?: string;
}

export interface RideSearchResult {
  id: string;
  name: string;
  identifier: string;
  serviceName: string;
  description?: string | null;
  icon?: string | null;
  price: number; // Calculated fare from fare engine
  strikethroughPrice: number; // Default value
  passengerLimit: number; // Default value 2
  pickupToDestinationResult: {
    durationInSeconds: number | null; // Default 0
    distanceMeters: number | null; // Default 0
  };
  driverToPickupResults: {
    durationInSeconds: number | null; // Default 0
    distanceMeters: number | null; // Default 0
    estimatedArrivalTime: string | null; // now() + seconds(driverToPickupResults.durationInSeconds), default null if no driver data
  };
  driverToDestinationResults: {
    durationInSeconds: number; // pickupToDestinationResult.durationInSeconds + driverToPickupResults.durationInSeconds
    distanceMeters: number; // pickupToDestinationResult.distanceMeters + driverToPickupResults.distanceMeters
    estimatedArrivalTime: string | null; // now() + seconds(driverToDestinationResults.durationInSeconds)
  };
  fare?: {
    amount: number;
    currency: string;
    cityProductId: string;
    cityProductFareId?: string;
  } | null; // Fare calculation result
  cityProduct?: {
    id: string;
    cityId: string;
    productId: string;
  } | null; // City product information
  couponDiscount?: {
    couponCode: string;
    discountAmount: number;
    originalPrice: number;
    discountedPrice: number;
    discountType: string;
    discountValue: number;
  } | null; // Coupon discount information (if applicable)
  couponMessage?: string | null; // Coupon validation message (success or error)
}

// Internal service types
export interface ProductWithRelations extends Product {
  productService?: ProductServiceModel;
  vehicleType?: VehicleType;
}

export interface NearbyDriver {
  driverId: string;
  metadata: DriverMetadata | null;
}

export interface DriverRouteData {
  driverId: string;
  etaToPickupSeconds: number;
  distanceToPickupMeters: number;
  estimatedArrivalTime: string;
  productId?: string | undefined; // Optional, can be undefined if not available
}

export interface RouteComputationResult {
  distanceMeters?: number | undefined;
  duration?: number | undefined;
}

export interface ZoneDetectionResult {
  serviceTypes: ServiceType[];
  cityIds?: string[] | undefined;
}

export interface ProductDriverMatch {
  product: ProductWithRelations;
  driverData?: DriverRouteData | undefined;
  cityProduct?: CityProduct | null;
  fare?: {
    amount: number;
    currency: string;
    cityProductId: string;
    cityProductFareId?: string;
  } | null;
}

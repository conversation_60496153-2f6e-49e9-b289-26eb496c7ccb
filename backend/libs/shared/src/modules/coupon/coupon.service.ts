import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { ApplicabilityType } from '@prisma/client';
import {
  CouponFilterOptions,
  CouponListResult,
  CouponRepository,
} from '@shared/shared/repositories/coupon.repository';
import {
  Coupon,
  CouponApplicabilityData,
  CouponUsageInfo,
  CouponValidationResult,
  DiscountType,
} from '@shared/shared/repositories/models/coupon.model';

export interface CreateCouponData {
  name: string;
  code: string;
  thumbnail?: string;
  description?: string;
  startDate: Date;
  endDate: Date;
  discountType: DiscountType;
  discountValue: number;
  maxDiscountLimit?: number;
  minFareCondition?: number;
  usageLimit: number;
  applicabilityType: ApplicabilityType;
  applyConditionLogic?: any;
  isActive?: boolean;
}

@Injectable()
export class CouponService {
  private readonly logger = new Logger(CouponService.name);

  constructor(private readonly couponRepository: CouponRepository) {}

  /**
   * Create a new coupon
   */
  async createCoupon(
    couponData: CreateCouponData,
    applicabilityData: CouponApplicabilityData,
  ): Promise<Coupon> {
    try {
      this.logger.log(`Creating coupon with code: ${couponData.code}`);

      // Validate coupon data
      const validation = this.validateCouponData(couponData, applicabilityData);
      if (!validation.isValid) {
        throw new BadRequestException(validation.errors.join(', '));
      }

      // Check if code already exists
      const existingCoupon = await this.couponRepository.findCouponByCode(
        couponData.code,
      );
      if (existingCoupon) {
        throw new ConflictException(
          `Coupon with code '${couponData.code}' already exists`,
        );
      }

      // Create coupon
      const coupon = await this.couponRepository.createCoupon(
        {
          ...couponData,
          isActive: couponData.isActive ?? true,
        },
        applicabilityData,
      );

      this.logger.log(`Coupon created successfully with ID: ${coupon.id}`);
      return coupon;
    } catch (error) {
      this.logger.error('Error creating coupon:', error);

      // Handle Prisma unique constraint violation
      if (
        error instanceof PrismaClientKnownRequestError &&
        error.code === 'P2002'
      ) {
        const target = error?.meta?.['target'] as string[];
        if (target && target.includes('code')) {
          throw new ConflictException(
            `Coupon with code '${couponData.code}' already exists`,
          );
        }
        throw new ConflictException(
          'A coupon with these details already exists',
        );
      }

      // Re-throw other errors as-is
      throw error;
    }
  }

  /**
   * Update an existing coupon
   */
  async updateCoupon(
    id: string,
    couponData: Partial<CreateCouponData>,
    applicabilityData?: CouponApplicabilityData,
  ): Promise<Coupon> {
    this.logger.log(`Updating coupon with ID: ${id}`);

    // Check if coupon exists
    const existingCoupon = await this.couponRepository.findCouponById(id);
    if (!existingCoupon) {
      throw new NotFoundException(`Coupon with ID ${id} not found`);
    }

    // Validate updated data
    if (
      couponData.code ||
      couponData.startDate ||
      couponData.endDate ||
      applicabilityData
    ) {
      const mergedData = { ...existingCoupon, ...couponData };
      const validation = this.validateCouponData(
        mergedData as CreateCouponData,
        applicabilityData,
      );
      if (!validation.isValid) {
        throw new BadRequestException(validation.errors.join(', '));
      }
    }

    // Check code uniqueness if code is being updated
    if (couponData.code && couponData.code !== existingCoupon.code) {
      const codeExists = await this.couponRepository.codeExists(
        couponData.code,
        id,
      );
      if (codeExists) {
        throw new ConflictException(
          `Coupon with code '${couponData.code}' already exists`,
        );
      }
    }

    // Update coupon
    try {
      const updatedCoupon = await this.couponRepository.updateCoupon(
        id,
        couponData,
        applicabilityData,
      );

      if (!updatedCoupon) {
        throw new NotFoundException(`Coupon with ID ${id} not found`);
      }

      this.logger.log(`Coupon updated successfully with ID: ${id}`);
      return updatedCoupon;
    } catch (error) {
      this.logger.error(`Error updating coupon with ID ${id}:`, error);

      // Handle Prisma unique constraint violation
      if (
        error instanceof PrismaClientKnownRequestError &&
        error.code === 'P2002'
      ) {
        const target = error?.meta?.['target'] as string[];
        if (target && target.includes('code')) {
          throw new ConflictException(
            `Coupon with code '${couponData.code}' already exists`,
          );
        }
        throw new ConflictException(
          'A coupon with these details already exists',
        );
      }

      // Re-throw other errors as-is
      throw error;
    }
  }

  /**
   * Get coupon by ID
   */
  async getCouponById(id: string): Promise<Coupon> {
    const coupon = await this.couponRepository.findCouponById(id);
    if (!coupon) {
      throw new NotFoundException(`Coupon with ID ${id} not found`);
    }
    return coupon;
  }

  /**
   * Get coupon by code
   */
  async getCouponByCode(code: string): Promise<Coupon> {
    const coupon = await this.couponRepository.findCouponByCode(code);
    if (!coupon) {
      throw new NotFoundException(`Coupon with code '${code}' not found`);
    }
    return coupon;
  }

  /**
   * List coupons with filtering and pagination
   */
  async listCoupons(options: CouponFilterOptions): Promise<CouponListResult> {
    return this.couponRepository.listCoupons(options);
  }

  /**
   * Delete a coupon (soft delete)
   */
  async deleteCoupon(id: string): Promise<Coupon> {
    const coupon = await this.couponRepository.softDeleteCoupon(id);
    if (!coupon) {
      throw new NotFoundException(`Coupon with ID ${id} not found`);
    }

    this.logger.log(`Coupon soft deleted with ID: ${id}`);
    return coupon;
  }

  /**
   * Toggle coupon active status
   */
  async toggleCouponStatus(id: string): Promise<Coupon> {
    const coupon = await this.couponRepository.toggleActiveStatus(id);
    if (!coupon) {
      throw new NotFoundException(`Coupon with ID ${id} not found`);
    }

    this.logger.log(
      `Coupon status toggled for ID: ${id}, new status: ${coupon.isActive}`,
    );
    return coupon;
  }

  /**
   * Get coupon usage information
   */
  async getCouponUsageInfo(id: string): Promise<CouponUsageInfo> {
    const coupon = await this.getCouponById(id);
    const now = new Date();

    return {
      couponId: coupon.id,
      usageCount: coupon.usageCount,
      usageLimit: coupon.usageLimit,
      remainingUsage: Math.max(0, coupon.usageLimit - coupon.usageCount),
      isExpired: coupon.endDate < now,
      isActive: coupon.isActive,
    };
  }

  /**
   * Check if coupon is applicable for given context
   */
  async isCouponApplicable(
    couponId: string,
    context: {
      userId?: string;
      zoneId?: string;
      productId?: string;
      cityProductId?: string;
      fareAmount?: number;
    },
  ): Promise<{ applicable: boolean; reason?: string }> {
    const coupon = await this.getCouponById(couponId);
    const now = new Date();

    // Check basic conditions
    if (!coupon.isActive) {
      return { applicable: false, reason: 'Coupon is not active' };
    }

    if (coupon.startDate > now) {
      return { applicable: false, reason: 'Coupon is not yet active' };
    }

    if (coupon.endDate < now) {
      return { applicable: false, reason: 'Coupon has expired' };
    }

    if (coupon.usageCount >= coupon.usageLimit) {
      return { applicable: false, reason: 'Coupon usage limit reached' };
    }

    // Check minimum fare condition
    if (
      coupon.minFareCondition &&
      context.fareAmount &&
      context.fareAmount < coupon.minFareCondition
    ) {
      return {
        applicable: false,
        reason: `Minimum fare of ${coupon.minFareCondition} required`,
      };
    }

    // Check applicability based on type
    const applicabilityCheck = await this.checkApplicabilityRules(
      coupon,
      context,
    );
    if (!applicabilityCheck.applicable) {
      return applicabilityCheck;
    }

    // Check JSONLogic conditions if present
    if (coupon.applyConditionLogic) {
      const logicResult = this.evaluateJsonLogic(
        coupon.applyConditionLogic,
        context,
      );
      if (!logicResult) {
        return { applicable: false, reason: 'Custom conditions not met' };
      }
    }

    return { applicable: true };
  }

  /**
   * Calculate discount amount for a coupon
   */
  calculateDiscountAmount(coupon: Coupon, fareAmount: number): number {
    let discountAmount = 0;

    if (coupon.discountType === DiscountType.FLAT) {
      discountAmount = coupon.discountValue;
    } else if (coupon.discountType === DiscountType.PERCENTAGE) {
      discountAmount = (fareAmount * coupon.discountValue) / 100;
    }

    // Apply maximum discount limit if set
    if (coupon.maxDiscountLimit && discountAmount > coupon.maxDiscountLimit) {
      discountAmount = coupon.maxDiscountLimit;
    }

    // Ensure discount doesn't exceed fare amount
    return Math.min(discountAmount, fareAmount);
  }

  /**
   * Process coupon usage (increment usage count)
   */
  async processCouponUsage(couponId: string): Promise<Coupon> {
    const coupon = await this.couponRepository.incrementUsageCount(couponId);
    if (!coupon) {
      throw new NotFoundException(`Coupon with ID ${couponId} not found`);
    }

    this.logger.log(
      `Coupon usage processed for ID: ${couponId}, new count: ${coupon.usageCount}`,
    );
    return coupon;
  }

  /**
   * Get expired coupons for cleanup
   */
  async getExpiredCoupons(): Promise<Coupon[]> {
    return this.couponRepository.getExpiredCoupons();
  }

  /**
   * Validate coupon data
   */
  private validateCouponData(
    couponData: CreateCouponData,
    applicabilityData?: CouponApplicabilityData,
  ): CouponValidationResult {
    const errors: string[] = [];

    // Validate dates are valid Date objects
    const startDate = new Date(couponData.startDate);
    const endDate = new Date(couponData.endDate);

    // Validate date range
    if (startDate >= endDate) {
      errors.push('Start date must be before end date');
    }

    // Validate discount value
    if (couponData.discountValue <= 0) {
      errors.push('Discount value must be greater than 0');
    }

    if (
      couponData.discountType === DiscountType.PERCENTAGE &&
      couponData.discountValue > 100
    ) {
      errors.push('Percentage discount cannot exceed 100%');
    }

    // Validate usage limit
    if (couponData.usageLimit <= 0) {
      errors.push('Usage limit must be greater than 0');
    }

    // Validate max discount limit for percentage discounts
    if (
      couponData.discountType === DiscountType.PERCENTAGE &&
      !couponData.maxDiscountLimit
    ) {
      errors.push(
        'Maximum discount limit is recommended for percentage discounts',
      );
    }

    // Validate applicability data
    if (applicabilityData) {
      const applicabilityErrors =
        this.validateApplicabilityData(applicabilityData);
      errors.push(...applicabilityErrors);

      // Check that required IDs are provided based on applicability type
      switch (applicabilityData.type) {
        case ApplicabilityType.CITY:
          if (
            !applicabilityData.zoneIds ||
            applicabilityData.zoneIds.length === 0
          ) {
            errors.push('Zone IDs are required for city-specific coupons');
          }
          break;
        case ApplicabilityType.PRODUCT:
          if (
            !applicabilityData.productIds ||
            applicabilityData.productIds.length === 0
          ) {
            errors.push(
              'Product IDs are required for product-specific coupons',
            );
          }
          break;
        case ApplicabilityType.CITY_PRODUCT:
          if (
            !applicabilityData.cityProductIds ||
            applicabilityData.cityProductIds.length === 0
          ) {
            errors.push(
              'City Product IDs are required for city-product-specific coupons',
            );
          }
          break;
        case ApplicabilityType.USER:
          if (
            !applicabilityData.userIds ||
            applicabilityData.userIds.length === 0
          ) {
            errors.push('User IDs are required for user-specific coupons');
          }
          break;
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate applicability data
   */
  private validateApplicabilityData(
    applicabilityData: CouponApplicabilityData,
  ): string[] {
    const errors: string[] = [];
    const { type, zoneIds, productIds, cityProductIds, userIds } =
      applicabilityData;

    switch (type) {
      case ApplicabilityType.CITY:
        if (!zoneIds || zoneIds.length === 0) {
          errors.push(
            'At least one zone must be selected for city-specific coupons',
          );
        }
        break;

      case ApplicabilityType.PRODUCT:
        if (!productIds || productIds.length === 0) {
          errors.push(
            'At least one product must be selected for product-specific coupons',
          );
        }
        break;

      case ApplicabilityType.CITY_PRODUCT:
        if (!cityProductIds || cityProductIds.length === 0) {
          errors.push('At least one city-product combination must be selected');
        }
        break;

      case ApplicabilityType.USER:
        if (!userIds || userIds.length === 0) {
          errors.push(
            'At least one user must be selected for user-specific coupons',
          );
        }
        break;
    }

    return errors;
  }

  /**
   * Check applicability rules based on coupon type
   */
  private async checkApplicabilityRules(
    coupon: Coupon,
    context: {
      userId?: string;
      zoneId?: string;
      productId?: string;
      cityProductId?: string;
    },
  ): Promise<{ applicable: boolean; reason?: string }> {
    switch (coupon.applicabilityType) {
      case ApplicabilityType.CITY:
        if (!context.zoneId) {
          return { applicable: false, reason: 'Zone information required' };
        }
        const hasZone = coupon.couponZones?.some(
          (cz) => cz.zoneId === context.zoneId,
        );
        if (!hasZone) {
          return {
            applicable: false,
            reason: 'Coupon not applicable for this zone',
          };
        }
        break;

      case ApplicabilityType.PRODUCT:
        if (!context.productId) {
          return { applicable: false, reason: 'Product information required' };
        }
        const hasProduct = coupon.couponProducts?.some(
          (cp) => cp.productId === context.productId,
        );
        if (!hasProduct) {
          return {
            applicable: false,
            reason: 'Coupon not applicable for this product',
          };
        }
        break;

      case ApplicabilityType.CITY_PRODUCT:
        if (!context.cityProductId) {
          return {
            applicable: false,
            reason: 'City-product information required',
          };
        }
        const hasCityProduct = coupon.couponCityProducts?.some(
          (ccp) => ccp.cityProductId === context.cityProductId,
        );
        if (!hasCityProduct) {
          return {
            applicable: false,
            reason: 'Coupon not applicable for this city-product combination',
          };
        }
        break;

      case ApplicabilityType.USER:
        if (!context.userId) {
          return { applicable: false, reason: 'User information required' };
        }
        const hasUser = coupon.couponUsers?.some(
          (cu) => cu.userId === context.userId,
        );
        if (!hasUser) {
          return {
            applicable: false,
            reason: 'Coupon not applicable for this user',
          };
        }
        break;
    }

    return { applicable: true };
  }

  /**
   * Evaluate JSONLogic conditions (placeholder implementation)
   */
  private evaluateJsonLogic(_logic: any, _context: any): boolean {
    // This is a placeholder implementation
    // In a real implementation, you would use a JSONLogic library
    // like 'json-logic-js' to evaluate the conditions
    this.logger.debug('JSONLogic evaluation not implemented, returning true');
    return true;
  }
}

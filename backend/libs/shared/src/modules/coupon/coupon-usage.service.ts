import { Injectable, Logger } from '@nestjs/common';
import { CouponUsageRepository } from '@shared/shared/repositories/coupon-usage.repository';
import { CouponRepository } from '@shared/shared/repositories/coupon.repository';
import {
  CouponUsage,
  CouponUsageStats,
  UserCouponUsageInfo,
} from '@shared/shared/repositories/models/couponUsage.model';

export interface ApplyCouponUsageData {
  couponId: string;
  userId: string;
  rideId: string;
  originalFare: number;
  discountAmount: number;
  finalFare: number;
}

@Injectable()
export class CouponUsageService {
  private readonly logger = new Logger(CouponUsageService.name);

  constructor(
    private readonly couponUsageRepository: CouponUsageRepository,
    private readonly couponRepository: CouponRepository,
  ) {}

  /**
   * Apply coupon usage for a ride request
   * This increments both global and user-specific usage counts
   */
  async applyCouponUsage(data: ApplyCouponUsageData): Promise<CouponUsage> {
    this.logger.log(`Applying coupon usage for ride ${data.rideId}`);

    try {
      // Start transaction-like operations
      // 1. Increment global coupon usage count
      await this.couponRepository.incrementUsageCount(data.couponId);

      // 2. Increment user-specific usage count
      await this.couponUsageRepository.incrementUserUsageCount(
        data.couponId,
        data.userId,
      );

      // 3. Create coupon usage record
      const couponUsage = await this.couponUsageRepository.createCouponUsage({
        couponId: data.couponId,
        userId: data.userId,
        rideId: data.rideId,
        originalFare: data.originalFare,
        discountAmount: data.discountAmount,
        finalFare: data.finalFare,
      });

      this.logger.log(
        `Coupon usage applied successfully for ride ${data.rideId}`,
      );
      return couponUsage;
    } catch (error) {
      this.logger.error(
        `Error applying coupon usage for ride ${data.rideId}:`,
        error,
      );

      // TODO: Implement proper rollback mechanism
      // For now, we'll let the error propagate
      throw error;
    }
  }

  /**
   * Cancel coupon usage for a cancelled ride
   * This decrements both global and user-specific usage counts
   */
  async cancelCouponUsage(rideId: string): Promise<boolean> {
    this.logger.log(`Cancelling coupon usage for ride ${rideId}`);

    try {
      // Find the coupon usage record
      const couponUsage = await this.couponUsageRepository.findByRideId(rideId);
      if (!couponUsage) {
        this.logger.warn(`No coupon usage found for ride ${rideId}`);
        return false;
      }

      // Only cancel if it's currently applied
      if (couponUsage.status !== 'APPLIED') {
        this.logger.warn(
          `Coupon usage for ride ${rideId} is already ${couponUsage.status}`,
        );
        return false;
      }

      // 1. Cancel the coupon usage record
      await this.couponUsageRepository.cancelCouponUsage(rideId);

      // 2. Decrement global coupon usage count
      await this.couponRepository.decrementUsageCount(couponUsage.couponId);

      // 3. Decrement user-specific usage count
      await this.couponUsageRepository.decrementUserUsageCount(
        couponUsage.couponId,
        couponUsage.userId,
      );

      this.logger.log(`Coupon usage cancelled successfully for ride ${rideId}`);
      return true;
    } catch (error) {
      this.logger.error(
        `Error cancelling coupon usage for ride ${rideId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get coupon usage by ride ID
   */
  async getCouponUsageByRideId(rideId: string): Promise<CouponUsage | null> {
    return this.couponUsageRepository.findByRideId(rideId);
  }

  /**
   * Get user's coupon usage history
   */
  async getUserCouponUsageHistory(
    userId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ usages: CouponUsage[]; total: number }> {
    return this.couponUsageRepository.findByUserId(userId, page, limit);
  }

  /**
   * Get coupon usage statistics
   */
  async getCouponUsageStats(couponId: string): Promise<CouponUsageStats> {
    return this.couponUsageRepository.getCouponUsageStats(couponId);
  }

  /**
   * Get user's usage information for a specific coupon
   */
  async getUserCouponUsageInfo(
    couponId: string,
    userId: string,
  ): Promise<UserCouponUsageInfo> {
    return this.couponUsageRepository.getUserCouponUsageInfo(couponId, userId);
  }

  /**
   * Check if user can use a coupon (based on user-specific limits)
   */
  async canUserUseCoupon(couponId: string, userId: string): Promise<boolean> {
    const usageInfo = await this.getUserCouponUsageInfo(couponId, userId);
    return usageInfo.canUseAgain;
  }

  /**
   * Update coupon usage with ride ID
   */
  async updateCouponUsageRideId(
    couponUsageId: string,
    rideId: string,
  ): Promise<void> {
    try {
      await this.couponUsageRepository.updateCouponUsageRideId(
        couponUsageId,
        rideId,
      );
      this.logger.log(
        `Updated coupon usage ${couponUsageId} with ride ID ${rideId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error updating coupon usage ${couponUsageId} with ride ID ${rideId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Update coupon usage amounts (for recalculation at ride completion)
   */
  async updateCouponUsageAmounts(
    couponUsageId: string,
    amounts: {
      originalFare: number;
      discountAmount: number;
      finalFare: number;
    },
  ): Promise<void> {
    try {
      await this.couponUsageRepository.updateCouponUsageAmounts(
        couponUsageId,
        amounts,
      );
      this.logger.log(
        `Updated coupon usage ${couponUsageId} with recalculated amounts: ` +
          `original=${amounts.originalFare}, discount=${amounts.discountAmount}, final=${amounts.finalFare}`,
      );
    } catch (error) {
      this.logger.error(
        `Error updating coupon usage amounts for ${couponUsageId}:`,
        error,
      );
      throw error;
    }
  }
}

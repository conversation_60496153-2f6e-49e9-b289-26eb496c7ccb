import { Injectable, Logger } from '@nestjs/common';
import { Coupon } from '@shared/shared/repositories/models/coupon.model';

export interface CouponDiscountResult {
  couponCode: string;
  originalFare: number;
  discountAmount: number;
  finalFare: number;
  discountType: string;
  discountValue: number;
}

@Injectable()
export class CouponDiscountService {
  private readonly logger = new Logger(CouponDiscountService.name);

  /**
   * Calculate coupon discount based on fare amount
   */
  calculateDiscount(
    coupon: Coupon,
    originalFare: number,
  ): CouponDiscountResult {
    let discountAmount = 0;

    if (coupon.discountType === 'FLAT') {
      discountAmount = coupon.discountValue;
    } else if (coupon.discountType === 'PERCENTAGE') {
      discountAmount = (originalFare * coupon.discountValue) / 100;

      // Apply max discount limit if specified
      if (coupon.maxDiscountLimit && discountAmount > coupon.maxDiscountLimit) {
        discountAmount = coupon.maxDiscountLimit;
      }
    }

    // Ensure discount doesn't exceed the fare
    discountAmount = Math.min(discountAmount, originalFare);
    discountAmount = Number(discountAmount.toFixed(2));

    const finalFare = Number((originalFare - discountAmount).toFixed(2));

    const result: CouponDiscountResult = {
      couponCode: coupon.code,
      originalFare,
      discountAmount,
      finalFare,
      discountType: coupon.discountType,
      discountValue: coupon.discountValue,
    };

    this.logger.debug(`🎫 COUPON DISCOUNT CALCULATED:`, {
      couponCode: coupon.code,
      discountType: coupon.discountType,
      discountValue: coupon.discountValue,
      originalFare,
      discountAmount,
      finalFare,
    });

    return result;
  }
}

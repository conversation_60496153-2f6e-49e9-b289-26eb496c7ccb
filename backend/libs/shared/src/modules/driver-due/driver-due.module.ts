import { Module } from '@nestjs/common';
import { DriverDueService } from './driver-due-service';
import {
  DriverDueConfigRepository,
  DriverDuePaymentTransactionRepository,
} from '@shared/shared/repositories/driver-due.repository';
import { PrismaService } from '@shared/shared/database/prisma/prisma.service';
import { DriverAccountRepository } from '@shared/shared/repositories';

@Module({
  providers: [
    DriverDueService,
    DriverDueConfigRepository,
    DriverDuePaymentTransactionRepository,
    DriverAccountRepository,
    PrismaService,
  ],
  exports: [
    DriverDueService,
    DriverDueConfigRepository,
    DriverDuePaymentTransactionRepository,
  ],
})
export class DriverDueModule {}

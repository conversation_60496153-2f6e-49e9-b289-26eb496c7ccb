import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { AppConfigService } from '@shared/shared/config';
import axios, { AxiosResponse } from 'axios';

export interface InitiateDuePaymentRequest {
  transactionId: string;
  driverId: string;
  amount: number;
  customerEmail?: string;
  customerPhone?: string;
}

export interface DuePaymentResponse {
  transactionId: string;
  paymentLink?: string;
  data: any;
  status: string;
  message: string;
}

@Injectable()
export class CashfreeDuePaymentService {
  private readonly logger = new Logger(CashfreeDuePaymentService.name);
  private readonly baseUrl: string;
  private readonly clientId: string;
  private readonly clientSecret: string;
  private readonly apiVersion: string;
  private readonly notifyUrl: string;

  constructor(private readonly appConfigService: AppConfigService) {
    this.baseUrl =
      this.appConfigService.cashfreeEnvironment === 'production'
        ? 'https://api.cashfree.com/pg'
        : 'https://api.cashfree.com/pg';

    this.clientId = this.appConfigService.cashfreePaymentClientId || '';
    this.clientSecret = this.appConfigService.cashfreePaymentSecret || '';
    this.apiVersion = this.appConfigService.cashfreeApiVersion || '';
    this.notifyUrl = this.appConfigService.cashfreePaymentNotifyUrl || '';

    if (!this.clientId || !this.clientSecret) {
      this.logger.warn(
        'Cashfree credentials not configured. Payment gateway will not work.',
      );
    }
  }

  /**
   * Initiate a due payment via Cashfree
   */
  async initiateDuePayment(
    request: InitiateDuePaymentRequest,
  ): Promise<DuePaymentResponse> {
    this.logger.log(
      `Initiating due payment for driver ${request.driverId}: ₹${request.amount}`,
    );

    try {
      const paymentData = {
        order_id: request.transactionId,
        order_amount: request.amount,
        order_currency: 'INR',
        customer_details: {
          customer_id: request.driverId,
          customer_email: request.customerEmail || '<EMAIL>',
          customer_phone: request.customerPhone || '9999999999',
        },
        order_note: `Driver Due Payment - ${request.driverId}`,
        order_meta: {
          notify_url: this.notifyUrl,
        },
      };

      const headers = {
        'X-Client-Id': this.clientId,
        'X-Client-Secret': this.clientSecret,
        'X-Api-version': this.apiVersion,
        'Content-Type': 'application/json',
      };

      this.logger.debug(
        `Cashfree payment request: ${JSON.stringify(paymentData)}`,
      );

      const response: AxiosResponse = await axios.post(
        `${this.baseUrl}/orders`,
        paymentData,
        { headers, timeout: 30000 },
      );

      this.logger.log(
        `Payment initiated successfully. Order ID: ${response.data.order_id}`,
      );

      // console.log({ response })
      return {
        transactionId: response.data.order_id,
        paymentLink: response.data.payment_link,
        data: response.data,
        status: 'INITIATED',
        message: 'Payment initiated successfully',
      };
    } catch (error: any) {
      this.logger.error('Cashfree payment initiation error:', {
        message: error?.message,
        status: error?.response?.status,
        data: error?.response?.data,
      });

      throw new BadRequestException(
        `Failed to initiate payment: ${error?.response?.data?.message || error?.message}`,
      );
    }
  }

  /**
   * Verify payment status from Cashfree
   */
  async verifyPaymentStatus(transactionId: string): Promise<{
    status: string;
    amount: number;
    paymentMethod?: string;
  }> {
    this.logger.log(
      `Verifying payment status for transaction ${transactionId}`,
    );

    try {
      const headers = {
        'X-Client-Id': this.clientId,
        'X-Client-Secret': this.clientSecret,
        'X-Api-version': this.apiVersion,
        'Content-Type': 'application/json',
      };

      const response: AxiosResponse = await axios.get(
        `${this.baseUrl}/orders/${transactionId}`,
        { headers, timeout: 30000 },
      );

      this.logger.log(`Payment status verified: ${response.data.order_status}`);
      // console.log({ response })
      return {
        status: response.data.order_status,
        amount: response.data.order_amount,
        paymentMethod: response.data.payment_method,
      };
    } catch (error: any) {
      this.logger.error('Payment verification error:', {
        message: error?.message,
        status: error?.response?.status,
        data: error?.response?.data,
      });

      throw new BadRequestException(
        `Failed to verify payment: ${error?.response?.data?.message || error?.message}`,
      );
    }
  }

  /**
   * Handle payment webhook/callback
   */
  async handlePaymentCallback(
    transactionId: string,
    paymentStatus: string,
  ): Promise<{
    success: boolean;
    message: string;
  }> {
    this.logger.log(
      `Processing payment callback for transaction ${transactionId}: ${paymentStatus}`,
    );

    try {
      // Verify the payment status with Cashfree
      const verification = await this.verifyPaymentStatus(transactionId);

      if (verification.status !== 'SETTLED' && verification.status !== 'PAID') {
        return {
          success: false,
          message: `Payment not settled. Status: ${verification.status}`,
        };
      }

      this.logger.log(
        `Payment callback processed successfully for ${transactionId}`,
      );

      return {
        success: true,
        message: 'Payment processed successfully',
      };
    } catch (error: any) {
      this.logger.error('Payment callback processing error:', error);

      return {
        success: false,
        message: `Error processing payment: ${error?.message}`,
      };
    }
  }
}

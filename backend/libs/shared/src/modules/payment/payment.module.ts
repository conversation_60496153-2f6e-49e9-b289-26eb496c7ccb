import { Module } from '@nestjs/common';
import { PaymentService } from './payment.service';
import { CashfreeDuePaymentService } from './cashfree-due-payment.service';
import { PrismaModule } from '@shared/shared/database/prisma/prisma.module';
import { AppConfigModule } from '@shared/shared/config';
import { PaymentRepository } from '@shared/shared/repositories';

@Module({
  imports: [PrismaModule, AppConfigModule],
  providers: [PaymentService, PaymentRepository, CashfreeDuePaymentService],
  exports: [PaymentService, PaymentRepository, CashfreeDuePaymentService],
})
export class PaymentModule {}

import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import {
  RiderContactRepository,
  RiderContactPaginationResult,
} from '@shared/shared/repositories/rider-contact.repository';
import { RiderContact } from '@shared/shared/repositories/models/riderContact.model';

export interface CreateRiderContactData {
  riderId: string;
  name?: string | null;
  phoneNumber?: string | null;
  email?: string | null;
}

export interface UpdateRiderContactData {
  name?: string | null;
  phoneNumber?: string | null;
  email?: string | null;
}

export interface RiderContactListQuery {
  page?: number;
  limit?: number;
  search?: string;
}

@Injectable()
export class RiderContactService {
  constructor(
    private readonly riderContactRepository: RiderContactRepository,
  ) {}

  /**
   * Create a new rider contact
   */
  async createContact(
    data: CreateRiderContactData,
    authenticatedRiderId: string,
  ): Promise<RiderContact> {
    // Ensure the authenticated user can only create contacts for themselves
    if (data.riderId !== authenticatedRiderId) {
      throw new ForbiddenException('You can only create contacts for yourself');
    }

    // Validate that at least one contact method is provided
    if (!data.name && !data.phoneNumber && !data.email) {
      throw new BadRequestException(
        'At least one contact field (name, phoneNumber, or email) must be provided',
      );
    }
    // Check for existing contact with the same phone number for this rider
    const existingContact = await this.riderContactRepository.findOne({
      where: {
        riderId: data.riderId,
        phoneNumber: data.phoneNumber,
      },
    });
    if (existingContact) {
      throw new BadRequestException(
        'Contact with this phone number already exists',
      );
    }

    return this.riderContactRepository.createRiderContact({
      riderId: data.riderId,
      name: data.name || null,
      phoneNumber: data.phoneNumber || null,
      email: data.email || null,
    });
  }

  /**
   * Get paginated contacts for authenticated rider
   */
  async getContactsList(
    riderId: string,
    query: RiderContactListQuery,
  ): Promise<RiderContactPaginationResult> {
    const { page = 1, limit = 10, search } = query;

    return this.riderContactRepository.getPaginatedContacts(
      riderId,
      page,
      limit,
      search,
    );
  }

  /**
   * Get contact by ID (with authorization check)
   */
  async getContactById(
    contactId: string,
    authenticatedRiderId: string,
  ): Promise<RiderContact> {
    const contact = await this.riderContactRepository.findContactByIdAndRiderId(
      contactId,
      authenticatedRiderId,
    );

    if (!contact) {
      throw new NotFoundException('Contact not found');
    }

    return contact;
  }

  /**
   * Update contact (with authorization check)
   */
  async updateContact(
    contactId: string,
    data: UpdateRiderContactData,
    authenticatedRiderId: string,
  ): Promise<RiderContact> {
    // Check if contact exists and belongs to the authenticated rider
    const existingContact =
      await this.riderContactRepository.findContactByIdAndRiderId(
        contactId,
        authenticatedRiderId,
      );

    if (!existingContact) {
      throw new NotFoundException('Contact not found');
    }

    // Validate that at least one field is being updated
    if (!data.name && !data.phoneNumber && !data.email) {
      throw new Error('At least one field must be provided for update');
    }

    return this.riderContactRepository.updateRiderContact(
      contactId,
      authenticatedRiderId,
      data,
    );
  }

  /**
   * Delete contact (with authorization check)
   */
  async deleteContact(
    contactId: string,
    authenticatedRiderId: string,
  ): Promise<RiderContact> {
    // Check if contact exists and belongs to the authenticated rider
    const existingContact =
      await this.riderContactRepository.findContactByIdAndRiderId(
        contactId,
        authenticatedRiderId,
      );

    if (!existingContact) {
      throw new NotFoundException('Contact not found');
    }

    return this.riderContactRepository.deleteRiderContact(
      contactId,
      authenticatedRiderId,
    );
  }

  /**
   * Get all contacts for a rider (without pagination)
   */
  async getAllContactsByRiderId(riderId: string): Promise<RiderContact[]> {
    return this.riderContactRepository.findContactsByRiderId(riderId);
  }

  /**
   * Get contact count for a rider
   */
  async getContactCount(riderId: string): Promise<number> {
    return this.riderContactRepository.countContactsByRiderId(riderId);
  }
}

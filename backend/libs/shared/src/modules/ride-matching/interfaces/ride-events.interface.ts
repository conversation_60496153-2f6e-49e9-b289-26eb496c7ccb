/**
 * WebSocket and RabbitMQ event interfaces for ride-matching
 */

export interface WebSocketRideStatusUpdateEvent {
  rideId: string;
  riderId?: string;
  status: string;
  message: string;
  timestamp: string;
  metadata?: any;
  correlationId?: string;
  driver?: any;
}

export interface WebSocketDriverOfferEvent {
  driverId: string;
  offerId: string;
  rideId: string;
  riderId: string;
  pickupLocation: {
    lat: number;
    lng: number;
    address?: string;
  };
  destinationLocation: {
    lat: number;
    lng: number;
    address?: string;
  };
  fareEstimate?: any;
  fareSpec?: any;
  estimatedDuration?: number;
  expiresAt: string;
  correlationId: string;
  batchNumber: any;
  etaToPickup?: any;
  totalBatches?: any;
  stops?: any[];
  distanceMeters?: number;
  passengerLimit?: number;
  duration?: number;
  distance?: number;
}

export interface WebSocketDriverOfferTimeoutEvent {
  driverId: string;
  offerId: string;
  rideId: string;
  correlationId: string;
}

export interface RideMatchingEvent {
  eventType: string;
  timestamp: string;
  data: any;
  metadata: {
    source: string;
    version: string;
    correlationId: string;
  };
}

import { Module } from '@nestjs/common';
import { DriverAccountService } from './driver-account.service';
import {
  DriverAccountRepository,
  DriverAccountTransactionRepository,
} from '../../repositories/driver-account.repository';
import { PrismaModule } from '../../database/prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  providers: [
    DriverAccountService,
    DriverAccountRepository,
    DriverAccountTransactionRepository,
  ],
  exports: [
    DriverAccountService,
    DriverAccountRepository,
    DriverAccountTransactionRepository,
  ],
})
export class DriverAccountModule {}

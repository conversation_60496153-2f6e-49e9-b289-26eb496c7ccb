import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';

export interface DriverDueConfig {
  id: string;
  cityId: string;
  maxDueLimit: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;
}

export interface DriverDuePaymentTransaction {
  id: string;
  driverId: string;
  cityId: string;
  transactionId: string;
  amount: number;
  paymentMethod: string;
  paymentStatus: string;
  balanceBefore: number;
  balanceAfter: number;
  metadata?: any;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;
}

export interface CreateDriverDueConfigData {
  maxDueLimit: number;
  isActive?: boolean;
}

export interface UpdateDriverDueConfigData {
  maxDueLimit?: number;
  isActive?: boolean;
}

export interface CreateDriverDuePaymentTransactionData {
  driverId: string;
  cityId: string;
  transactionId: string;
  amount: number;
  paymentMethod: string;
  paymentStatus: string;
  balanceBefore: number;
  balanceAfter: number;
  metadata?: any;
}

@Injectable()
export class DriverDueConfigRepository extends BaseRepository<DriverDueConfig> {
  protected readonly modelName = 'driverDueConfig';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Find due config by city ID
   */
  async findByCityId(cityId: string): Promise<DriverDueConfig | null> {
    try {
      return await this.findOne({
        where: { cityId },
      });
    } catch (error) {
      throw new Error(
        `Failed to find due config for city ${cityId}: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Create or update due config for a city
   */
  async upsertDueConfig(
    cityId: string,
    data: CreateDriverDueConfigData,
  ): Promise<DriverDueConfig> {
    try {
      return await this.upsert(
        { cityId },
        {
          cityId,
          maxDueLimit: data.maxDueLimit,
          isActive: data.isActive ?? true,
        },
        {
          maxDueLimit: data.maxDueLimit,
          isActive: data.isActive ?? true,
        },
      );
    } catch (error) {
      throw new Error(
        `Failed to upsert due config for city ${cityId}: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Update due config
   */
  async updateDueConfig(
    cityId: string,
    data: UpdateDriverDueConfigData,
  ): Promise<DriverDueConfig> {
    try {
      return await this.update({
        where: { cityId },
        data,
      });
    } catch (error) {
      throw new Error(
        `Failed to update due config for city ${cityId}: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Get all active due configs
   */
  async findAllActive(): Promise<DriverDueConfig[]> {
    return this.findMany({
      where: { isActive: true },
    });
  }
}

@Injectable()
export class DriverDuePaymentTransactionRepository extends BaseRepository<DriverDuePaymentTransaction> {
  protected readonly modelName = 'driverDuePaymentTransaction';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new due payment transaction
   */
  async createTransaction(
    data: CreateDriverDuePaymentTransactionData,
  ): Promise<DriverDuePaymentTransaction> {
    return this.create(data);
  }

  /**
   * Find transaction by transaction ID
   */
  async findByTransactionId(
    transactionId: string,
  ): Promise<DriverDuePaymentTransaction | null> {
    return this.findOne({
      where: { transactionId },
    });
  }

  /**
   * Find transactions by driver ID with pagination
   */
  async findByDriverId(
    driverId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ transactions: DriverDuePaymentTransaction[]; total: number }> {
    const skip = (page - 1) * limit;

    const [transactions, total] = await Promise.all([
      this.findMany({
        where: { driverId },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      this.count({ where: { driverId } }),
    ]);

    return { transactions, total };
  }

  /**
   * Find successful transactions by driver ID
   */
  async findSuccessfulByDriverId(
    driverId: string,
  ): Promise<DriverDuePaymentTransaction[]> {
    return this.findMany({
      where: {
        driverId,
        paymentStatus: 'SUCCESS',
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Update transaction status
   */
  async updateTransactionStatus(
    transactionId: string,
    paymentStatus: string,
    balanceAfter?: number,
  ): Promise<DriverDuePaymentTransaction> {
    try {
      const updateData: Partial<DriverDuePaymentTransaction> = {
        paymentStatus,
      };
      if (balanceAfter !== undefined) {
        updateData.balanceAfter = balanceAfter;
      }

      return await this.update({
        where: { transactionId },
        data: updateData,
      });
    } catch (error) {
      throw new Error(
        `Failed to update transaction status for transaction ${transactionId}: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }
}

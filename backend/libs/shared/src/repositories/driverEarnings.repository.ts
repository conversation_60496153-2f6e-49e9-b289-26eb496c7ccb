import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import {
  DriverEarnings,
  DriverEarningsAggregation,
  DailyEarnings,
} from './models/driverEarnings.model';
import { PrismaService } from '../database/prisma/prisma.service';

@Injectable()
export class DriverEarningsRepository extends BaseRepository<DriverEarnings> {
  protected readonly modelName = 'driverEarnings';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create or update driver earnings for a specific date
   */
  async upsertDriverEarnings(
    driverId: string,
    earningsDate: Date,
    fareAmount: number,
    rideCount: number = 1,
    taxesOnCharge: number = 0,
    totalCommission: number = 0,
    taxOnCommission: number = 0,
  ): Promise<DriverEarnings> {
    const dateOnly = new Date(earningsDate.toISOString().split('T')[0]);

    return this.prisma.driverEarnings.upsert({
      where: {
        unique_driver_earnings_date: {
          driverId,
          earningsDate: dateOnly,
        },
      },
      update: {
        totalFareAmount: {
          increment: fareAmount,
        },
        completedRides: {
          increment: rideCount,
        },
        totalTaxesOnCharge: {
          increment: taxesOnCharge,
        },
        totalCommission: {
          increment: totalCommission,
        },
        totalTaxOnCommission: {
          increment: taxOnCommission,
        },
      },
      create: {
        driverId,
        earningsDate: dateOnly,
        totalFareAmount: fareAmount,
        completedRides: rideCount,
        totalTaxesOnCharge: taxesOnCharge,
        totalCommission: totalCommission,
        totalTaxOnCommission: taxOnCommission,
      },
      include: {
        driver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });
  }

  /**
   * Find driver earnings by driver ID and date range
   */
  async findDriverEarningsByDateRange(
    driverId: string,
    fromDate?: Date,
    toDate?: Date,
  ): Promise<DriverEarnings[]> {
    const whereClause: any = { driverId };

    if (fromDate || toDate) {
      whereClause.earningsDate = {};
      if (fromDate) whereClause.earningsDate.gte = fromDate;
      if (toDate) whereClause.earningsDate.lte = toDate;
    }

    return this.findMany({
      where: whereClause,
      orderBy: { earningsDate: 'desc' },
      include: {
        driver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });
  }

  /**
   * Get aggregated earnings for a driver
   */
  async getDriverEarningsAggregation(
    driverId: string,
    fromDate?: Date,
    toDate?: Date,
  ): Promise<DriverEarningsAggregation | null> {
    const whereClause: any = { driverId };

    if (fromDate || toDate) {
      whereClause.earningsDate = {};
      if (fromDate) whereClause.earningsDate.gte = fromDate;
      if (toDate) whereClause.earningsDate.lte = toDate;
    }

    const result = await this.prisma.driverEarnings.aggregate({
      where: whereClause,
      _sum: {
        totalFareAmount: true,
        completedRides: true,
      },
    });

    if (!result._sum.totalFareAmount || !result._sum.completedRides) {
      return null;
    }

    const totalEarnings = Number(result._sum.totalFareAmount);
    const totalRides = result._sum.completedRides;

    return {
      driverId,
      totalEarnings,
      totalRides,
      averageEarningsPerRide: totalRides > 0 ? totalEarnings / totalRides : 0,
      dateRange: {
        from: fromDate || new Date(0),
        to: toDate || new Date(),
      },
    };
  }

  /**
   * Get daily earnings with pagination
   */
  async getDailyEarningsWithPagination(
    driverId: string,
    page: number = 1,
    limit: number = 10,
    fromDate?: Date,
    toDate?: Date,
  ): Promise<{
    earnings: DailyEarnings[];
    total: number;
    totalPages: number;
    page: number;
    limit: number;
  }> {
    const skip = (page - 1) * limit;
    const whereClause: any = { driverId };

    if (fromDate || toDate) {
      whereClause.earningsDate = {};
      if (fromDate) whereClause.earningsDate.gte = fromDate;
      if (toDate) whereClause.earningsDate.lte = toDate;
    }

    const [earningsData, total] = await Promise.all([
      this.findMany({
        where: whereClause,
        orderBy: { earningsDate: 'desc' },
        skip,
        take: limit,
      }),
      this.count(whereClause),
    ]);

    const earnings: DailyEarnings[] = earningsData.map((earning) => ({
      date: earning.earningsDate,
      totalFareAmount: Number(earning.totalFareAmount),
      completedRides: earning.completedRides,
      averageEarningsPerRide:
        earning.completedRides > 0
          ? Number(earning.totalFareAmount) / earning.completedRides
          : 0,
    }));

    return {
      earnings,
      total,
      totalPages: Math.ceil(total / limit),
      page,
      limit,
    };
  }

  /**
   * Find earnings for a specific date
   */
  async findEarningsByDate(
    driverId: string,
    date: Date,
  ): Promise<DriverEarnings | null> {
    const dateOnly = new Date(date.toISOString().split('T')[0]);

    return this.findOne({
      where: {
        driverId,
        earningsDate: dateOnly,
      },
      include: {
        driver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });
  }

  /**
   * Get top earning drivers for a date range
   */
  async getTopEarningDrivers(
    limit: number = 10,
    fromDate?: Date,
    toDate?: Date,
  ): Promise<DriverEarningsAggregation[]> {
    const whereClause: any = {};

    if (fromDate || toDate) {
      whereClause.earningsDate = {};
      if (fromDate) whereClause.earningsDate.gte = fromDate;
      if (toDate) whereClause.earningsDate.lte = toDate;
    }

    const results = await this.prisma.driverEarnings.groupBy({
      by: ['driverId'],
      where: whereClause,
      _sum: {
        totalFareAmount: true,
        completedRides: true,
      },
      orderBy: {
        _sum: {
          totalFareAmount: 'desc',
        },
      },
      take: limit,
    });

    return results.map((result) => ({
      driverId: result.driverId,
      totalEarnings: Number(result._sum.totalFareAmount || 0),
      totalRides: result._sum.completedRides || 0,
      averageEarningsPerRide:
        result._sum.completedRides && result._sum.totalFareAmount
          ? Number(result._sum.totalFareAmount) / result._sum.completedRides
          : 0,
      dateRange: {
        from: fromDate || new Date(0),
        to: toDate || new Date(),
      },
    }));
  }

  /**
   * Get aggregated earnings for all drivers with pagination
   */
  async getAggregatedDriverEarnings(
    page: number = 1,
    limit: number = 10,
    fromDate?: Date,
    toDate?: Date,
    driverId?: string,
    cityId?: string,
    phoneNumber?: string,
  ): Promise<{
    data: any[];
    total: number;
    totalPages: number;
    page: number;
    limit: number;
  }> {
    const skip = (page - 1) * limit;

    // Build base where clause for earnings
    const whereClause: any = {};

    if (fromDate || toDate) {
      whereClause.earningsDate = {};
      if (fromDate) whereClause.earningsDate.gte = fromDate;
      if (toDate) whereClause.earningsDate.lte = toDate;
    }

    if (driverId) {
      whereClause.driverId = driverId;
    }

    // Handle phone number filtering by first getting matching driver IDs
    let driverIds: string[] | undefined;

    if (phoneNumber) {
      const phoneFilteredDrivers = await this.prisma.userProfile.findMany({
        where: {
          user: {
            phoneNumber: {
              contains: phoneNumber.trim(),
              mode: 'insensitive',
            },
          },
        },
        select: {
          id: true,
        },
      });

      driverIds = phoneFilteredDrivers.map((d) => d.id);

      // If no drivers match the phone number, return empty result
      if (driverIds.length === 0) {
        return {
          data: [],
          total: 0,
          totalPages: 0,
          page,
          limit,
        };
      }
    }

    // Apply driver ID filter if we have specific drivers from phone search
    if (driverIds) {
      whereClause.driverId = { in: driverIds };
    }

    // Add city filtering at the database level
    const driverWhereClause: any = {};
    if (cityId) {
      driverWhereClause.cityId = cityId;
    }

    // Get total count efficiently using distinct count
    const totalCount = await this.prisma.driverEarnings.aggregate({
      where: {
        ...whereClause,
        driver: driverWhereClause,
      },
      _count: {
        driverId: true,
      },
    });

    // Get aggregated data with single optimized query
    const results = await this.prisma.driverEarnings.groupBy({
      by: ['driverId'],
      where: {
        ...whereClause,
        driver: driverWhereClause,
      },
      _sum: {
        totalFareAmount: true,
        completedRides: true,
        totalTaxesOnCharge: true,
        totalCommission: true,
        totalTaxOnCommission: true,
      },
      skip,
      take: limit,
      orderBy: {
        _sum: {
          totalFareAmount: 'desc',
        },
      },
    });

    // Get all driver details in one query using the driver IDs from results
    const driverIdsFromResults = results.map((r) => r.driverId);

    const drivers = await this.prisma.userProfile.findMany({
      where: {
        id: { in: driverIdsFromResults },
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        city: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Create a map for quick driver lookup
    const driverMap = new Map(drivers.map((d) => [d.id, d]));

    // Transform results with pre-fetched driver data
    const data = results.map((result) => {
      const driver = driverMap.get(result.driverId);

      if (!driver) {
        return null;
      }

      const totalFare = Number(result._sum.totalFareAmount || 0);
      const totalTaxesOnCharge = Number(result._sum.totalTaxesOnCharge || 0);
      const totalCommission = Number(result._sum.totalCommission || 0);
      const totalTaxOnCommission = Number(
        result._sum.totalTaxOnCommission || 0,
      );
      const netDriverEarnings =
        totalFare - totalCommission - totalTaxOnCommission - totalTaxesOnCharge;

      return {
        driverId: result.driverId,
        driverFirstName: driver.firstName || '',
        driverLastName: driver.lastName || '',
        city: driver.city?.name || '',
        totalFare,
        totalTaxesOnCharge,
        totalCommission,
        totalTaxOnCommission,
        netDriverEarnings,
        completedRides: result._sum.completedRides || 0,
      };
    });

    // Filter out any null values
    const filteredData = data.filter((item) => item !== null);

    return {
      data: filteredData,
      total: totalCount._count.driverId,
      totalPages: Math.ceil(totalCount._count.driverId / limit),
      page,
      limit,
    };
  }

  /**
   * Get daily earnings for a specific driver with pagination
   */
  async getDailyEarningsForDriver(
    driverId: string,
    page: number = 1,
    limit: number = 10,
    fromDate?: Date,
    toDate?: Date,
  ): Promise<{
    data: any[];
    total: number;
    totalPages: number;
    page: number;
    limit: number;
  }> {
    const skip = (page - 1) * limit;
    const whereClause: any = { driverId };

    if (fromDate || toDate) {
      whereClause.earningsDate = {};
      if (fromDate) whereClause.earningsDate.gte = fromDate;
      if (toDate) whereClause.earningsDate.lte = toDate;
    }

    const [earnings, total] = await Promise.all([
      this.prisma.driverEarnings.findMany({
        where: whereClause,
        orderBy: { earningsDate: 'desc' },
        skip,
        take: limit,
      }),
      this.prisma.driverEarnings.count({ where: whereClause }),
    ]);

    const data = earnings.map((earning) => {
      const totalFare = Number(earning.totalFareAmount || 0);
      const totalTaxesOnCharge = Number(earning.totalTaxesOnCharge || 0);
      const totalCommission = Number(earning.totalCommission || 0);
      const totalTaxOnCommission = Number(earning.totalTaxOnCommission || 0);
      const netDriverEarnings =
        totalFare - totalCommission - totalTaxOnCommission - totalTaxesOnCharge;

      return {
        date: earning.earningsDate.toISOString().split('T')[0],
        totalFare,
        totalTaxesOnCharge,
        totalCommission,
        totalTaxOnCommission,
        netDriverEarnings,
        completedRides: earning.completedRides,
      };
    });

    return {
      data,
      total,
      totalPages: Math.ceil(total / limit),
      page,
      limit,
    };
  }
}

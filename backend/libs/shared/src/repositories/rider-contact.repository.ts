import { Injectable, NotFoundException } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { RiderContact } from './models/riderContact.model';
import { PrismaService } from '../database/prisma/prisma.service';

export interface RiderContactFilters {
  riderId?: string;
  search?: string;
}

export interface RiderContactPaginationResult {
  data: RiderContact[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

@Injectable()
export class RiderContactRepository extends BaseRepository<RiderContact> {
  protected readonly modelName = 'riderContact';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new rider contact
   */
  async createRiderContact(
    data: Omit<RiderContact, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<RiderContact> {
    return this.create(data);
  }

  /**
   * Find contacts by rider ID
   */
  async findContactsByRiderId(riderId: string): Promise<RiderContact[]> {
    return this.findMany({
      where: { riderId },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Find contact by ID and rider ID (for authorization)
   */
  async findContactByIdAndRiderId(
    id: string,
    riderId: string,
  ): Promise<RiderContact | null> {
    return this.findOne({
      where: { id, riderId },
    });
  }

  /**
   * Update rider contact
   */
  async updateRiderContact(
    id: string,
    riderId: string,
    data: Partial<
      Omit<
        RiderContact,
        'id' | 'createdAt' | 'updatedAt' | 'deletedAt' | 'riderId'
      >
    >,
  ): Promise<RiderContact> {
    return this.update({
      where: { id, riderId },
      data,
    });
  }

  /**
   * Delete rider contact (soft delete)
   */
  async deleteRiderContact(id: string, riderId: string): Promise<RiderContact> {
    // First verify the contact belongs to the rider
    const contact = await this.findContactByIdAndRiderId(id, riderId);
    if (!contact) {
      throw new NotFoundException('Contact not found');
    }

    // Perform soft delete
    return this.softDeleteById(id);
  }

  /**
   * Get paginated contacts for a rider with optional search
   */
  async getPaginatedContacts(
    riderId: string,
    page: number = 1,
    limit: number = 10,
    search?: string,
  ): Promise<RiderContactPaginationResult> {
    const skip = (page - 1) * limit;

    const whereClause: any = {
      riderId,
    };

    // Add search functionality
    if (search) {
      whereClause.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { phoneNumber: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [data, total] = await Promise.all([
      this.findMany({
        where: whereClause,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      this.count(whereClause),
    ]);

    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNext,
      hasPrev,
    };
  }

  /**
   * Check if contact exists for rider
   */
  async contactExists(id: string, riderId: string): Promise<boolean> {
    const contact = await this.findOne({
      where: { id, riderId },
      select: { id: true },
    });
    return !!contact;
  }

  /**
   * Count total contacts for a rider
   */
  async countContactsByRiderId(riderId: string): Promise<number> {
    return this.count({
      where: { riderId },
    });
  }
}

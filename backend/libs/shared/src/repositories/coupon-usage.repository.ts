import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import {
  CouponUsage,
  UserCouponUsageLimit,
  CreateCouponUsageData,
  CouponUsageStats,
  UserCouponUsageInfo,
} from './models/couponUsage.model';
import { PrismaService } from '../database/prisma/prisma.service';
import { CouponUsageStatus } from '@prisma/client';

@Injectable()
export class CouponUsageRepository extends BaseRepository<CouponUsage> {
  protected readonly modelName = 'couponUsage';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new coupon usage record
   */
  async createCouponUsage(data: CreateCouponUsageData): Promise<CouponUsage> {
    const result = await this.prisma.couponUsage.create({
      data: {
        couponId: data.couponId,
        userId: data.userId,
        rideId: data.rideId,
        originalFare: data.originalFare,
        discountAmount: data.discountAmount,
        finalFare: data.finalFare,
        status: CouponUsageStatus.APPLIED,
      },
      include: {
        coupon: true,
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
        ride: {
          select: {
            id: true,
            status: true,
            pickupLocation: true,
            destinationLocation: true,
          },
        },
      },
    });

    return this.mapToModel(result);
  }

  /**
   * Find coupon usage by ride ID
   */
  async findByRideId(rideId: string): Promise<CouponUsage | null> {
    const result = await this.prisma.couponUsage.findFirst({
      where: {
        rideId,
        deletedAt: null,
      },
      include: {
        coupon: true,
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    return result ? this.mapToModel(result) : null;
  }

  /**
   * Find coupon usages by user ID
   */
  async findByUserId(
    userId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ usages: CouponUsage[]; total: number }> {
    const skip = (page - 1) * limit;

    const [usages, total] = await Promise.all([
      this.prisma.couponUsage.findMany({
        where: {
          userId,
          deletedAt: null,
        },
        include: {
          coupon: {
            select: {
              id: true,
              name: true,
              code: true,
              discountType: true,
              discountValue: true,
            },
          },
          ride: {
            select: {
              id: true,
              status: true,
              pickupLocation: true,
              destinationLocation: true,
              completedAt: true,
            },
          },
        },
        orderBy: { appliedAt: 'desc' },
        skip,
        take: limit,
      }),
      this.prisma.couponUsage.count({
        where: {
          userId,
          deletedAt: null,
        },
      }),
    ]);

    return {
      usages: usages.map((usage) => this.mapToModel(usage)),
      total,
    };
  }

  /**
   * Cancel coupon usage (for ride cancellations)
   */
  async cancelCouponUsage(rideId: string): Promise<CouponUsage | null> {
    const result = await this.prisma.couponUsage.updateMany({
      where: {
        rideId,
        status: CouponUsageStatus.APPLIED,
        deletedAt: null,
      },
      data: {
        status: CouponUsageStatus.CANCELLED,
        cancelledAt: new Date(),
      },
    });

    if (result.count === 0) {
      return null;
    }

    // Return the updated record
    return this.findByRideId(rideId);
  }

  /**
   * Get coupon usage statistics
   */
  async getCouponUsageStats(couponId: string): Promise<CouponUsageStats> {
    const stats = await this.prisma.couponUsage.aggregate({
      where: {
        couponId,
        deletedAt: null,
      },
      _count: {
        id: true,
      },
      _sum: {
        discountAmount: true,
      },
      _avg: {
        discountAmount: true,
      },
    });

    const statusCounts = await this.prisma.couponUsage.groupBy({
      by: ['status'],
      where: {
        couponId,
        deletedAt: null,
      },
      _count: {
        id: true,
      },
    });

    const activeUsages =
      statusCounts.find((s) => s.status === CouponUsageStatus.APPLIED)?._count
        .id || 0;

    const cancelledUsages =
      statusCounts.find((s) => s.status === CouponUsageStatus.CANCELLED)?._count
        .id || 0;

    return {
      totalUsages: stats._count.id || 0,
      activeUsages,
      cancelledUsages,
      totalDiscountGiven: Number(stats._sum.discountAmount || 0),
      averageDiscountAmount: Number(stats._avg.discountAmount || 0),
    };
  }

  /**
   * Get or create user coupon usage limit record
   */
  async getOrCreateUserUsageLimit(
    couponId: string,
    userId: string,
    userSpecificLimit?: number,
  ): Promise<UserCouponUsageLimit> {
    let record = await this.prisma.userCouponUsageLimit.findUnique({
      where: {
        unique_user_coupon_limit: {
          couponId,
          userId,
        },
      },
    });

    if (!record) {
      record = await this.prisma.userCouponUsageLimit.create({
        data: {
          couponId,
          userId,
          usageCount: 0,
          usageLimit: userSpecificLimit || null,
        },
      });
    }

    return this.mapUserUsageLimitToModel(record);
  }

  /**
   * Increment user usage count
   */
  async incrementUserUsageCount(
    couponId: string,
    userId: string,
  ): Promise<UserCouponUsageLimit> {
    const result = await this.prisma.userCouponUsageLimit.upsert({
      where: {
        unique_user_coupon_limit: {
          couponId,
          userId,
        },
      },
      update: {
        usageCount: {
          increment: 1,
        },
      },
      create: {
        couponId,
        userId,
        usageCount: 1,
      },
    });

    return this.mapUserUsageLimitToModel(result);
  }

  /**
   * Decrement user usage count (for cancellations)
   */
  async decrementUserUsageCount(
    couponId: string,
    userId: string,
  ): Promise<UserCouponUsageLimit | null> {
    const existing = await this.prisma.userCouponUsageLimit.findUnique({
      where: {
        unique_user_coupon_limit: {
          couponId,
          userId,
        },
      },
    });

    if (!existing || existing.usageCount <= 0) {
      return null;
    }

    const result = await this.prisma.userCouponUsageLimit.update({
      where: {
        unique_user_coupon_limit: {
          couponId,
          userId,
        },
      },
      data: {
        usageCount: {
          decrement: 1,
        },
      },
    });

    return this.mapUserUsageLimitToModel(result);
  }

  /**
   * Get user coupon usage information
   */
  async getUserCouponUsageInfo(
    couponId: string,
    userId: string,
  ): Promise<UserCouponUsageInfo> {
    const record = await this.getOrCreateUserUsageLimit(couponId, userId);

    const canUseAgain = record.usageLimit
      ? record.usageCount < record.usageLimit
      : true; // No user-specific limit

    const remainingUsages = record.usageLimit
      ? Math.max(0, record.usageLimit - record.usageCount)
      : undefined;

    const result: UserCouponUsageInfo = {
      couponId,
      userId,
      currentUsageCount: record.usageCount,
      canUseAgain,
    };

    // Only add optional properties if they have values
    if (record.usageLimit !== null) {
      result.userSpecificLimit = record.usageLimit;
    }

    if (remainingUsages !== undefined) {
      result.remainingUsages = remainingUsages;
    }

    return result;
  }

  /**
   * Update coupon usage with ride ID
   */
  async updateCouponUsageRideId(
    couponUsageId: string,
    rideId: string,
  ): Promise<void> {
    await this.prisma.couponUsage.update({
      where: { id: couponUsageId },
      data: { rideId },
    });
  }

  /**
   * Update coupon usage amounts (for recalculation at ride completion)
   */
  async updateCouponUsageAmounts(
    couponUsageId: string,
    amounts: {
      originalFare: number;
      discountAmount: number;
      finalFare: number;
    },
  ): Promise<void> {
    await this.prisma.couponUsage.update({
      where: { id: couponUsageId },
      data: {
        originalFare: amounts.originalFare,
        discountAmount: amounts.discountAmount,
        finalFare: amounts.finalFare,
      },
    });
  }

  /**
   * Map Prisma result to model
   */
  private mapToModel(prismaResult: any): CouponUsage {
    return {
      id: prismaResult.id,
      couponId: prismaResult.couponId,
      userId: prismaResult.userId,
      rideId: prismaResult.rideId,
      originalFare: Number(prismaResult.originalFare),
      discountAmount: Number(prismaResult.discountAmount),
      finalFare: Number(prismaResult.finalFare),
      status: prismaResult.status,
      appliedAt: prismaResult.appliedAt,
      cancelledAt: prismaResult.cancelledAt,
      createdAt: prismaResult.createdAt,
      updatedAt: prismaResult.updatedAt,
      deletedAt: prismaResult.deletedAt,
      coupon: prismaResult.coupon,
      user: prismaResult.user,
      ride: prismaResult.ride,
    };
  }

  /**
   * Map user usage limit Prisma result to model
   */
  private mapUserUsageLimitToModel(prismaResult: any): UserCouponUsageLimit {
    return {
      id: prismaResult.id,
      couponId: prismaResult.couponId,
      userId: prismaResult.userId,
      usageCount: prismaResult.usageCount,
      usageLimit: prismaResult.usageLimit,
      createdAt: prismaResult.createdAt,
      updatedAt: prismaResult.updatedAt,
      deletedAt: prismaResult.deletedAt,
      coupon: prismaResult.coupon,
      user: prismaResult.user,
    };
  }
}

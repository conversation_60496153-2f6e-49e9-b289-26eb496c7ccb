import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '@shared/shared/database/prisma/prisma.service';
import {
  RideMeterLog,
  CreateRideMeterLogData,
} from './models/rideMeterLog.model';

@Injectable()
export class RideMeterLogRepository extends BaseRepository<RideMeterLog> {
  protected readonly modelName = 'rideMeterLog';

  constructor(protected readonly prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new ride meter log
   */
  async createRideMeterLog(
    data: CreateRideMeterLogData,
  ): Promise<RideMeterLog> {
    const rideMeterLog = await this.model.create({
      data: {
        rideId: data.rideId,
        rideMeterId: data.rideMeterId,
        name: data.name,
        fromTime: data.fromTime || new Date(),
      },
    });

    return rideMeterLog;
  }

  /**
   * Find active (not ended) meter logs by ride ID and meter type
   */
  async findActiveLogsByRideIdAndMeterType(
    rideId: string,
    meterType: string,
  ): Promise<RideMeterLog> {
    return await this.model.findFirst({
      where: {
        rideId,
        name: meterType,
        toTime: null, // Active logs have no end time
        deletedAt: null,
      },
    });
  }

  /**
   * Find a specific meter log by ID
   */
  async findMeterLogById(id: string): Promise<RideMeterLog | null> {
    const log = await this.model.findFirst({
      where: {
        id,
        deletedAt: null,
      },
      include: {
        ride: true,
        rideMeter: true,
      },
    });

    return log;
  }

  /**
   * Update a meter log to mark it as ended
   */
  async endMeterLog(id: string, endTime?: Date): Promise<RideMeterLog> {
    const log = await this.model.update({
      where: { id },
      data: {
        toTime: endTime || new Date(),
      },
      include: {
        ride: true,
        rideMeter: true,
      },
    });

    return log;
  }

  /**
   * Find meter logs by ride ID
   */
  async findByRideId(rideId: string): Promise<RideMeterLog[]> {
    return await this.model.findMany({
      where: {
        rideId,
        deletedAt: null,
      },
      include: {
        ride: true,
        rideMeter: true,
      },
      orderBy: {
        fromTime: 'asc',
      },
    });
  }

  /**
   * Find meter logs by ride meter ID
   */
  async findByRideMeterId(rideMeterId: string): Promise<RideMeterLog[]> {
    const logs = await this.model.findMany({
      where: {
        rideMeterId,
        deletedAt: null,
      },
      include: {
        ride: true,
        rideMeter: true,
      },
      orderBy: {
        fromTime: 'asc',
      },
    });

    return logs;
  }
}

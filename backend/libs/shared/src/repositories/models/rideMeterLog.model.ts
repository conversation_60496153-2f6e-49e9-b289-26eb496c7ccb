import { BaseEntity } from '../base.repository';
import { Ride } from './ride.model';
import { RideMeter } from './rideMeter.model';

/**
 * Interface for ride meter log data
 */
export interface RideMeterLog extends BaseEntity {
  rideId: string;
  rideMeterId: string;
  name: string; // The meter/metric name (e.g., "pickup_wait_time", "trip_wait_time")
  fromTime: Date; // Start timestamp of the waiting period
  toTime?: Date | null; // End timestamp of the waiting period (null if still active)

  // Relations
  ride?: Ride;
  rideMeter?: RideMeter;
}

/**
 * Interface for creating a new ride meter log
 */
export interface CreateRideMeterLogData {
  rideId: string;
  rideMeterId: string;
  name: string;
  fromTime?: Date; // Optional, defaults to current time
}

/**
 * Interface for updating ride meter log data
 */
export interface UpdateRideMeterLogData {
  toTime?: Date;
  name?: string;
}

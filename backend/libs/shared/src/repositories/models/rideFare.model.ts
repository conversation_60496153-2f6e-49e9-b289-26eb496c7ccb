import { BaseEntity } from '../base.repository';
import { Ride } from './ride.model';
import { Charge } from './charge.model';
import { CityProduct } from './cityProduct.model';
import { CityProductFare } from './cityProductFare.model';
import { FareCalculationResult } from '@shared/shared/modules/fare-engine/fare-calculator/interfaces/fare-calculation.interface';

/**
 * Interface for ride fare data
 */
export interface RideFare extends BaseEntity {
  rideId: string;
  chargeId?: string | null;
  fare: number; // Individual fare component
  taxId?: string | null;
  commissionId?: string | null;
  totalFare: number; // Final calculated fare
  currency: string;
  cityProductId?: string | null;
  cityProductFareId?: string | null;

  // Fare breakdown details
  subtotal: number; // Sum of all charges before taxes
  totalTaxes: number; // Sum of all taxes
  totalCommissions: number; // Sum of all commissions
  passengerFare: number; // What customer pays
  driverEarnings: number; // What driver receives
  platformRevenue: number; // What platform earns

  // Calculation metadata
  calculatedAt: Date;
  fareBreakdown?: any; // Detailed breakdown JSON

  // Relations
  ride?: Ride;
  charge?: Charge | null;
  cityProduct?: CityProduct | null;
  cityProductFare?: CityProductFare | null;
}

/**
 * Interface for creating a new ride fare
 */
export interface CreateRideFareData {
  rideId: string;
  chargeId?: string | null;
  fare: number;
  taxId?: string | null;
  commissionId?: string | null;
  totalFare: number;
  currency?: string;
  cityProductId?: string | null;
  cityProductFareId?: string | null;
  subtotal: number;
  totalTaxes: number;
  totalCommissions: number;
  passengerFare: number;
  driverEarnings: number;
  platformRevenue: number;
  calculatedAt?: Date;
  fareBreakdown?: any;
}

/**
 * Interface for updating ride fare data
 */
export interface UpdateRideFareData {
  fare?: number;
  totalFare?: number;
  currency?: string;
  subtotal?: number;
  totalTaxes?: number;
  totalCommissions?: number;
  passengerFare?: number;
  driverEarnings?: number;
  platformRevenue?: number;
  fareBreakdown?: any;
}

/**
 * Interface for ride fare query filters
 */
export interface RideFareFilters {
  rideId?: string;
  chargeId?: string;
  cityProductId?: string;
  cityProductFareId?: string;
  currency?: string;
  minFare?: number;
  maxFare?: number;
  calculatedAfter?: Date;
  calculatedBefore?: Date;
  createdAfter?: Date;
  createdBefore?: Date;
}

/**
 * Interface for creating ride fare from fare calculation result
 */
export interface CreateRideFareFromCalculationData {
  rideId: string;
  fareCalculationResult: FareCalculationResult;
}

/**
 * Interface for bulk ride fare creation
 */
export interface BulkCreateRideFareData {
  rideId: string;
  fares: CreateRideFareData[];
}

/**
 * Interface for ride fare summary
 */
export interface RideFareSummary {
  rideId: string;
  totalFareComponents: number;
  totalPassengerFare: number;
  totalDriverEarnings: number;
  totalPlatformRevenue: number;
  totalTaxes: number;
  totalCommissions: number;
  currency: string;
  faresByCharge: Record<string, number>;
  faresByType: Record<string, number>;
  calculatedAt: Date;
}

/**
 * Interface for fare calculation context for rides
 */
export interface RideFareCalculationContext {
  rideId: string;
  cityProductId: string;
  pickupZoneId?: string | null;
  destinationZoneId?: string | null;
  stopsZoneIds?: string[] | null;
  rideMeters: Record<string, number>; // meter name -> value mapping
  currency?: string;
  timestamp?: Date;
}

/**
 * Interface for fare calculation request
 */
export interface CalculateRideFareRequest {
  rideId: string;
  cityProductId: string;
  rideMeters: Array<{
    name: string;
    value: number;
    unit: string;
  }>;
  pickupZoneId?: string | null;
  destinationZoneId?: string | null;
  stopsZoneIds?: string[] | null;
  currency?: string;
}

/**
 * Interface for fare calculation response
 */
export interface CalculateRideFareResponse {
  success: boolean;
  rideFare?: RideFare;
  fareCalculationResult?: FareCalculationResult;
  error?: string;
  details?: any;
}

/**
 * Enum for fare calculation status
 */
export enum FareCalculationStatus {
  PENDING = 'pending',
  CALCULATING = 'calculating',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

/**
 * Interface for fare calculation job
 */
export interface FareCalculationJob {
  id: string;
  rideId: string;
  status: FareCalculationStatus;
  request: CalculateRideFareRequest;
  response?: CalculateRideFareResponse;
  startedAt: Date;
  completedAt?: Date;
  error?: string;
  retryCount: number;
  maxRetries: number;
}

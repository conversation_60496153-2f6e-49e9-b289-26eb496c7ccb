import { BaseEntity } from '../base.repository';

export enum TransactionType {
  CREDIT = 'CREDIT',
  DEBIT = 'DEBIT',
}

export interface DriverAccount extends BaseEntity {
  driverId: string;
  availableBalance: number;
  transactions?: DriverAccountTransaction[];
}

export interface DriverAccountTransaction extends BaseEntity {
  driverId: string;
  rideId?: string | null;
  amount: number;
  transactionType: TransactionType;
  reason: string;
  balanceAfter: number;
}

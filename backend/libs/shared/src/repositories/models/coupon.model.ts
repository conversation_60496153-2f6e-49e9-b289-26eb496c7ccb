import { BaseEntity } from '../base.repository';
import {
  DiscountType as PrismaDiscountType,
  ApplicabilityType as PrismaApplicabilityType,
} from '@prisma/client';

// Re-export Prisma enums for consistency
export const DiscountType = PrismaDiscountType;
export const ApplicabilityType = PrismaApplicabilityType;
export type DiscountType = PrismaDiscountType;
export type ApplicabilityType = PrismaApplicabilityType;

export interface Coupon extends BaseEntity {
  name: string;
  code: string;
  thumbnail?: string | null;
  description?: string | null;
  startDate: Date;
  endDate: Date;
  discountType: DiscountType;
  discountValue: number;
  maxDiscountLimit?: number | null;
  minFareCondition?: number | null;
  usageLimit: number;
  usageCount: number;
  applicabilityType: ApplicabilityType;
  applyConditionLogic?: any; // JSONLogic rules
  isActive: boolean;

  // Relations
  couponZones?: CouponZone[];
  couponProducts?: CouponProduct[];
  couponCityProducts?: CouponCityProduct[];
  couponUsers?: CouponUser[];
}

export interface CouponZone extends BaseEntity {
  couponId: string;
  zoneId: string;

  // Relations
  coupon?: Coupon;
  zone?: any; // Zone interface from zone.model.ts
}

export interface CouponProduct extends BaseEntity {
  couponId: string;
  productId: string;

  // Relations
  coupon?: Coupon;
  product?: any; // Product interface from product.model.ts
}

export interface CouponCityProduct extends BaseEntity {
  couponId: string;
  cityProductId: string;

  // Relations
  coupon?: Coupon;
  cityProduct?: any; // CityProduct interface from cityProduct.model.ts
}

export interface CouponUser extends BaseEntity {
  couponId: string;
  userId: string;

  // Relations
  coupon?: Coupon;
  userProfile?: any; // UserProfile interface from userProfile.model.ts
}

// Helper interfaces for coupon applicability
export interface CouponApplicabilityData {
  type: ApplicabilityType;
  zoneIds?: string[] | undefined;
  productIds?: string[] | undefined;
  cityProductIds?: string[] | undefined;
  userIds?: string[] | undefined;
}

// Interface for coupon validation result
export interface CouponValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

// Interface for coupon usage tracking
export interface CouponUsageInfo {
  couponId: string;
  usageCount: number;
  usageLimit: number;
  remainingUsage: number;
  isExpired: boolean;
  isActive: boolean;
}

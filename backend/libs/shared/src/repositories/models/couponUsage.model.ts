import { BaseEntity } from '../base.repository';
import { CouponUsageStatus } from '@prisma/client';

export interface CouponUsage extends BaseEntity {
  couponId: string;
  userId: string;
  rideId: string;

  // Discount information
  originalFare: number;
  discountAmount: number;
  finalFare: number;

  // Status tracking
  status: CouponUsageStatus;
  appliedAt: Date;
  cancelledAt?: Date | null;

  // Relations
  coupon?: any; // Coupon interface
  user?: any; // UserProfile interface
  ride?: any; // Ride interface
}

export interface UserCouponUsageLimit extends BaseEntity {
  couponId: string;
  userId: string;
  usageCount: number;
  usageLimit?: number | null; // Per-user limit (optional)

  // Relations
  coupon?: any; // Coupon interface
  user?: any; // UserProfile interface
}

// Helper interfaces for coupon usage operations
export interface CreateCouponUsageData {
  couponId: string;
  userId: string;
  rideId: string;
  originalFare: number;
  discountAmount: number;
  finalFare: number;
}

export interface CouponUsageStats {
  totalUsages: number;
  activeUsages: number;
  cancelledUsages: number;
  totalDiscountGiven: number;
  averageDiscountAmount: number;
}

export interface UserCouponUsageInfo {
  couponId: string;
  userId: string;
  currentUsageCount: number;
  userSpecificLimit?: number | undefined;
  canUseAgain: boolean;
  remainingUsages?: number;
}

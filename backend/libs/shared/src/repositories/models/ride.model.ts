import { BaseEntity } from '../base.repository';
import { UserProfile } from './userProfile.model';
import { Product } from './product.model';
import { RideLifecycle } from './rideLifecycle.model';
import { RideStatus } from '@shared/shared/modules/ride-matching/constants';
import { CityProduct } from './cityProduct.model';
import { RideMeter } from './rideMeter.model';
import { RideFare } from './rideFare.model';
import { Review } from './review.model';

export interface LocationPoint {
  lat: number;
  lng: number;
  address?: string;
}

export interface RiderMeta {
  name?: string;
  phoneNumber?: string;
  email?: string;
}

export enum PickupType {
  NOW = 'now',
  LATER = 'later',
}

export enum BookFor {
  ME = 'me',
  OTHER = 'other',
}

export interface Ride extends BaseEntity {
  driverId?: string | null;
  riderId: string;
  productId: string;
  cityProductId?: string | null;
  driverVehicleId?: string | null;
  status: RideStatus;
  pickupLocation: LocationPoint | null;
  destinationLocation: LocationPoint | null;
  stops?: LocationPoint[] | null;
  verificationCode?: string | null;
  otpVerifiedAt?: Date | null;
  completedAt?: Date | null;
  duration?: number | null;
  actualDuration?: number | null;
  distance?: number | null;
  fareSpec?: any | null; // Complete fare calculation details in JSON format
  driverToPickupSpec?: any | null; // Driver to pickup distance and duration calculation details
  createdBy?: string | null;
  riderMeta?: RiderMeta | null;
  bookFor?: BookFor;
  pickupType?: PickupType;
  pickupTime?: Date | null;

  // Relations
  driver?: UserProfile | null;
  rider?: UserProfile;
  product?: Product;
  cityProduct?: CityProduct | null;
  driverVehicle?: any; // Optional relation to driver vehicle
  rideLifecycles?: RideLifecycle[];
  rideMeters?: RideMeter[];
  rideFares?: RideFare[];
  reviews?: Review[];
  createdByUser?: UserProfile | null;
}

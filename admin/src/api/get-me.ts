import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';
import { minutesToMilliseconds } from 'date-fns';

const getMe = (): Promise<IGetMeResponse> => {
   return apiClient.get('/auth/me');
};

export const useGetMe = () => {
   return useQuery({
      queryKey: ['auth-me'],
      queryFn: getMe,
      staleTime: minutesToMilliseconds(5),
      refetchOnMount: false,
      retry: false,
   });
};

export interface IGetMeResponse {
   success: boolean;
   message: string;
   data: IGetMeData;
   timestamp: number;
}

export interface IGetMeData {
   id: string;
   phoneNumber: string | null;
   email: string;
   emailVerifiedAt: string | null;
   phoneVerifiedAt: string | null;
   isPolicyAllowed: boolean;
   otpSecret: string | null;
   createdAt: string;
   updatedAt: string;
   deletedAt: string | null;
   userProfile: IUserProfile;
}

export interface IUserProfile {
   id: string;
   userId: string;
   roleId: string;
   firstName: string | null;
   lastName: string | null;
   cityId: string | null;
   referralCode: string | null;
   profilePictureUrl: string | null;
   languageId: string | null;
   gender: string | null;
   dob: string | null;
   createdAt: string;
   updatedAt: string;
   deletedAt: string | null;
   language: any | null;
}

'use client';

import { CityDetailsPage } from '@/modules/city/pages/city-details-page';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';

export default function CityDetailsRoute() {
   const { hasPermission } = useRoleBasedAccess();

   return hasPermission(RBAC_PERMISSIONS.CITY.MANAGE) ? <CityDetailsPage /> : null;
}

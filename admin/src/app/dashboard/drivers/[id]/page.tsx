'use client';

import { DriverDetailsPage } from '@/modules/driver/pages/driver-details-page';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';

export default function DriverDetailPage() {
   const { hasPermission } = useRoleBasedAccess();

   return hasPermission(RBAC_PERMISSIONS.DRIVER.MANAGE) ? <DriverDetailsPage /> : null;
}

'use client';

import LoginPage from '@/modules/auth/pages/login-page';
import { useAuthStore } from '@/store/auth-store';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function HomePage() {
   const authToken = useAuthStore.getState().authToken;
   const router = useRouter();

   useEffect(() => {
      if (authToken) {
         router.push('/dashboard/drivers');
      }
   }, [authToken, router]);

   return <LoginPage />;
}

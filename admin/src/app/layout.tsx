import MetaProvider from '@/wrappers/providers/meta-provider';
import { QueryProvider } from '@/wrappers/providers/query-provider';
import { GoogleMapsProvider } from '@/providers/google-maps-provider';
import type { Metadata } from 'next';
import { Geist, <PERSON>eist_Mono } from 'next/font/google';
import NextTopLoader from 'nextjs-toploader';
import { Suspense } from 'react';
import { Toaster } from 'sonner';
import './globals.css';

const geistSans = Geist({
   variable: '--font-geist-sans',
   subsets: ['latin'],
});

const geistMono = Geist_Mono({
   variable: '--font-geist-mono',
   subsets: ['latin'],
});

export const metadata: Metadata = {
   title: 'Tukxi Admin',
   description: 'Admin Dashboard for Tukxi',
};

export default function RootLayout({
   children,
}: Readonly<{
   children: React.ReactNode;
}>) {
   return (
      <html lang='en'>
         <body className={`${geistSans.variable} ${geistMono.variable} antialiased font-sans`}>
            <QueryProvider>
               <GoogleMapsProvider>
                  <Suspense>
                     <NextTopLoader color='#000' />
                     <MetaProvider>{children}</MetaProvider>
                  </Suspense>
               </GoogleMapsProvider>
            </QueryProvider>
            <Toaster richColors={true} />
         </body>
      </html>
   );
}

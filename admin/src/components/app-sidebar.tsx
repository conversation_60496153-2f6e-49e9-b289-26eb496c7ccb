'use client';

import { usePathname } from 'next/navigation';
import * as React from 'react';

import { NavMain } from '@/components/nav-main';
import { Sidebar, SidebarContent, SidebarHeader, SidebarRail } from '@/components/ui/sidebar';
import { Skeleton } from '@/components/ui/skeleton';
import { SIDEBAR_ROUTES } from '@/data/sidebar-routes';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { LogoName } from './logo-name';

// Sidebar skeleton loader component
function SidebarSkeleton() {
   return (
      <div className='py-2 group-data-[collapsible=icon]:px-1'>
         <div className='space-y-1'>
            {Array.from({ length: 9 }).map((_, index) => (
               <div
                  key={index}
                  className='h-8 px-3 py-1 rounded-md flex items-center gap-2 group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:mx-auto group-data-[collapsible=icon]:w-8'
               >
                  <Skeleton className='w-4 h-4 flex-shrink-0' />
                  <Skeleton className='h-3.5 flex-1 group-data-[collapsible=icon]:hidden' />
               </div>
            ))}
         </div>
      </div>
   );
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
   const pathname = usePathname();
   const [isClient, setIsClient] = React.useState(false);
   const [data, setData] = React.useState<
      Array<{
         title: string;
         url: string;
         icon: any;
         isActive: boolean;
         pagePermission?: any;
         items?: Array<{
            title: string;
            url: string;
            icon: any;
            isActive: boolean;
            pagePermission: any;
         }>;
      }>
   >([]);

   React.useEffect(() => {
      setIsClient(true);
      setData(SIDEBAR_ROUTES);
   }, []);

   const { hasAnyPermission, isLoading } = useRoleBasedAccess();

   // Update navMain items with active state based on current pathname
   const navMenuWithActiveState = React.useMemo(() => {
      return data.map(group => {
         if (!group.items || group.items.length === 0) {
            return {
               ...group,
               isActive: pathname === group.url || pathname.startsWith(group.url + '/'),
            };
         }

         // For groups with items, filter items based on permissions and update active state
         const filteredItems = group.items
            .filter((item: any) => {
               if (!item.pagePermission) return true;
               const allPagePermissions = Object.keys(item.pagePermission).map(
                  pagePermission => (item.pagePermission as any)[pagePermission]
               );
               return hasAnyPermission(allPagePermissions);
            })
            .map((item: any) => ({
               ...item,
               isActive: pathname === item.url || pathname.startsWith(item.url + '/'),
            }));

         // Group is active if any of its items are active
         const isGroupActive = filteredItems.some((item: any) => item.isActive);

         return {
            ...group,
            items: filteredItems,
            isActive: isGroupActive,
         };
      });
   }, [data, pathname, hasAnyPermission]);

   const navMainWithActiveState = React.useMemo(
      () =>
         !isClient || isLoading || data.length === 0
            ? []
            : navMenuWithActiveState.filter(group => {
                 // For groups with items, only show if there's at least one visible item after permission filtering
                 if (group.items) {
                    return group.items.length > 0;
                 }
                 // For non-grouped items, check permissions
                 if ((group as any).pagePermission) {
                    const allPagePermissions = Object.keys((group as any).pagePermission).map(
                       pagePermission => ((group as any).pagePermission as any)[pagePermission]
                    );
                    return hasAnyPermission(allPagePermissions);
                 }
                 return true;
              }),
      [hasAnyPermission, isLoading, navMenuWithActiveState, data.length, isClient]
   );

   return (
      <Sidebar className='bg-white border-r border-gray-200' collapsible='icon' {...props}>
         <SidebarHeader className='bg-white border-b border-gray-200 p-3 group-data-[collapsible=icon]:p-1 group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:items-center group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:h-12'>
            <div className='flex items-center justify-center'>
               <LogoName width={60} height={60} />
            </div>
         </SidebarHeader>
         <SidebarContent className='bg-white group-data-[collapsible=icon]:p-1 group-data-[collapsible=icon]:overflow-visible'>
            {!isClient || isLoading ? (
               <SidebarSkeleton />
            ) : (
               <NavMain items={navMainWithActiveState} />
            )}
         </SidebarContent>
         <SidebarRail />
      </Sidebar>
   );
}

'use client';

import { Card } from '@/components/ui/card';
import { useState } from 'react';
import { useListZoneType, useGetAlgorithms } from '../api/queries';
import { ZoneTypeModal } from '../components/zone-type-modal';
import { ZoneTypeFilters } from '../components/zone-type-filters';
import { ZoneTypeTable } from '../components/zone-type-table';
import { ZoneAlgorithm } from '../types/zone-type';

export function ZoneTypePage() {
   const [page, setPage] = useState(1);
   const [limit] = useState(10);
   const [search, setSearch] = useState('');
   const [algorithm, setAlgorithm] = useState<ZoneAlgorithm | undefined>(undefined);
   const [isActive, setIsActive] = useState<boolean | undefined>(undefined);

   // Preload algorithms for the filters
   useGetAlgorithms();

   // Reset to first page when filters change
   const handleSearchChange = (value: string) => {
      setSearch(value);
      setPage(1);
   };

   const handleAlgorithmChange = (value: ZoneAlgorithm | undefined) => {
      setAlgorithm(value);
      setPage(1);
   };

   const handleStatusChange = (value: boolean | undefined) => {
      setIsActive(value);
      setPage(1);
   };

   // Function to clear all filters
   const handleClearFilters = () => {
      setSearch('');
      setAlgorithm(undefined);
      setIsActive(undefined);
      setPage(1);
   };

   const listZoneTypeQuery = useListZoneType({
      page,
      limit,
      search: search || undefined,
      algorithm: algorithm || undefined,
      isActive: isActive === true ? true : undefined,
      isInactive: isActive === false ? true : undefined,
      includeInactive: true,
      includeZoneCount: true,
   });

   // Calculate total zone types count from API meta data
   const totalZoneTypes = listZoneTypeQuery.data?.data?.total || 0;

   return (
      <div className='flex flex-1 flex-col gap-4 p-6'>
         <div className='flex justify-between items-center'>
            <h2 className='text-2xl font-semibold text-gray-900'>Zone Types</h2>
            <div className='flex items-center gap-4'>
               {/* Zone Type Info Cards */}
               <div className='flex gap-2'>
                  <div className='flex items-center gap-2 px-3 py-2 bg-gray-50 rounded-md border'>
                     <span className='text-sm text-gray-600'>Total Zone Types</span>
                     <span className='inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-blue-700 bg-blue-100 rounded-full'>
                        {totalZoneTypes}
                     </span>
                  </div>
               </div>
               <ZoneTypeModal mode='create' />
            </div>
         </div>

         <Card className='overflow-hidden py-4 px-4 rounded-sm'>
            <ZoneTypeFilters
               search={search}
               algorithm={algorithm}
               isActive={isActive}
               onSearchChange={handleSearchChange}
               onAlgorithmChange={handleAlgorithmChange}
               onStatusChange={handleStatusChange}
               isLoading={listZoneTypeQuery.isFetching && !listZoneTypeQuery.isLoading}
            />

            <ZoneTypeTable
               data={listZoneTypeQuery.data}
               isLoading={listZoneTypeQuery.isLoading}
               currentPage={page}
               onPageChange={(newPage: number) => setPage(newPage)}
               hasFilters={!!search || !!algorithm || isActive !== undefined}
               hasSearch={!!search}
               hasStatus={isActive !== undefined}
               onClearFilters={handleClearFilters}
            />
         </Card>
      </div>
   );
}
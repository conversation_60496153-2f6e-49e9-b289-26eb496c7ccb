import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import {
   VehicleCategoryResponse,
   ListVehicleCategoryParams,
   ListVehicleCategoryResponse,
} from '../types/vehicle-category';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

export const useListVehicleCategory = ({
   page,
   limit,
   search,
   sortBy,
   sortOrder,
}: ListVehicleCategoryParams = {}) => {
   const { hasPermission } = useRoleBasedAccess();

   return useQuery({
      placeholderData: keepPreviousData,
      enabled: hasPermission(RBAC_PERMISSIONS.VEHICLE_CATEGORY.LIST),
      queryKey: [
         'vehicle-categories',
         page,
         limit,
         search,
         sortBy,
         sortOrder,
      ],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<ListVehicleCategoryResponse> => {
         return apiClient.get('/vehicle-type/paginate', {
            params: {
               page,
               limit,
               search,
               sortBy,
               sortOrder,
            },
         });
      },
   });
};

export const useGetVehicleCategory = (id: string | null) => {
   return useQuery({
      queryKey: ['vehicle-category', id],
      queryFn: (): Promise<VehicleCategoryResponse> => {
         return apiClient.get(`/vehicle-type/${id || ''}`);
      },
      enabled: !!id,
      refetchOnWindowFocus: false,
   });
};


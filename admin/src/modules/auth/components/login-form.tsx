'use client';

import { ErrorMessage } from '@/components/error-message';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import { cn } from '@/lib/utils';
import { toast } from '@/lib/toast';
import { useAuthStore } from '@/store/auth-store';
import { zodResolver } from '@hookform/resolvers/zod';
import { EyeIcon, EyeOffIcon } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { useLogin } from '../api/mutations';
import { apiClient } from '@/lib/api-client';
import { MyPermissions } from '@/role-based-access/queries';
import { PAGE_ROUTE_PERMISSIONS } from '@/role-based-access/page-permissions';

const loginSchema = z.object({
   email: z.string().email('Please enter a valid email address'),
   password: z.string().min(1, 'Password is required'),
});

type LoginFormData = z.infer<typeof loginSchema>;

export function LoginForm({ className, ...props }: React.ComponentProps<'div'>) {
   const [showPassword, setShowPassword] = useState(false);
   const router = useRouter();
   const authStore = useAuthStore(state => state);

   const {
      register,
      handleSubmit,
      formState: { errors },
   } = useForm<LoginFormData>({
      resolver: zodResolver(loginSchema),
      defaultValues: {
         email: '',
         password: '',
      },
   });

   const loginMutation = useLogin();

   const onSubmit = async (data: LoginFormData) => {
      const payload = {
         email: data.email,
         password: data.password,
      };

      loginMutation.mutate(payload, {
         onSuccess: async response => {
            const userId = response?.data.email;
            authStore.setUserId(String(userId));

            const token = response.data.accessToken;
            const refreshToken = response.data.refreshToken;
            authStore.setToken(token, refreshToken);

            const myPermissions = (await apiClient.get('/permissions/my-permissions'))
               .data as unknown as MyPermissions['data'];

            const myPermissionsList = myPermissions?.map(permission => permission.name);

            const availableRoutesWithPermissions = PAGE_ROUTE_PERMISSIONS.filter(menu => {
               const allPagePermissions = Object.keys(menu.pagePermission).map(
                  pagePermission => (menu.pagePermission as any)[pagePermission]
               );

               return myPermissionsList?.some(myPermission =>
                  allPagePermissions.includes(myPermission)
               );
            }).map(route => route.url);

            const firstAvailableRoute = availableRoutesWithPermissions?.[0];

            if (firstAvailableRoute) {
               toast.success('Login successful');
               router.push(firstAvailableRoute);
            } else {
               authStore.reset();
               router.push('/');
               toast.error('This user doesnot have permission to visit any pages');
            }
         },
      });
   };

   return (
      <div className={cn('flex flex-col gap-6', className)} {...props}>
         <Card>
            <CardHeader className='text-center'>
               <CardTitle className='text-xl'>Admin Portal</CardTitle>
            </CardHeader>
            <CardContent>
               <form onSubmit={handleSubmit(onSubmit)}>
                  <div className='grid gap-6'>
                     <div className='grid gap-6'>
                        <div className='grid gap-3'>
                           <Label htmlFor='email'>Email</Label>
                           <Input
                              id='email'
                              type='email'
                              placeholder='Enter your email'
                              autoComplete='email'
                              {...register('email')}
                           />
                           <ErrorMessage error={errors.email} />
                        </div>
                        <div className='grid gap-3'>
                           <div className='flex items-center justify-between'>
                              <Label htmlFor='password'>Password</Label>
                              <Link
                                 href='/auth/forgot-password'
                                 className='text-sm text-primary hover:underline'
                              >
                                 Forgot password?
                              </Link>
                           </div>
                           <div className='relative'>
                              <Input
                                 id='password'
                                 type={showPassword ? 'text' : 'password'}
                                 placeholder='Enter your password'
                                 autoComplete='current-password'
                                 {...register('password')}
                              />
                              <Button
                                 type='button'
                                 variant='ghost'
                                 size='sm'
                                 className='absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent'
                                 onClick={() => setShowPassword(!showPassword)}
                              >
                                 {showPassword ? (
                                    <EyeOffIcon className='h-4 w-4 text-muted-foreground' />
                                 ) : (
                                    <EyeIcon className='h-4 w-4 text-muted-foreground' />
                                 )}
                              </Button>
                           </div>
                           <ErrorMessage error={errors.password} />
                        </div>
                        <Button type='submit' className='w-full' disabled={loginMutation.isPending}>
                           {loginMutation.isPending ? (
                              <>
                                 Logging in...
                                 <Spinner className='ml-2' />
                              </>
                           ) : (
                              'Login'
                           )}
                        </Button>
                     </div>
                  </div>
               </form>
            </CardContent>
         </Card>
         <div className='text-muted-foreground *:[a]:hover:text-primary text-center text-xs text-balance *:[a]:underline *:[a]:underline-offset-4'>
            By clicking continue, you agree to our <a href='#'>Terms of Service</a> and{' '}
            <a href='#'>Privacy Policy</a>.
         </div>
      </div>
   );
}

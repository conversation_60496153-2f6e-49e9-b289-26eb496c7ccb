import { DiscountType, DiscountCalculation, Coupon } from '../types/coupon';

/**
 * Format coupon code to uppercase
 */
export function formatCouponCode(code: string): string {
  return code.toUpperCase().trim();
}

/**
 * Calculate discount amount and final fare based on discount type
 */
export function calculateDiscountPreview(
  originalFare: number,
  discountType: DiscountType,
  discountValue: number,
  maxDiscountLimit?: number
): DiscountCalculation {
  let discountAmount = 0;
  let discountPercentage: number | undefined;

  if (discountType === DiscountType.PERCENTAGE) {
    // Calculate percentage discount
    discountAmount = (originalFare * discountValue) / 100;
    discountPercentage = discountValue;

    // Apply max discount limit if specified
    if (maxDiscountLimit && discountAmount > maxDiscountLimit) {
      discountAmount = maxDiscountLimit;
    }
  } else if (discountType === DiscountType.FLAT) {
    // Flat discount
    discountAmount = discountValue;
  }

  // Ensure discount doesn't exceed original fare
  if (discountAmount > originalFare) {
    discountAmount = originalFare;
  }

  const finalFare = Math.max(0, originalFare - discountAmount);

  return {
    originalFare,
    discountAmount: parseFloat(discountAmount.toFixed(2)),
    finalFare: parseFloat(finalFare.toFixed(2)),
    discountPercentage,
  };
}

/**
 * Validate discount value based on type
 * - PERCENTAGE: Must be between 0 and 100
 * - FLAT: Must be positive
 * - Max 2 decimal places
 */
export function validateDiscountValue(
  value: number,
  discountType: DiscountType
): { valid: boolean; error?: string } {
  // Check decimal places (max 2)
  const decimalPart = value.toString().split('.')[1];
  if (decimalPart && decimalPart.length > 2) {
    return { valid: false, error: 'Maximum 2 decimal places allowed' };
  }

  // Check value range based on type
  if (discountType === DiscountType.PERCENTAGE) {
    if (value < 0 || value > 100) {
      return { valid: false, error: 'Percentage must be between 0 and 100' };
    }
  } else if (discountType === DiscountType.FLAT) {
    if (value <= 0) {
      return { valid: false, error: 'Amount must be greater than 0' };
    }
  }

  return { valid: true };
}

/**
 * Format applicability summary for display
 * Falls back to backend summary if available
 */
export function formatApplicabilitySummary(coupon: Coupon): string {
  // Use backend-generated summary if available
  if (coupon.applicabilitySummary) {
    return coupon.applicabilitySummary;
  }

  // Generate client-side summary as fallback
  const type = coupon.applicabilityType;

  if (type === 'CITY' && coupon.couponZones) {
    const zoneCount = coupon.couponZones.length;
    const cityNames = Array.from(
      new Set(coupon.couponZones.map(cz => cz.zone.city?.name).filter(Boolean))
    ).join(', ');
    return `${zoneCount} zone(s) in ${cityNames || 'selected cities'}`;
  }

  if (type === 'PRODUCT' && coupon.couponProducts) {
    const productNames = coupon.couponProducts.map(cp => cp.product.name).join(', ');
    return `Products: ${productNames}`;
  }

  if (type === 'CITY_PRODUCT' && coupon.couponCityProducts) {
    const count = coupon.couponCityProducts.length;
    return `${count} city-product combination(s)`;
  }

  if (type === 'USER' && coupon.couponUsers) {
    const count = coupon.couponUsers.length;
    return `${count} specific user(s)`;
  }

  return 'Applicability not specified';
}

/**
 * Check if coupon is expired
 */
export function isCouponExpired(endDate: string): boolean {
  return new Date(endDate) < new Date();
}

/**
 * Check if coupon is active and not expired
 */
export function isCouponValid(coupon: Coupon): boolean {
  return coupon.isActive && !isCouponExpired(coupon.endDate);
}

/**
 * Calculate coupon usage percentage
 */
export function calculateUsagePercentage(usageCount: number, usageLimit: number): number {
  if (usageLimit === 0) return 0;
  return Math.min(100, (usageCount / usageLimit) * 100);
}

/**
 * Get usage status color based on percentage
 */
export function getUsageStatusColor(percentage: number): 'green' | 'yellow' | 'red' {
  if (percentage < 70) return 'green';
  if (percentage < 90) return 'yellow';
  return 'red';
}

/**
 * Format date range display
 */
export function formatDateRange(startDate: string, endDate: string): string {
  const start = new Date(startDate);
  const end = new Date(endDate);

  const formatOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  };

  const startStr = start.toLocaleDateString('en-US', formatOptions);
  const endStr = end.toLocaleDateString('en-US', formatOptions);

  return `${startStr} - ${endStr}`;
}

/**
 * Calculate duration in days between start and end date
 */
export function calculateDurationDays(startDate: string, endDate: string): number {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diff = end.getTime() - start.getTime();
  return Math.ceil(diff / (1000 * 60 * 60 * 24));
}

/**
 * Format duration for display (e.g., "30 days", "3 months")
 */
export function formatDuration(startDate: string, endDate: string): string {
  const days = calculateDurationDays(startDate, endDate);

  if (days === 1) return '1 day';
  if (days < 30) return `${days} days`;

  const months = Math.floor(days / 30);
  if (months === 1) return '1 month';
  if (months < 12) return `${months} months`;

  const years = Math.floor(days / 365);
  if (years === 1) return '1 year';
  return `${years} years`;
}

/**
 * Validate date range
 */
export function validateDateRange(
  startDate: string,
  endDate: string
): { valid: boolean; error?: string } {
  const start = new Date(startDate);
  const end = new Date(endDate);

  if (end <= start) {
    return { valid: false, error: 'End date must be after start date' };
  }

  return { valid: true };
}

/**
 * Format discount display (e.g., "20%", "$50")
 */
export function formatDiscountDisplay(
  discountType: DiscountType,
  discountValue: string | number,
  currency: string = 'USD'
): string {
  const value = typeof discountValue === 'string' ? parseFloat(discountValue) : discountValue;

  if (discountType === DiscountType.PERCENTAGE) {
    return `${value}%`;
  }

  // For flat amount, show currency symbol
  const currencySymbol = currency === 'USD' ? '$' : currency;
  return `${currencySymbol}${value.toFixed(2)}`;
}

/**
 * Handle decimal input for discount values
 * Limits to 2 decimal places
 */
export function handleDecimalInput(value: string): number | undefined {
  if (!value || value === '') return undefined;

  // Remove any non-numeric characters except decimal point
  const cleaned = value.replace(/[^\d.]/g, '');

  // Split by decimal point
  const parts = cleaned.split('.');

  if (parts.length > 2) {
    // Multiple decimal points, invalid
    return undefined;
  }

  if (parts.length === 2) {
    // Has decimal, limit to 2 places
    const decimal = parts[1].slice(0, 2);
    const formatted = `${parts[0]}.${decimal}`;
    return parseFloat(formatted);
  }

  return parseFloat(cleaned);
}

/**
 * Get display value for number input
 * Preserves user's decimal input while typing
 */
export function getNumberDisplayValue(value: number | undefined | null): string {
  if (value === undefined || value === null) return '';
  return value.toString();
}

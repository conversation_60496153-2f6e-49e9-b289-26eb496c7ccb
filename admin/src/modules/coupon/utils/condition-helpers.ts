import {
  CouponConditionBlock,
  CouponConditionType,
  RideConditionConfig,
} from '../types/coupon';

// ============================================================================
// OPERATOR MAPPING
// ============================================================================

// Map UI operators to JSONLogic operators
const OPERATOR_MAP: Record<string, string> = {
  '=': '==',
  '!=': '!=',
  '<': '<',
  '<=': '<=',
  '>': '>',
  '>=': '>=',
};

// Reverse map for parsing
const REVERSE_OPERATOR_MAP: Record<string, string> = {
  '==': '=',
  '!=': '!=',
  '<': '<',
  '<=': '<=',
  '>': '>',
  '>=': '>=',
};

// ============================================================================
// RIDE COUNT CONDITION LOGIC GENERATION
// ============================================================================

/**
 * Generate JSON Logic for ride count condition with comparison operators
 * @param rideConfigs - Array with single ride configuration (one condition per block)
 * @returns JSON Logic object for ride conditions
 *
 * Examples:
 * - rides = 1: { "==": [{ "var": "rideCount" }, 1] }
 * - rides < 10: { "<": [{ "var": "rideCount" }, 10] }
 * - rides > 10: { ">": [{ "var": "rideCount" }, 10] }
 */
function generateRideLogic(rideConfigs: RideConditionConfig[]): any {
  if (!rideConfigs || rideConfigs.length === 0) {
    return null;
  }

  // Use the first config (single condition per block)
  const config = rideConfigs[0];
  const jsonLogicOperator = OPERATOR_MAP[config.operator];

  if (!jsonLogicOperator) return null;

  // Generate: { "operator": [{ "var": "rideCount" }, value] }
  return {
    [jsonLogicOperator]: [{ var: 'rideCount' }, config.value],
  };
}

/**
 * Parse ride logic back to config
 * Converts JSONLogic back to single RideConditionConfig
 */
function parseRideLogic(logic: any): RideConditionConfig[] {
  if (!logic || typeof logic !== 'object') {
    return [];
  }

  // Check each possible operator
  for (const [jsonOp, uiOp] of Object.entries(REVERSE_OPERATOR_MAP)) {
    if (logic[jsonOp] && Array.isArray(logic[jsonOp]) && logic[jsonOp].length === 2) {
      const [varPart, value] = logic[jsonOp];

      // Verify it's a rideCount variable
      if (varPart?.var === 'rideCount' && typeof value === 'number') {
        return [
          {
            operator: uiOp as '=' | '!=' | '<' | '<=' | '>' | '>=',
            value,
          },
        ];
      }
    }
  }

  return [];
}

// ============================================================================
// CONDITION BLOCK FUNCTIONS
// ============================================================================

/**
 * Generate JSON Logic for a single condition block based on its type
 * @param block - Condition block to convert
 * @returns JSON Logic object for this condition
 */
function generateConditionBlockLogic(block: CouponConditionBlock): any {
  // Only RIDES condition type is supported
  if (block.type === CouponConditionType.RIDES) {
    return generateRideLogic(block.config);
  }

  return null;
}

/**
 * Generate complete JSON Logic from multiple condition blocks
 * @param blocks - Array of condition blocks
 * @returns Complete JSON Logic object or null if no blocks
 */
export function generateJsonLogicFromBlocks(blocks: CouponConditionBlock[]): any {
  if (!blocks || blocks.length === 0) {
    return null;
  }

  // Generate logic for each block
  const blockLogics = blocks.map(block => generateConditionBlockLogic(block)).filter(Boolean);

  if (blockLogics.length === 0) {
    return null;
  }

  if (blockLogics.length === 1) {
    return blockLogics[0];
  }

  // Combine multiple blocks using their operators
  let result = blockLogics[0];
  for (let i = 1; i < blockLogics.length; i++) {
    const operator = blocks[i - 1].operator;
    result = {
      [operator]: [result, blockLogics[i]],
    };
  }

  return result;
}

/**
 * Check if logic represents a ride condition
 * Only RIDES condition type is supported
 */
function getConditionType(logic: any): CouponConditionType | null {
  if (!logic || typeof logic !== 'object') return null;

  // Check for ride condition (contains "rideCount" variable)
  const jsonStr = JSON.stringify(logic);
  if (jsonStr.includes('"var":"rideCount"')) {
    return CouponConditionType.RIDES;
  }

  return null;
}

/**
 * Parse JSON Logic to extract condition blocks
 * @param condition - JSON Logic condition object
 * @returns Array of condition blocks
 */
export function parseJsonLogicToBlocks(condition: any): CouponConditionBlock[] {
  if (!condition || typeof condition !== 'object') {
    return [];
  }

  const blocks: CouponConditionBlock[] = [];

  /**
   * Recursively extract condition blocks from nested logic
   */
  function extractBlocks(logic: any, currentOperator: 'or' | 'and' = 'or'): void {
    const condType = getConditionType(logic);

    if (condType) {
      // This is a ride condition block, parse it
      const config = parseRideLogic(logic);

      if (config.length > 0) {
        blocks.push({
          type: condType,
          config,
          operator: currentOperator,
        });
      }
      return;
    }

    // Check if this is a combining operator
    if (logic.or && Array.isArray(logic.or) && logic.or.length === 2) {
      const [first, second] = logic.or;
      extractBlocks(first, 'or');
      extractBlocks(second, 'or');
      return;
    }

    if (logic.and && Array.isArray(logic.and) && logic.and.length === 2) {
      const [first, second] = logic.and;
      extractBlocks(first, 'and');
      extractBlocks(second, 'and');
      return;
    }
  }

  extractBlocks(condition);

  // Remove operator from last block (not needed for UI)
  if (blocks.length > 0) {
    blocks[blocks.length - 1].operator = 'or';
  }

  return blocks;
}

/**
 * Validate a condition block based on its type
 * @param block - Condition block to validate
 * @returns Error message if invalid, null if valid
 */
export function validateConditionBlock(block: CouponConditionBlock): string | null {
  if (!block.config || block.config.length === 0) {
    return 'At least one configuration is required';
  }

  // Only RIDES condition type is supported
  if (block.type === CouponConditionType.RIDES) {
    const rideConfigs = block.config;
    for (const config of rideConfigs) {
      // Validate operator
      if (!config.operator || !['=', '!=', '<', '<=', '>', '>='].includes(config.operator)) {
        return 'Valid operator must be specified (=, !=, <, <=, >, >=)';
      }

      // Validate value
      if (config.value === undefined || config.value === null || config.value === '') {
        return 'Ride count value must be specified';
      }

      if (typeof config.value !== 'number' || config.value < 0) {
        return 'Ride count must be a non-negative number (≥ 0)';
      }

      if (!Number.isInteger(config.value)) {
        return 'Ride count must be a whole number';
      }
    }
    return null;
  }

  return 'Unknown condition type';
}

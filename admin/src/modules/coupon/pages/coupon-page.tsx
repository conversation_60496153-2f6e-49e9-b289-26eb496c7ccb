'use client';

import { Card } from '@/components/ui/card';
import { useState } from 'react';
import { useListCoupons } from '../api/queries';
import { CouponFilters } from '../components/coupon-filters';
import { CouponTable } from '../components/coupon-table';
import { CouponModal } from '../components/coupon-modal';
import { ApplicabilityType, DiscountType } from '../types/coupon';

export function CouponPage() {
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [search, setSearch] = useState('');
  const [applicabilityType, setApplicabilityType] = useState<ApplicabilityType | undefined>();
  const [discountType, setDiscountType] = useState<DiscountType | undefined>();
  const [isActive, setIsActive] = useState<boolean | undefined>();
  const [isExpired, setIsExpired] = useState<boolean | undefined>();
  const [editingCouponId, setEditingCouponId] = useState<string | null>(null);

  // Reset to first page when filters change
  const handleSearchChange = (value: string) => {
    setSearch(value);
    setPage(1);
  };

  const handleApplicabilityTypeChange = (value: ApplicabilityType | undefined) => {
    setApplicabilityType(value);
    setPage(1);
  };

  const handleDiscountTypeChange = (value: DiscountType | undefined) => {
    setDiscountType(value);
    setPage(1);
  };

  const handleActiveChange = (value: boolean | undefined) => {
    setIsActive(value);
    setPage(1);
  };

  const handleExpiredChange = (value: boolean | undefined) => {
    setIsExpired(value);
    setPage(1);
  };

  const listQuery = useListCoupons({
    page,
    limit,
    search: search || undefined,
    applicabilityType,
    discountType,
    isActive,
    isExpired,
  });

  return (
    <div className='flex flex-1 flex-col gap-4 p-6'>
      <div className='flex justify-between items-center'>
        <h2 className='text-2xl font-semibold text-gray-900'>Coupons</h2>
        <div className='flex items-center gap-4'>
          <CouponModal mode='create' />
        </div>
      </div>

      <Card className='overflow-hidden py-4 px-4 rounded-sm'>
        <CouponFilters
          search={search}
          applicabilityType={applicabilityType}
          discountType={discountType}
          isActive={isActive}
          isExpired={isExpired}
          onSearchChange={handleSearchChange}
          onApplicabilityTypeChange={handleApplicabilityTypeChange}
          onDiscountTypeChange={handleDiscountTypeChange}
          onActiveChange={handleActiveChange}
          onExpiredChange={handleExpiredChange}
          isLoading={listQuery.isFetching && !listQuery.isLoading}
        />

        <CouponTable
          data={listQuery.data}
          isLoading={listQuery.isLoading}
          currentPage={page}
          onPageChange={(newPage: number) => setPage(newPage)}
          onOpenEditModal={(id) => setEditingCouponId(id)}
        />
      </Card>

      {/* Edit Modal */}
      <CouponModal
        mode='edit'
        couponId={editingCouponId}
        isOpen={!!editingCouponId}
        onClose={() => setEditingCouponId(null)}
      />
    </div>
  );
}

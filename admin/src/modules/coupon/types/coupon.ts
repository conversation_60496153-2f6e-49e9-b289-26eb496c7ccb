// ============================================
// ENUMS MATCHING BACKEND
// ============================================

export enum DiscountType {
  PERCENTAGE = 'PERCENTAGE',
  FLAT = 'FLAT',
}

export enum ApplicabilityType {
  CITY = 'CITY',
  PRODUCT = 'PRODUCT',
  CITY_PRODUCT = 'CITY_PRODUCT',
  USER = 'USER',
}

// Condition types for coupon rules
export enum CouponConditionType {
  RIDES = 'rides', // Number of rides with comparison operators
}

// ============================================
// CONDITION CONFIGURATION TYPES
// ============================================

export interface RideConditionConfig {
  operator: '=' | '!=' | '<' | '<=' | '>' | '>='; // Comparison operator
  value: number | ''; // Number of rides to compare against (allows empty string during input)
}

// Union type for all config types (currently only RideConditionConfig)
export type ConditionConfig = RideConditionConfig;

// Each condition block is self-contained with its own type and configuration
export interface CouponConditionBlock {
  type: CouponConditionType;
  config: RideConditionConfig[]; // Array to support multiple ride conditions
  operator: 'and' | 'or'; // How to combine with the next condition block
}

// ============================================
// ZONE & CITY TYPES
// ============================================

export interface ZoneType {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

export interface City {
  id: string;
  name: string;
  countryId: string;
  createdAt: string;
  updatedAt: string;
}

export interface Zone {
  id: string;
  name: string;
  cityId: string;
  zoneTypeId: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
  zoneType?: ZoneType;
  city?: City;
}

export interface GetCityZonesResponse {
  success: boolean;
  message: string;
  data: Zone[];
  timestamp: number;
}

// ============================================
// PRODUCT TYPES
// ============================================

export interface Product {
  id: string;
  name: string;
  icon?: string | null;
  createdAt: string;
  updatedAt: string;
}

// ============================================
// USER TYPES
// ============================================

export interface UserProfile {
  id: string;
  firstName: string;
  lastName: string;
  email?: string | null;
  phoneNumber?: string | null;
}

// ============================================
// APPLICABILITY RELATION TYPES
// ============================================

export interface CouponZone {
  id: string;
  couponId: string;
  zoneId: string;
  createdAt: string;
  updatedAt: string;
  zone: Zone;
}

export interface CouponProduct {
  id: string;
  couponId: string;
  productId: string;
  createdAt: string;
  updatedAt: string;
  product: Product;
}

export interface CouponCityProduct {
  id: string;
  couponId: string;
  cityProductId: string;
  createdAt: string;
  updatedAt: string;
  // cityProduct relation would be nested here if needed
}

export interface CouponUser {
  id: string;
  couponId: string;
  userProfileId: string;
  createdAt: string;
  updatedAt: string;
  userProfile: UserProfile;
}

// ============================================
// CORE COUPON ENTITY
// ============================================

export interface Coupon {
  id: string;
  name: string;
  code: string; // Auto-uppercased unique identifier
  thumbnail?: string | null;
  description?: string | null;
  startDate: string; // ISO 8601 format
  endDate: string; // ISO 8601 format
  discountType: DiscountType;
  discountValue: string; // Prisma Decimal → string
  maxDiscountLimit?: string | null; // For PERCENTAGE type - caps max discount
  minFareCondition?: string | null; // Minimum order value
  usageLimit: number; // Total usage cap
  usageCount: number; // Current usage (read-only, computed)
  applicabilityType: ApplicabilityType;
  applyConditionLogic?: any | null; // JSONLogic rules for advanced conditions
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
  // Relations
  couponZones?: CouponZone[];
  couponProducts?: CouponProduct[];
  couponCityProducts?: CouponCityProduct[];
  couponUsers?: CouponUser[];
  // Computed field from backend
  applicabilitySummary?: string;
}

// ============================================
// API RESPONSE STRUCTURES
// ============================================

export interface CouponResponse {
  success: boolean;
  message: string;
  data: Coupon;
  timestamp: number;
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface ListCouponResponse {
  success: boolean;
  message: string;
  data: Coupon[];
  meta: PaginationMeta;
  timestamp: number;
}

// ============================================
// APPLICABILITY PAYLOAD STRUCTURE
// ============================================

export interface CouponApplicabilityConfig {
  zoneIds?: string[]; // For CITY type
  productIds?: string[]; // For PRODUCT type
  cityProductIds?: string[]; // For CITY_PRODUCT type
  userIds?: string[]; // For USER type
}

// ============================================
// REQUEST PAYLOADS
// ============================================

export interface CreateCouponPayload {
  name: string;
  code: string;
  thumbnail?: string;
  description?: string;
  startDate: string; // ISO 8601
  endDate: string; // ISO 8601
  discountType: DiscountType;
  discountValue: number;
  maxDiscountLimit?: number; // Only for PERCENTAGE type
  minFareCondition?: number;
  usageLimit: number;
  applicabilityType: ApplicabilityType;
  applyConditionLogic?: any; // JSONLogic
  isActive?: boolean;
}

export interface CreateCouponRequest {
  coupon: CreateCouponPayload;
  applicability: CouponApplicabilityConfig;
}

export interface UpdateCouponPayload {
  name?: string;
  code?: string;
  thumbnail?: string;
  description?: string;
  startDate?: string;
  endDate?: string;
  discountType?: DiscountType;
  discountValue?: number;
  maxDiscountLimit?: number;
  minFareCondition?: number;
  usageLimit?: number;
  applicabilityType?: ApplicabilityType;
  applyConditionLogic?: any;
  isActive?: boolean;
}

export interface UpdateCouponRequest {
  coupon: UpdateCouponPayload;
  applicability: CouponApplicabilityConfig;
}

// ============================================
// QUERY PARAMETERS
// ============================================

export interface ListCouponParams {
  page?: number;
  limit?: number;
  search?: string; // Searches name, code, or description
  applicabilityType?: ApplicabilityType;
  discountType?: DiscountType;
  isActive?: boolean;
  cityId?: string;
  productId?: string;
  isExpired?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// ============================================
// HUMAN-READABLE LABELS
// ============================================

export const DISCOUNT_TYPE_LABELS: Record<DiscountType, string> = {
  [DiscountType.PERCENTAGE]: 'Percentage',
  [DiscountType.FLAT]: 'Flat Amount',
};

export const APPLICABILITY_TYPE_LABELS: Record<ApplicabilityType, string> = {
  [ApplicabilityType.CITY]: 'City Zones',
  [ApplicabilityType.PRODUCT]: 'Products',
  [ApplicabilityType.CITY_PRODUCT]: 'City-Product',
  [ApplicabilityType.USER]: 'Specific Users',
};

export const COUPON_CONDITION_TYPE_LABELS: Record<CouponConditionType, string> = {
  [CouponConditionType.RIDES]: 'Number of Rides',
};

// Operator labels for ride conditions
export const RIDE_OPERATOR_LABELS: Record<string, string> = {
  '=': 'Equals',
  '!=': 'Not Equals',
  '<': 'Less Than',
  '<=': 'Less Than or Equal To',
  '>': 'Greater Than',
  '>=': 'Greater Than or Equal To',
};

// ============================================
// HELPER TYPES FOR UI
// ============================================

// For city selection in zone selector
export interface CityOption {
  value: string;
  label: string;
}

// For zone multi-select
export interface ZoneOption {
  value: string;
  label: string;
  cityName: string;
}

// For product multi-select
export interface ProductOption {
  value: string;
  label: string;
  icon?: string | null;
}

// For discount preview calculator
export interface DiscountCalculation {
  originalFare: number;
  discountAmount: number;
  finalFare: number;
  discountPercentage?: number;
}

'use client';

import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import { useState, useRef } from 'react';
import { Controller, Control } from 'react-hook-form';
import { ApplicabilityType } from '../types/coupon';
import { useGetCityZones } from '../api/queries';
import { useListCities } from '@/modules/city/api/queries';
import { useListProduct } from '@/modules/product/api/queries';
import { useListCityProducts } from '@/modules/city/api/city-product-queries';
import { useListRiders } from '@/modules/riders/api/queries';
import { ErrorMessage } from '@/components/error-message';
import MultipleSelector, { Option } from '@/components/ui/multiselect';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
   Command,
   CommandEmpty,
   CommandGroup,
   CommandInput,
   CommandItem,
   CommandList,
} from '@/components/ui/command';
import { Check, ChevronsUpDown, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Rider } from '@/modules/riders/types/rider';

interface ApplicabilitySelectorProps {
   control: Control<any>;
   applicabilityType: ApplicabilityType;
   errors?: any;
}

export function ApplicabilitySelector({
   control,
   applicabilityType,
   errors,
}: ApplicabilitySelectorProps) {
   const [selectedCity, setSelectedCity] = useState<string>('');
   const [selectedCities, setSelectedCities] = useState<Option[]>([]);

   // User selection state for multi-select combobox
   const [selectedUsers, setSelectedUsers] = useState<Rider[]>([]);
   const [userSearchQuery, setUserSearchQuery] = useState('');
   const [userComboboxOpen, setUserComboboxOpen] = useState(false);
   const userTriggerRef = useRef<HTMLButtonElement>(null);

   const citiesQuery = useListCities({ page: 1, limit: 100, status: 'active' });
   const zonesQuery = useGetCityZones(selectedCity, applicabilityType === ApplicabilityType.CITY);

   // Products query
   const productsQuery = useListProduct({
      page: 1,
      limit: 100,
      isEnabled: 'true',
   });

   // City products query
   const cityProductsQuery = useListCityProducts(selectedCity || 'all', {
      page: 1,
      limit: 100,
   });

   // Riders query
   const ridersQuery = useListRiders({
      page: 1,
      limit: 50,
      search: userSearchQuery,
   });

   // Convert cities to Option format
   const cityOptions: Option[] = (citiesQuery.data?.data || []).map((city: any) => ({
      value: city.id,
      label: city.name,
   }));

   // Convert zones to Option format
   const zoneOptions: Option[] = (zonesQuery.data?.data || []).map((zone: any) => ({
      value: zone.id,
      label: zone.name,
   }));

   // Convert products to Option format
   const productOptions: Option[] = (productsQuery.data?.data || []).map((product: any) => ({
      value: product.id,
      label: product.name,
      icon: product.icon,
   }));

   // Convert city products to Option format
   const cityProductOptions: Option[] = (cityProductsQuery.data?.data || []).map(
      (cityProduct: any) => ({
         value: cityProduct.id,
         label: cityProduct.product?.name || 'Product',
      })
   );

   // Render based on applicability type
   switch (applicabilityType) {
      case ApplicabilityType.CITY:
         return (
            <div className='space-y-4'>
               <div className='flex flex-col gap-2'>
                  <Label htmlFor='city-select'>Select City *</Label>
                  <Select value={selectedCity} onValueChange={setSelectedCity}>
                     <SelectTrigger id='city-select' className='w-full'>
                        <SelectValue placeholder='Select a city' />
                     </SelectTrigger>
                     <SelectContent>
                        {citiesQuery.isLoading && (
                           <div className='flex items-center justify-center p-4'>
                              <Spinner className='h-4 w-4' />
                           </div>
                        )}
                        {citiesQuery.data?.data?.map((city: any) => (
                           <SelectItem key={city.id} value={city.id}>
                              {city.name}
                           </SelectItem>
                        ))}
                     </SelectContent>
                  </Select>
               </div>

               <div className='flex flex-col gap-2'>
                  <Label>Select Zones *</Label>
                  <Controller
                     control={control}
                     name='applicability.zoneIds'
                     render={({ field }) => {
                        // Show loading state only when city is selected and loading
                        if (selectedCity && zonesQuery.isLoading) {
                           return (
                              <div className='flex items-center justify-center p-8 border rounded-md'>
                                 <Spinner className='h-6 w-6' />
                              </div>
                           );
                        }

                        // Show no zones message only when city is selected but no zones found
                        if (selectedCity && !zonesQuery.data?.data?.length) {
                           return (
                              <div className='p-4 border rounded-md text-center text-sm text-gray-500'>
                                 No zones found for this city
                              </div>
                           );
                        }

                        // Convert field value (string[]) to Option[]
                        const selectedOptions = zoneOptions.filter(opt =>
                           field.value?.includes(opt.value)
                        );

                        // Always show the selector, but disable it when no city is selected
                        return (
                           <div className={!selectedCity ? 'opacity-50 pointer-events-none' : ''}>
                              <MultipleSelector
                                 value={selectedOptions}
                                 onChange={options => {
                                    field.onChange(options.map(opt => opt.value));
                                 }}
                                 defaultOptions={zoneOptions}
                                 placeholder={
                                    !selectedCity
                                       ? 'Please select a city first...'
                                       : 'Select zones...'
                                 }
                                 emptyIndicator={
                                    <p className='text-center text-sm text-gray-500'>
                                       No zones found
                                    </p>
                                 }
                                 showSelectAll={true}
                                 disabled={!selectedCity}
                              />
                           </div>
                        );
                     }}
                  />
                  {errors?.applicability?.zoneIds && (
                     <ErrorMessage error={errors.applicability.zoneIds} />
                  )}
               </div>
            </div>
         );

      case ApplicabilityType.PRODUCT:
         return (
            <div className='flex flex-col gap-2'>
               <Label>Select Products *</Label>
               <Controller
                  control={control}
                  name='applicability.productIds'
                  render={({ field }) => {
                     if (productsQuery.isLoading) {
                        return (
                           <div className='flex items-center justify-center p-8 border rounded-md'>
                              <Spinner className='h-6 w-6' />
                           </div>
                        );
                     }

                     if (!productsQuery.data?.data?.length) {
                        return (
                           <div className='p-4 border rounded-md text-center text-sm text-gray-500'>
                              No products available
                           </div>
                        );
                     }

                     // Convert field value (string[]) to Option[]
                     const selectedOptions = productOptions.filter(opt =>
                        field.value?.includes(opt.value)
                     );

                     return (
                        <MultipleSelector
                           value={selectedOptions}
                           onChange={options => {
                              field.onChange(options.map(opt => opt.value));
                           }}
                           defaultOptions={productOptions}
                           placeholder='Select products...'
                           emptyIndicator={
                              <p className='text-center text-sm text-gray-500'>
                                 No products found
                              </p>
                           }
                           showSelectAll={true}
                        />
                     );
                  }}
               />
               {errors?.applicability?.productIds && (
                  <ErrorMessage error={errors.applicability.productIds} />
               )}
            </div>
         );

      case ApplicabilityType.CITY_PRODUCT:
         return (
            <div className='space-y-4'>
               <div className='flex flex-col gap-2'>
                  <Label>Select Cities *</Label>
                  <MultipleSelector
                     value={selectedCities}
                     onChange={options => {
                        setSelectedCities(options);
                        // Set the first selected city for the city products query
                        if (options.length > 0) {
                           setSelectedCity(options[0].value);
                        } else {
                           setSelectedCity('');
                        }
                     }}
                     defaultOptions={cityOptions}
                     placeholder='Select cities...'
                     emptyIndicator={
                        <p className='text-center text-sm text-gray-500'>No cities found</p>
                     }
                     loadingIndicator={
                        <div className='flex items-center justify-center p-4'>
                           <Spinner className='h-4 w-4 mr-2' />
                           <span>Loading cities...</span>
                        </div>
                     }
                  />
               </div>

               <div className='flex flex-col gap-2'>
                  <Label>Select City-Product Combinations *</Label>
                  <Controller
                     control={control}
                     name='applicability.cityProductIds'
                     render={({ field }) => {
                        // Show loading state only when city is selected and loading
                        if (selectedCity && cityProductsQuery.isLoading) {
                           return (
                              <div className='flex items-center justify-center p-8 border rounded-md'>
                                 <Spinner className='h-6 w-6' />
                              </div>
                           );
                        }

                        // Show no products message only when city is selected but no products found
                        if (selectedCity && !cityProductsQuery.data?.data?.length) {
                           return (
                              <div className='p-4 border rounded-md text-center text-sm text-gray-500'>
                                 No city-product combinations available for selected cities
                              </div>
                           );
                        }

                        // Convert field value (string[]) to Option[]
                        const selectedOptions = cityProductOptions.filter(opt =>
                           field.value?.includes(opt.value)
                        );

                        // Always show the selector, but disable it when no city is selected
                        return (
                           <div className={!selectedCity ? 'opacity-50 pointer-events-none' : ''}>
                              <MultipleSelector
                                 value={selectedOptions}
                                 onChange={options => {
                                    field.onChange(options.map(opt => opt.value));
                                 }}
                                 defaultOptions={cityProductOptions}
                                 placeholder={
                                    !selectedCity
                                       ? 'Please select cities first...'
                                       : 'Select city-product combinations...'
                                 }
                                 emptyIndicator={
                                    <p className='text-center text-sm text-gray-500'>
                                       No combinations found
                                    </p>
                                 }
                                 showSelectAll={true}
                                 disabled={!selectedCity}
                              />
                           </div>
                        );
                     }}
                  />
                  {errors?.applicability?.cityProductIds && (
                     <ErrorMessage error={errors.applicability.cityProductIds} />
                  )}
               </div>
            </div>
         );

      case ApplicabilityType.USER:
         return (
            <div className='flex flex-col gap-2'>
               <Label>Select Specific Users *</Label>
               <Controller
                  control={control}
                  name='applicability.userIds'
                  render={({ field }) => {
                     // Sync selected users with field value
                     const fieldUserIds = field.value || [];

                     const handleUserSelect = (rider: Rider) => {
                        const isAlreadySelected = fieldUserIds.includes(rider.id);

                        if (isAlreadySelected) {
                           // Remove user
                           const newUserIds = fieldUserIds.filter((id: string) => id !== rider.id);
                           field.onChange(newUserIds);
                           setSelectedUsers(prev => prev.filter(u => u.id !== rider.id));
                        } else {
                           // Add user
                           const newUserIds = [...fieldUserIds, rider.id];
                           field.onChange(newUserIds);
                           setSelectedUsers(prev => [...prev, rider]);
                        }
                     };

                     const handleRemoveUser = (riderId: string) => {
                        const newUserIds = fieldUserIds.filter((id: string) => id !== riderId);
                        field.onChange(newUserIds);
                        setSelectedUsers(prev => prev.filter(u => u.id !== riderId));
                     };

                     return (
                        <div className='space-y-2'>
                           <Popover open={userComboboxOpen} onOpenChange={setUserComboboxOpen}>
                              <PopoverTrigger asChild>
                                 <Button
                                    ref={userTriggerRef}
                                    variant='outline'
                                    role='combobox'
                                    aria-expanded={userComboboxOpen}
                                    className='w-full justify-between'
                                 >
                                    <span className='text-muted-foreground'>Search users...</span>
                                    <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                                 </Button>
                              </PopoverTrigger>
                              <PopoverContent
                                 className='p-0'
                                 align='start'
                                 style={{ width: userTriggerRef.current?.offsetWidth }}
                              >
                                 <Command shouldFilter={false}>
                                    <CommandInput
                                       placeholder='Search by name, email, or phone...'
                                       value={userSearchQuery}
                                       onValueChange={setUserSearchQuery}
                                    />
                                    <CommandList>
                                       <CommandEmpty>
                                          {ridersQuery.isLoading ? 'Loading...' : 'No users found.'}
                                       </CommandEmpty>
                                       <CommandGroup>
                                          {ridersQuery.data?.data &&
                                             ridersQuery.data.data.slice(0, 10).map(rider => (
                                                <CommandItem
                                                   className='w-full'
                                                   key={rider.id}
                                                   value={rider.id}
                                                   onSelect={() => {
                                                      handleUserSelect(rider);
                                                   }}
                                                >
                                                   <Check
                                                      className={cn(
                                                         'mr-2 h-4 w-4',
                                                         fieldUserIds.includes(rider.id)
                                                            ? 'opacity-100'
                                                            : 'opacity-0'
                                                      )}
                                                   />
                                                   <div className='flex flex-col'>
                                                      <span>{`${rider.firstName} ${rider.lastName}`}</span>
                                                      <span className='text-xs text-gray-500'>
                                                         {rider.user.phoneNumber}
                                                      </span>
                                                   </div>
                                                </CommandItem>
                                             ))}
                                       </CommandGroup>
                                    </CommandList>
                                 </Command>
                              </PopoverContent>
                           </Popover>

                           {/* Selected Users Display */}
                           {selectedUsers.length > 0 && (
                              <div className='border rounded-md p-3 space-y-2'>
                                 <div className='text-sm font-medium text-gray-700'>
                                    Selected Users ({selectedUsers.length})
                                 </div>
                                 <div className='flex flex-wrap gap-2'>
                                    {selectedUsers.map(rider => (
                                       <div
                                          key={rider.id}
                                          className='flex items-center gap-2 bg-gray-100 px-3 py-1.5 rounded-md text-sm'
                                       >
                                          <span>{`${rider.firstName} ${rider.lastName}`}</span>
                                          <button
                                             type='button'
                                             onClick={() => handleRemoveUser(rider.id)}
                                             className='text-gray-500 hover:text-gray-700'
                                          >
                                             <X className='h-3 w-3' />
                                          </button>
                                       </div>
                                    ))}
                                 </div>
                              </div>
                           )}
                        </div>
                     );
                  }}
               />
               {errors?.applicability?.userIds && (
                  <ErrorMessage error={errors.applicability.userIds} />
               )}
            </div>
         );

      default:
         return null;
   }
}

'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Spinner } from '@/components/ui/spinner';
import { AlertTriangle } from 'lucide-react';

interface CouponDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading: boolean;
  couponCode: string;
  couponName: string;
}

export function CouponDeleteModal({
  isOpen,
  onClose,
  onConfirm,
  isLoading,
  couponCode,
  couponName,
}: CouponDeleteModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-md'>
        <DialogHeader>
          <div className='flex items-center gap-2'>
            <div className='rounded-full bg-red-100 p-2'>
              <AlertTriangle className='h-5 w-5 text-red-600' />
            </div>
            <DialogTitle>Confirm Deletion</DialogTitle>
          </div>
          <DialogDescription className='pt-3'>
            Are you sure you want to delete the coupon <strong>"{couponCode}"</strong> ({couponName}
            )?
            <br />
            <br />
            This action cannot be undone. The coupon will be permanently removed from the system.
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className='gap-2 sm:gap-0'>
          <Button
            type='button'
            variant='outline'
            onClick={onClose}
            disabled={isLoading}
            className='flex-1'
          >
            Cancel
          </Button>
          <Button
            type='button'
            variant='destructive'
            onClick={onConfirm}
            disabled={isLoading}
            className='flex-1'
          >
            {isLoading ? (
              <>
                Deleting...
                <Spinner className='ml-2 h-4 w-4' />
              </>
            ) : (
              'Delete Coupon'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

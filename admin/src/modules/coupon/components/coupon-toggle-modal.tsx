'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Spinner } from '@/components/ui/spinner';
import { AlertCircle, CheckCircle } from 'lucide-react';

interface CouponToggleModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading: boolean;
  couponCode: string;
  couponName: string;
  isCurrentlyActive: boolean;
}

export function CouponToggleModal({
  isOpen,
  onClose,
  onConfirm,
  isLoading,
  couponCode,
  couponName,
  isCurrentlyActive,
}: CouponToggleModalProps) {
  const action = isCurrentlyActive ? 'deactivate' : 'activate';
  const actionCapitalized = isCurrentlyActive ? 'Deactivate' : 'Activate';

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-md'>
        <DialogHeader>
          <div className='flex items-center gap-2'>
            <div
              className={`rounded-full p-2 ${
                isCurrentlyActive ? 'bg-orange-100' : 'bg-green-100'
              }`}
            >
              {isCurrentlyActive ? (
                <AlertCircle className='h-5 w-5 text-orange-600' />
              ) : (
                <CheckCircle className='h-5 w-5 text-green-600' />
              )}
            </div>
            <DialogTitle>Confirm {actionCapitalized}</DialogTitle>
          </div>
          <DialogDescription className='pt-3'>
            Are you sure you want to {action} the coupon <strong>"{couponCode}"</strong> (
            {couponName})?
            <br />
            <br />
            {isCurrentlyActive ? (
              <>
                This will prevent users from using this coupon until it is reactivated. Existing
                ride bookings will not be affected.
              </>
            ) : (
              <>
                This will allow users to use this coupon for their ride bookings, subject to the
                coupon's terms and conditions.
              </>
            )}
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className='gap-3'>
          <Button
            type='button'
            variant='outline'
            onClick={onClose}
            disabled={isLoading}
            className='flex-1'
          >
            Cancel
          </Button>
          <Button
            type='button'
            variant={isCurrentlyActive ? 'default' : 'default'}
            onClick={onConfirm}
            disabled={isLoading}
            className={`flex-1 ${
              isCurrentlyActive
                ? 'bg-orange-600 hover:bg-orange-700'
                : 'bg-green-600 hover:bg-green-700'
            }`}
          >
            {isLoading ? (
              <>
                {actionCapitalized.slice(0, -1)}ing...
                <Spinner className='ml-2 h-4 w-4' />
              </>
            ) : (
              `${actionCapitalized} Coupon`
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

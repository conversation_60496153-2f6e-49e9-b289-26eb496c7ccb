'use client';

import { ErrorMessage } from '@/components/error-message';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Switch } from '@/components/ui/switch';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { toast } from '@/lib/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { CalendarIcon, Plus } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { useQueryClient } from '@tanstack/react-query';
import { DateRange } from 'react-day-picker';
import { format } from 'date-fns';
import * as z from 'zod';
import { useCreateCoupon, useUpdateCoupon } from '../api/mutations';
import { useGetCoupon } from '../api/queries';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { DiscountType, ApplicabilityType, CouponConditionBlock } from '../types/coupon';
import { ApplicabilitySelector } from './applicability-selector';
import { ConditionBuilder } from './condition-builder';
import { generateJsonLogicFromBlocks, parseJsonLogicToBlocks } from '../utils/condition-helpers';
import { cn } from '@/lib/utils';
import {
   formatCouponCode,
   validateDiscountValue,
   validateDateRange,
   handleDecimalInput,
} from '../utils/coupon-helpers';
import { handleIntegerInputChange, getNumberDisplayValue } from '@/lib/utils';
import { FileUploadSection } from '@/components/file-upload-section';
import { useFileUpload } from '@/hooks/use-file-upload';
import { useFileUpload as useFileUploadMutation } from '@/lib/file-upload-api';
import Editor, {
   BtnBold,
   BtnItalic,
   BtnUnderline,
   BtnStrikeThrough,
   BtnBulletList,
   BtnNumberedList,
   BtnLink,
   BtnStyles,
   BtnClearFormatting,
   BtnUndo,
   BtnRedo,
   Toolbar,
} from 'react-simple-wysiwyg';

// ============================================
// ZOD VALIDATION SCHEMA
// ============================================
const couponSchema = z
   .object({
      name: z
         .string()
         .min(1, 'Name is required')
         .min(2, 'Name must be at least 2 characters')
         .max(100, 'Name must not exceed 100 characters'),
      code: z
         .string()
         .min(1, 'Code is required')
         .min(3, 'Code must be at least 3 characters')
         .max(20, 'Code must not exceed 20 characters'),
      description: z.string().max(1000, 'Description must not exceed 1000 characters').optional(),
      thumbnail: z.string().optional().or(z.literal('')),
      startDate: z.string().min(1, 'Start date is required'),
      endDate: z.string().min(1, 'End date is required'),
      discountType: z.nativeEnum(DiscountType),
      discountValue: z.number().min(0.01, 'Discount value must be greater than 0'),
      maxDiscountLimit: z.number().min(0.01).optional(),
      minFareCondition: z.number().min(0.01).optional(),
      usageLimit: z.preprocess(
         val => (val === '' ? undefined : val),
         z
            .number({ message: 'Usage limit is required' })
            .int('Usage limit must be a whole number')
            .min(1, 'Usage limit must be at least 1')
      ),
      applicabilityType: z.nativeEnum(ApplicabilityType),
      isActive: z.boolean(),
      // Applicability config
      applicability: z.object({
         zoneIds: z.array(z.string().uuid()).optional(),
         productIds: z.array(z.string().uuid()).optional(),
         cityProductIds: z.array(z.string().uuid()).optional(),
         userIds: z.array(z.string().uuid()).optional(),
      }),
      // Advanced conditions
      enableConditions: z.boolean().optional(),
      conditionBlocks: z.array(z.any()).optional(),
   })
   .superRefine((data, ctx) => {
      // Validate end date > start date
      const dateValidation = validateDateRange(data.startDate, data.endDate);
      if (!dateValidation.valid) {
         ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: dateValidation.error || 'Invalid date range',
            path: ['endDate'],
         });
      }

      // Validate discount value based on type
      const discountValidation = validateDiscountValue(data.discountValue, data.discountType);
      if (!discountValidation.valid) {
         ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: discountValidation.error || 'Invalid discount value',
            path: ['discountValue'],
         });
      }

      // Validate max discount limit is required for percentage discount type
      if (data.discountType === DiscountType.PERCENTAGE) {
         if (!data.maxDiscountLimit || data.maxDiscountLimit <= 0) {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: 'Max discount limit is required for percentage discounts',
               path: ['maxDiscountLimit'],
            });
         }
      }

      // Validate applicability arrays match the type
      if (data.applicabilityType === ApplicabilityType.CITY) {
         if (!data.applicability.zoneIds || data.applicability.zoneIds.length === 0) {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: 'At least one zone is required for city-based coupons',
               path: ['applicability', 'zoneIds'],
            });
         }
      } else if (data.applicabilityType === ApplicabilityType.PRODUCT) {
         if (!data.applicability.productIds || data.applicability.productIds.length === 0) {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: 'At least one product is required',
               path: ['applicability', 'productIds'],
            });
         }
      } else if (data.applicabilityType === ApplicabilityType.CITY_PRODUCT) {
         if (!data.applicability.cityProductIds || data.applicability.cityProductIds.length === 0) {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: 'At least one city-product combination is required',
               path: ['applicability', 'cityProductIds'],
            });
         }
      } else if (data.applicabilityType === ApplicabilityType.USER) {
         if (!data.applicability.userIds || data.applicability.userIds.length === 0) {
            ctx.addIssue({
               code: z.ZodIssueCode.custom,
               message: 'At least one user is required',
               path: ['applicability', 'userIds'],
            });
         }
      }
   });

type CouponFormValues = z.infer<typeof couponSchema>;

// ============================================
// COMPONENT PROPS
// ============================================
interface CouponModalProps {
   couponId?: string | null;
   isOpen?: boolean;
   onClose?: () => void;
   mode?: 'create' | 'edit';
}

// ============================================
// MODAL COMPONENT
// ============================================
export const CouponModal = ({ couponId, isOpen, onClose, mode = 'create' }: CouponModalProps) => {
   const [internalOpen, setInternalOpen] = useState(false);
   const createMutation = useCreateCoupon();
   const updateMutation = useUpdateCoupon();
   const couponQuery = useGetCoupon(couponId || null);
   const queryClient = useQueryClient();
   const { withPermission } = useRoleBasedAccess();

   // Use external open state if provided, otherwise use internal state
   const modalOpen = isOpen !== undefined ? isOpen : internalOpen;
   const setModalOpen = onClose ? (open: boolean) => !open && onClose() : setInternalOpen;

   // File upload state
   const [fileError, setFileError] = useState<string>('');
   const [shouldRemoveThumbnail, setShouldRemoveThumbnail] = useState(false);

   const form = useForm<CouponFormValues>({
      resolver: zodResolver(couponSchema) as any,
      defaultValues: {
         name: '',
         code: '',
         description: '',
         thumbnail: '',
         startDate: '',
         endDate: '',
         discountType: DiscountType.PERCENTAGE,
         discountValue: 0,
         maxDiscountLimit: undefined,
         minFareCondition: undefined,
         usageLimit: 1,
         applicabilityType: ApplicabilityType.CITY,
         isActive: true,
         applicability: {
            zoneIds: [],
            productIds: [],
            cityProductIds: [],
            userIds: [],
         },
         enableConditions: false,
         conditionBlocks: [],
      },
   });

   const {
      formState: { errors },
      reset,
      control,
      handleSubmit,
      watch,
      setValue,
   } = form;

   const discountType = watch('discountType');
   const applicabilityType = watch('applicabilityType');
   const codeValue = watch('code');
   const startDate = watch('startDate');
   const endDate = watch('endDate');

   const isDataLoading = couponQuery.isLoading;

   // Create date range object from form values
   const dateRange: DateRange | undefined =
      startDate || endDate
         ? {
              from: startDate ? new Date(startDate) : undefined,
              to: endDate ? new Date(endDate) : undefined,
           }
         : undefined;

   // Handle date range change
   const handleDateRangeChange = (range: DateRange | undefined) => {
      setValue('startDate', range?.from ? format(range.from, 'yyyy-MM-dd') : '', {
         shouldValidate: true,
      });
      setValue('endDate', range?.to ? format(range.to, 'yyyy-MM-dd') : '', {
         shouldValidate: true,
      });
   };

   // File upload configuration
   const fileUploadMutation = useFileUploadMutation({ isPublic: true });
   const maxSize = 10 * 1024 * 1024; // 10MB
   const acceptedTypes = '.jpg,.jpeg,.png,.svg';

   const [
      { files, isDragging, errors: uploadErrors },
      {
         handleDragEnter,
         handleDragLeave,
         handleDragOver,
         handleDrop,
         openFileDialog,
         removeFile,
         getInputProps,
         clearFiles,
         clearErrors,
         addFiles,
         handleFileChange,
      },
   ] = useFileUpload({
      maxSize,
      accept: acceptedTypes,
      multiple: false,
   });

   const file = files[0];

   // Auto-uppercase code as user types
   useEffect(() => {
      if (codeValue) {
         const formatted = formatCouponCode(codeValue);
         if (formatted !== codeValue) {
            setValue('code', formatted);
         }
      }
   }, [codeValue, setValue]);

   // Reset form when couponId changes or modal opens
   useEffect(() => {
      if (mode === 'edit' && couponQuery.data?.data && modalOpen && !isDataLoading) {
         const coupon = couponQuery.data.data;

         // Parse condition blocks from JSON logic
         const conditionBlocks = coupon.applyConditionLogic
            ? parseJsonLogicToBlocks(coupon.applyConditionLogic)
            : [];

         // Parse applicability
         const applicability = {
            zoneIds: coupon.couponZones?.map(cz => cz.zoneId) || [],
            productIds: coupon.couponProducts?.map(cp => cp.productId) || [],
            cityProductIds: coupon.couponCityProducts?.map(ccp => ccp.cityProductId) || [],
            userIds: coupon.couponUsers?.map(cu => cu.userProfileId) || [],
         };

         reset({
            name: coupon.name,
            code: coupon.code,
            description: coupon.description || '',
            thumbnail: coupon.thumbnail || '',
            startDate: new Date(coupon.startDate).toISOString().split('T')[0],
            endDate: new Date(coupon.endDate).toISOString().split('T')[0],
            discountType: coupon.discountType,
            discountValue: parseFloat(coupon.discountValue),
            maxDiscountLimit: coupon.maxDiscountLimit
               ? parseFloat(coupon.maxDiscountLimit)
               : undefined,
            minFareCondition: coupon.minFareCondition
               ? parseFloat(coupon.minFareCondition)
               : undefined,
            usageLimit: coupon.usageLimit,
            applicabilityType: coupon.applicabilityType,
            isActive: coupon.isActive,
            applicability,
            enableConditions: conditionBlocks.length > 0,
            conditionBlocks,
         });
      } else if (mode === 'create') {
         reset({
            name: '',
            code: '',
            description: '',
            thumbnail: '',
            startDate: new Date().toISOString().split('T')[0],
            endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            discountType: DiscountType.PERCENTAGE,
            discountValue: 0,
            maxDiscountLimit: undefined,
            minFareCondition: undefined,
            usageLimit: 1,
            applicabilityType: ApplicabilityType.CITY,
            isActive: true,
            applicability: {
               zoneIds: [],
               productIds: [],
               cityProductIds: [],
               userIds: [],
            },
            enableConditions: false,
            conditionBlocks: [],
         });
      }
   }, [couponQuery.data, reset, mode, modalOpen, isDataLoading]);

   // Clear files and errors when modal opens
   useEffect(() => {
      if (modalOpen) {
         clearFiles();
         clearErrors();
         setFileError('');
         setShouldRemoveThumbnail(false);
      }
   }, [modalOpen, clearFiles, clearErrors]);

   // Clear file error when file is selected
   useEffect(() => {
      if (file) {
         setFileError('');
         setShouldRemoveThumbnail(false);
      }
   }, [file]);

   const onSubmit = async (data: CouponFormValues) => {
      setFileError('');

      try {
         let thumbnailUrl: string | undefined;

         // Upload new file if selected
         if (file) {
            const uploadResponse = await fileUploadMutation.mutateAsync(file.file as File);
            thumbnailUrl = uploadResponse.data.url;
         }
         // Remove thumbnail if marked
         else if (shouldRemoveThumbnail) {
            thumbnailUrl = undefined;
         }
         // Keep existing thumbnail in edit mode
         else if (mode === 'edit' && couponQuery.data?.data?.thumbnail) {
            thumbnailUrl = couponQuery.data.data.thumbnail;
         }

         // Generate JSON logic from condition blocks (only if enabled)
         const applyConditionLogic =
            data.enableConditions && data.conditionBlocks?.length
               ? generateJsonLogicFromBlocks(data.conditionBlocks as CouponConditionBlock[])
               : undefined;

         // Build applicability object based on type - only include relevant fields
         let applicability: any = {};
         switch (data.applicabilityType) {
            case ApplicabilityType.CITY:
               applicability = { zoneIds: data.applicability.zoneIds || [] };
               break;
            case ApplicabilityType.PRODUCT:
               applicability = { productIds: data.applicability.productIds || [] };
               break;
            case ApplicabilityType.CITY_PRODUCT:
               applicability = { cityProductIds: data.applicability.cityProductIds || [] };
               break;
            case ApplicabilityType.USER:
               applicability = { userIds: data.applicability.userIds || [] };
               break;
         }

         // Build payload
         const payload = {
            coupon: {
               name: data.name,
               code: data.code.toUpperCase(),
               description: data.description || undefined,
               thumbnail: thumbnailUrl,
               startDate: new Date(data.startDate).toISOString(),
               endDate: new Date(data.endDate).toISOString(),
               discountType: data.discountType,
               discountValue: data.discountValue,
               maxDiscountLimit: data.maxDiscountLimit,
               minFareCondition: data.minFareCondition,
               usageLimit: data.usageLimit,
               applicabilityType: data.applicabilityType,
               applyConditionLogic,
               isActive: data.isActive,
            },
            applicability,
         };

         if (mode === 'create') {
            createMutation.mutate(payload, {
               onSuccess: () => {
                  toast.success('Coupon created successfully');
                  handleClose();
                  queryClient.invalidateQueries({ queryKey: ['coupons'] });
               },
            });
         } else if (mode === 'edit' && couponId) {
            updateMutation.mutate(
               { id: couponId, ...payload },
               {
                  onSuccess: () => {
                     toast.success('Coupon updated successfully');
                     handleClose();
                     queryClient.invalidateQueries({ queryKey: ['coupons'] });
                     queryClient.invalidateQueries({ queryKey: ['coupon', couponId] });
                  },
               }
            );
         }
      } catch (error: any) {
         console.error('Submit error:', error);
         toast.error('An error occurred');
      }
   };

   const handleClose = () => {
      setModalOpen(false);
      reset();
      clearFiles();
      clearErrors();
      setFileError('');
      setShouldRemoveThumbnail(false);
   };

   const isLoading =
      mode === 'create'
         ? createMutation.isPending || fileUploadMutation.isPending
         : updateMutation.isPending || fileUploadMutation.isPending;

   const renderDialogContent = () => {
      // Show loading state for edit mode
      if (mode === 'edit' && couponQuery.isLoading) {
         return (
            <DialogContent className='max-w-xl min-w-xl'>
               <DialogHeader>
                  <DialogTitle>Loading...</DialogTitle>
                  <DialogDescription>Please wait while we load the coupon data.</DialogDescription>
               </DialogHeader>
               <div className='flex items-center justify-center py-8'>
                  <Spinner className='h-8 w-8' />
               </div>
            </DialogContent>
         );
      }

      // Show error state for edit mode
      if (mode === 'edit' && couponQuery.error) {
         return (
            <DialogContent className='max-w-xl min-w-xl'>
               <DialogHeader>
                  <DialogTitle>Error</DialogTitle>
                  <DialogDescription>Failed to load coupon data.</DialogDescription>
               </DialogHeader>
               <div className='text-center py-8'>
                  <p className='text-red-600'>Failed to load coupon</p>
                  <Button onClick={handleClose} className='mt-4'>
                     Close
                  </Button>
               </div>
            </DialogContent>
         );
      }

      // Show form content
      return (
         <DialogContent
            onInteractOutside={e => {
               e.preventDefault();
            }}
            className='max-w-xl min-w-xl max-h-[90vh] overflow-y-auto'
         >
            <DialogHeader>
               <DialogTitle>{mode === 'create' ? 'Create New Coupon' : 'Edit Coupon'}</DialogTitle>
               <DialogDescription>
                  {mode === 'create'
                     ? 'Create a new coupon with specific rules and applicability'
                     : 'Update coupon information and settings'}
               </DialogDescription>
            </DialogHeader>

            <form onSubmit={handleSubmit(onSubmit)} className='space-y-6 py-4'>
               {/* Basic Details Section */}
               <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>Basic Details</h3>

                  <div className='grid grid-cols-2 gap-4'>
                     <div className='flex flex-col gap-2'>
                        <Label htmlFor='name'>Coupon Name *</Label>
                        <Controller
                           control={control}
                           name='name'
                           render={({ field }) => (
                              <Input id='name' placeholder='e.g., Weekend Special' {...field} />
                           )}
                        />
                        {errors.name && <ErrorMessage error={errors.name} />}
                     </div>

                     <div className='flex flex-col gap-2'>
                        <Label htmlFor='code'>Coupon Code *</Label>
                        <Controller
                           control={control}
                           name='code'
                           render={({ field }) => (
                              <Input
                                 id='code'
                                 placeholder='e.g., WEEKEND50'
                                 {...field}
                                 className='uppercase'
                              />
                           )}
                        />
                        {errors.code && <ErrorMessage error={errors.code} />}
                     </div>
                  </div>

                  <div className='flex flex-col gap-2'>
                     <Label htmlFor='description'>Description</Label>
                     <Controller
                        control={control}
                        name='description'
                        render={({ field }) => (
                           <Editor
                              value={field.value || ''}
                              onChange={field.onChange}
                              containerProps={{
                                 style: { minHeight: '150px' },
                              }}
                           >
                              <Toolbar>
                                 <BtnUndo />
                                 <BtnRedo />
                                 <BtnBold />
                                 <BtnItalic />
                                 <BtnUnderline />
                                 <BtnStrikeThrough />
                                 <BtnBulletList />
                                 <BtnNumberedList />
                                 <BtnLink />
                                 <BtnStyles />
                                 <BtnClearFormatting />
                              </Toolbar>
                           </Editor>
                        )}
                     />
                     {errors.description && <ErrorMessage error={errors.description} />}
                  </div>

                  <div className='flex flex-col gap-2'>
                     <FileUploadSection
                        label='Coupon Thumbnail'
                        fieldName='thumbnail'
                        maxSize={maxSize}
                        existingFileUrl={
                           mode === 'edit' ? couponQuery.data?.data?.thumbnail : undefined
                        }
                        fileError={fileError}
                        fileUploadState={{ files, isDragging, errors: uploadErrors }}
                        fileUploadActions={{
                           handleDragEnter,
                           handleDragLeave,
                           handleDragOver,
                           handleDrop,
                           openFileDialog,
                           removeFile,
                           getInputProps,
                           addFiles,
                           clearFiles,
                           clearErrors,
                           handleFileChange,
                        }}
                        onRemoveExisting={() => setShouldRemoveThumbnail(true)}
                        isExistingFileMarkedForRemoval={shouldRemoveThumbnail}
                        showRemovalIndicator={true}
                     />
                  </div>
               </div>

               {/* Discount Configuration Section */}
               <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>Discount Configuration</h3>

                  <div className='flex flex-col gap-2'>
                     <Label>Discount Type *</Label>
                     <Controller
                        control={control}
                        name='discountType'
                        render={({ field }) => (
                           <RadioGroup
                              value={field.value}
                              onValueChange={field.onChange}
                              className='flex gap-4'
                           >
                              <div className='flex items-center gap-2'>
                                 <RadioGroupItem value={DiscountType.PERCENTAGE} id='percentage' />
                                 <Label htmlFor='percentage' className='cursor-pointer'>
                                    Percentage (%)
                                 </Label>
                              </div>
                              <div className='flex items-center gap-2'>
                                 <RadioGroupItem value={DiscountType.FLAT} id='flat' />
                                 <Label htmlFor='flat' className='cursor-pointer'>
                                    Flat Amount (₹)
                                 </Label>
                              </div>
                           </RadioGroup>
                        )}
                     />
                  </div>

                  <div className='grid grid-cols-3 gap-4'>
                     <div className='flex flex-col gap-2'>
                        <Label htmlFor='discountValue'>
                           Discount Value *{' '}
                           {discountType === DiscountType.PERCENTAGE ? '(%)' : '(₹)'}
                        </Label>
                        <Controller
                           control={control}
                           name='discountValue'
                           render={({ field }) => (
                              <Input
                                 id='discountValue'
                                 type='number'
                                 step='0.01'
                                 min='0'
                                 max={discountType === DiscountType.PERCENTAGE ? '100' : undefined}
                                 placeholder='0'
                                 value={field.value || ''}
                                 onChange={e => {
                                    const val = handleDecimalInput(e.target.value);
                                    field.onChange(val);
                                 }}
                              />
                           )}
                        />
                        {errors.discountValue && <ErrorMessage error={errors.discountValue} />}
                     </div>

                     {discountType === DiscountType.PERCENTAGE && (
                        <div className='flex flex-col gap-2'>
                           <Label htmlFor='maxDiscountLimit'>Max Discount Limit (₹) *</Label>
                           <Controller
                              control={control}
                              name='maxDiscountLimit'
                              render={({ field }) => (
                                 <Input
                                    id='maxDiscountLimit'
                                    type='number'
                                    step='0.01'
                                    min='0'
                                    placeholder='0'
                                    value={field.value || ''}
                                    onChange={e => {
                                       const val = handleDecimalInput(e.target.value);
                                       field.onChange(val);
                                    }}
                                 />
                              )}
                           />
                           {errors.maxDiscountLimit && (
                              <ErrorMessage error={errors.maxDiscountLimit} />
                           )}
                        </div>
                     )}

                     <div className='flex flex-col gap-2'>
                        <Label htmlFor='minFareCondition'>Min Fare Condition (₹)</Label>
                        <Controller
                           control={control}
                           name='minFareCondition'
                           render={({ field }) => (
                              <Input
                                 id='minFareCondition'
                                 type='number'
                                 step='0.01'
                                 min='0'
                                 placeholder='0'
                                 value={field.value || ''}
                                 onChange={e => {
                                    const val = handleDecimalInput(e.target.value);
                                    field.onChange(val);
                                 }}
                              />
                           )}
                        />
                        {errors.minFareCondition && (
                           <ErrorMessage error={errors.minFareCondition} />
                        )}
                     </div>
                  </div>
               </div>

               {/* Applicability Section */}
               <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>Applicability Rules</h3>

                  <div className='flex flex-col gap-2'>
                     <Label>Applicability Type *</Label>
                     <Controller
                        control={control}
                        name='applicabilityType'
                        render={({ field }) => (
                           <RadioGroup
                              value={field.value}
                              onValueChange={field.onChange}
                              className='grid grid-cols-2 gap-2'
                           >
                              <div className='flex items-center gap-2'>
                                 <RadioGroupItem value={ApplicabilityType.CITY} id='city' />
                                 <Label htmlFor='city' className='cursor-pointer'>
                                    City Zones
                                 </Label>
                              </div>
                              <div className='flex items-center gap-2'>
                                 <RadioGroupItem value={ApplicabilityType.PRODUCT} id='product' />
                                 <Label htmlFor='product' className='cursor-pointer'>
                                    Products
                                 </Label>
                              </div>
                              <div className='flex items-center gap-2'>
                                 <RadioGroupItem
                                    value={ApplicabilityType.CITY_PRODUCT}
                                    id='city-product'
                                 />
                                 <Label htmlFor='city-product' className='cursor-pointer'>
                                    City-Product
                                 </Label>
                              </div>
                              <div className='flex items-center gap-2'>
                                 <RadioGroupItem value={ApplicabilityType.USER} id='user' />
                                 <Label htmlFor='user' className='cursor-pointer'>
                                    Specific Users
                                 </Label>
                              </div>
                           </RadioGroup>
                        )}
                     />
                  </div>

                  <ApplicabilitySelector
                     control={control}
                     applicabilityType={applicabilityType}
                     errors={errors}
                  />
               </div>

               {/* Validity Period & Settings Section */}
               <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>Validity Period & Settings</h3>

                  <div className='flex flex-col gap-2'>
                     <Label>Validity Period *</Label>
                     <div className='relative'>
                        <Popover>
                           <PopoverTrigger asChild>
                              <Button
                                 variant='outline'
                                 className={cn(
                                    'w-full justify-start text-left font-normal pr-10',
                                    !dateRange && 'text-muted-foreground'
                                 )}
                              >
                                 <CalendarIcon className='mr-2 h-4 w-4' />
                                 {dateRange?.from ? (
                                    dateRange.to ? (
                                       <>
                                          {format(dateRange.from, 'MMM dd, yyyy')} -{' '}
                                          {format(dateRange.to, 'MMM dd, yyyy')}
                                       </>
                                    ) : (
                                       format(dateRange.from, 'MMM dd, yyyy')
                                    )
                                 ) : (
                                    'Pick a date range'
                                 )}
                              </Button>
                           </PopoverTrigger>
                           <PopoverContent className='w-auto p-2' align='start'>
                              <Calendar
                                 mode='range'
                                 selected={dateRange}
                                 onSelect={handleDateRangeChange}
                              />
                           </PopoverContent>
                        </Popover>
                        {dateRange && (
                           <button
                              type='button'
                              onClick={() => handleDateRangeChange(undefined)}
                              className='absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 flex items-center justify-center rounded-md hover:bg-gray-100 transition-colors'
                              aria-label='Clear date range'
                           >
                              <svg
                                 xmlns='http://www.w3.org/2000/svg'
                                 width='14'
                                 height='14'
                                 viewBox='0 0 24 24'
                                 fill='none'
                                 stroke='currentColor'
                                 strokeWidth='2'
                                 strokeLinecap='round'
                                 strokeLinejoin='round'
                                 className='text-gray-500'
                              >
                                 <line x1='18' y1='6' x2='6' y2='18'></line>
                                 <line x1='6' y1='6' x2='18' y2='18'></line>
                              </svg>
                           </button>
                        )}
                     </div>
                     {(errors.startDate || errors.endDate) && (
                        <ErrorMessage error={errors.startDate || errors.endDate} />
                     )}
                  </div>

                  <div className='grid grid-cols-2 gap-4'>
                     <div className='flex flex-col gap-2'>
                        <Label htmlFor='usageLimit'>Usage Limit Per User *</Label>
                        <Controller
                           control={control}
                           name='usageLimit'
                           render={({ field }) => (
                              <Input
                                 id='usageLimit'
                                 type='number'
                                 min='1'
                                 step='1'
                                 placeholder='0'
                                 value={getNumberDisplayValue(field.value)}
                                 onChange={e =>
                                    handleIntegerInputChange(e.target.value, field.onChange)
                                 }
                              />
                           )}
                        />
                        {errors.usageLimit && <ErrorMessage error={errors.usageLimit} />}
                     </div>

                     <div className='flex flex-col gap-2'>
                        <Label htmlFor='isActive'>Active Status</Label>
                        <Controller
                           control={control}
                           name='isActive'
                           render={({ field }) => (
                              <div className='flex items-center gap-2 h-10'>
                                 <Switch
                                    id='isActive'
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                 />
                                 <Label htmlFor='isActive' className='cursor-pointer'>
                                    {field.value ? 'Active' : 'Inactive'}
                                 </Label>
                              </div>
                           )}
                        />
                     </div>
                  </div>
               </div>

               {/* Advanced Conditions Section */}
               <div className='space-y-4'>
                  <ConditionBuilder control={control} />
               </div>

               {/* Action Buttons */}
               <div className='flex gap-3 pt-4 border-t'>
                  <Button type='button' variant='outline' onClick={handleClose} className='flex-1'>
                     Cancel
                  </Button>
                  <Button type='submit' disabled={isLoading} className='flex-1'>
                     {isLoading ? (
                        <>
                           {mode === 'create' ? 'Creating...' : 'Updating...'}
                           <Spinner className='ml-2 h-4 w-4' />
                        </>
                     ) : mode === 'create' ? (
                        'Create Coupon'
                     ) : (
                        'Update Coupon'
                     )}
                  </Button>
               </div>
            </form>
         </DialogContent>
      );
   };

   // For create mode, return button and dialog separately
   if (mode === 'create' && isOpen === undefined) {
      return (
         <>
            <Button
               className='cursor-pointer'
               variant='outline'
               onClick={() =>
                  withPermission(RBAC_PERMISSIONS.COUPON.CREATE, () => setModalOpen(true))
               }
            >
               <Plus />
               Add Coupon
            </Button>
            <Dialog open={modalOpen} onOpenChange={setModalOpen}>
               {renderDialogContent()}
            </Dialog>
         </>
      );
   }

   // For edit mode or controlled create mode
   return (
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
         {renderDialogContent()}
      </Dialog>
   );
};

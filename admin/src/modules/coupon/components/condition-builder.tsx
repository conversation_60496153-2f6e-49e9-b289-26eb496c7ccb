'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Plus, Trash2 } from 'lucide-react';
import { Control, Controller, useFieldArray } from 'react-hook-form';
import {
  CouponConditionBlock,
  CouponConditionType,
  RideConditionConfig,
  RIDE_OPERATOR_LABELS,
} from '../types/coupon';
import { getNumberDisplayValue } from '@/lib/utils';

interface ConditionBuilderProps {
  control: Control<any>;
}

/**
 * Handle integer input change (no decimals allowed)
 * Allows empty string and integers >= 0
 * Validation for min value (>= 1) should happen at form submission
 */
const handleIntegerInputChange = (value: string, onChange: (val: any) => void) => {
  // Allow empty string (user can clear the input)
  if (value === '') {
    onChange('');
    return;
  }

  // Remove any non-digit characters (no decimals, no negative signs)
  const cleanedValue = value.replace(/[^\d]/g, '');

  if (cleanedValue === '') {
    onChange('');
    return;
  }

  const num = parseInt(cleanedValue, 10);

  // Store the integer value (allows 0, 1, 2, 3, ...)
  onChange(isNaN(num) ? '' : num);
};

export function ConditionBuilder({ control }: ConditionBuilderProps) {
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'conditionBlocks',
  });

  const addConditionBlock = () => {
    append({
      type: CouponConditionType.RIDES,
      config: [{ operator: '=', value: 0 }],
      operator: 'and',
    } as CouponConditionBlock);
  };

  return (
    <div className='border-t pt-4 mt-4'>
      {/* Toggle Section */}
      <div className='flex items-center justify-between mb-4'>
        <div className='flex flex-col gap-1'>
          <Label htmlFor='enable-conditions' className='text-base font-medium'>
            Enable based on condition
          </Label>
          <p className='text-xs text-muted-foreground'>
            Whether this coupon to be applied to all rides or based on a specific condition such as
            number of rides
          </p>
        </div>

        <Controller
          control={control}
          name='enableConditions'
          defaultValue={false}
          render={({ field }) => (
            <Switch
              id='enable-conditions'
              checked={field.value}
              onCheckedChange={(checked) => {
                field.onChange(checked);
                // Auto-add first condition when enabled and no conditions exist
                if (checked && fields.length === 0) {
                  addConditionBlock();
                }
              }}
            />
          )}
        />
      </div>

      {/* Condition Builder - Only shown when enabled */}
      <Controller
        control={control}
        name='enableConditions'
        render={({ field }) => {
          if (!field.value) {
            return <></>;
          }

          return (
            <div className='space-y-4'>
              <p className='text-sm text-muted-foreground'>
                Define one or more conditions. Each condition can be combined with AND/OR operators.
              </p>

              {/* Condition Blocks */}
              <div className='space-y-4'>
                {fields.map((field, index) => (
                  <div key={field.id} className='border rounded-md p-4 bg-muted/30 space-y-4'>
                    <div className='flex items-center justify-between'>
                      <h4 className='font-medium'>Condition {index + 1}</h4>
                      {fields.length > 1 && (
                        <Button
                          type='button'
                          variant='ghost'
                          size='sm'
                          onClick={() => remove(index)}
                          className='text-red-600 hover:text-red-700'
                        >
                          <Trash2 className='h-4 w-4' />
                        </Button>
                      )}
                    </div>

                  <Controller
                    control={control}
                    name={`conditionBlocks.${index}`}
                    render={({ field: blockField }) => {
                      const block = blockField.value as CouponConditionBlock;

                      return (
                        <div className='space-y-4'>
                          <div className='w-full'>
                            <Label className='text-sm mb-2 block'>Condition Type *</Label>
                            <Select
                              value={block.type}
                              onValueChange={(value) => {
                                const newType = value as CouponConditionType;
                                // Only RIDES type is supported
                                const newConfig: RideConditionConfig[] = [
                                  { operator: '=', value: 0 },
                                ];
                                blockField.onChange({ ...block, type: newType, config: newConfig });
                              }}
                            >
                              <SelectTrigger className='w-full'>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value={CouponConditionType.RIDES}>
                                  Number of Rides
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          {/* Render ride condition config */}
                          {block.type === CouponConditionType.RIDES && (
                            <RideConfigRenderer
                              configs={block.config as RideConditionConfig[]}
                              onChange={(newConfigs) => {
                                blockField.onChange({ ...block, config: newConfigs });
                              }}
                            />
                          )}

                          {/* Combine with next condition - only show if not last */}
                          {index < fields.length - 1 && (
                            <div className='pt-4 border-t'>
                              <Label className='text-sm mb-2 block'>
                                Combine with next condition using:
                              </Label>
                              <div className='flex gap-2'>
                                <Button
                                  type='button'
                                  variant={block.operator === 'and' ? 'default' : 'outline'}
                                  size='sm'
                                  onClick={() =>
                                    blockField.onChange({ ...block, operator: 'and' })
                                  }
                                  className='flex-1'
                                >
                                  AND
                                </Button>
                                <Button
                                  type='button'
                                  variant={block.operator === 'or' ? 'default' : 'outline'}
                                  size='sm'
                                  onClick={() => blockField.onChange({ ...block, operator: 'or' })}
                                  className='flex-1'
                                >
                                  OR
                                </Button>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    }}
                  />
                </div>
              ))}
              </div>

              {/* Add Condition Button */}
              <Button
                type='button'
                variant='outline'
                size='sm'
                onClick={addConditionBlock}
                className='w-full'
              >
                <Plus className='h-4 w-4 mr-1' />
                Add Condition
              </Button>
            </div>
          );
        }}
      />
    </div>
  );
}


// ============================================
// RIDE CONFIG RENDERER
// ============================================
/**
 * Renders a single operator-based ride condition
 * Examples: rides = 1, rides < 10, rides > 5
 */
function RideConfigRenderer({
  configs,
  onChange,
}: {
  configs: RideConditionConfig[];
  onChange: (configs: RideConditionConfig[]) => void;
}) {
  // Always work with the first config (single condition per block)
  const config = configs[0] || { operator: '=', value: 0 };

  const updateConfig = (updates: Partial<RideConditionConfig>) => {
    onChange([{ ...config, ...updates }]);
  };

  return (
    <div className='grid grid-cols-2 gap-4'>
      <div>
        <Label className='text-sm mb-2 block'>Operator</Label>
        <Select
          value={config.operator}
          onValueChange={(value) =>
            updateConfig({
              operator: value as '=' | '!=' | '<' | '<=' | '>' | '>=',
            })
          }
        >
          <SelectTrigger className='w-full'>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(RIDE_OPERATOR_LABELS).map(([op, label]) => (
              <SelectItem key={op} value={op}>
                {label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label className='text-sm mb-2 block'>Number of Rides</Label>
        <Input
          type='number'
          min='0'
          step='1'
          value={getNumberDisplayValue(config.value)}
          onChange={(e) => {
            handleIntegerInputChange(e.target.value, (val) => {
              updateConfig({ value: val });
            });
          }}
          placeholder='Enter number'
        />
      </div>
    </div>
  );
}

import { Skeleton } from '@/components/ui/skeleton';

export function CouponTableLoading() {
  return (
    <div className='space-y-4'>
      <div className='rounded-md border'>
        <div className='overflow-x-auto'>
          <table className='w-full'>
            <thead>
              <tr className='border-b bg-gray-50'>
                <th className='h-11 px-4 text-left'>
                  <Skeleton className='h-4 w-20' />
                </th>
                <th className='h-11 px-4 text-left'>
                  <Skeleton className='h-4 w-24' />
                </th>
                <th className='h-11 px-4 text-center'>
                  <Skeleton className='h-4 w-28 mx-auto' />
                </th>
                <th className='h-11 px-4 text-center'>
                  <Skeleton className='h-4 w-32 mx-auto' />
                </th>
                <th className='h-11 px-4 text-left'>
                  <Skeleton className='h-4 w-36' />
                </th>
                <th className='h-11 px-4 text-center'>
                  <Skeleton className='h-4 w-20 mx-auto' />
                </th>
                <th className='h-11 px-4 text-center'>
                  <Skeleton className='h-4 w-16 mx-auto' />
                </th>
                <th className='h-11 px-4 text-left'>
                  <Skeleton className='h-4 w-28' />
                </th>
                <th className='h-11 px-4 text-center'>
                  <Skeleton className='h-4 w-20 mx-auto' />
                </th>
              </tr>
            </thead>
            <tbody>
              {[...Array(5)].map((_, index) => (
                <tr key={index} className='border-b'>
                  <td className='px-4 py-3'>
                    <Skeleton className='h-4 w-24' />
                  </td>
                  <td className='px-4 py-3'>
                    <Skeleton className='h-4 w-32' />
                  </td>
                  <td className='px-4 py-3 text-center'>
                    <Skeleton className='h-6 w-24 mx-auto rounded-full' />
                  </td>
                  <td className='px-4 py-3 text-center'>
                    <Skeleton className='h-6 w-16 mx-auto' />
                  </td>
                  <td className='px-4 py-3'>
                    <Skeleton className='h-4 w-40' />
                  </td>
                  <td className='px-4 py-3'>
                    <div className='flex flex-col gap-1'>
                      <Skeleton className='h-3 w-full' />
                      <Skeleton className='h-2 w-12' />
                    </div>
                  </td>
                  <td className='px-4 py-3 text-center'>
                    <Skeleton className='h-6 w-16 mx-auto rounded-full' />
                  </td>
                  <td className='px-4 py-3'>
                    <Skeleton className='h-4 w-28' />
                  </td>
                  <td className='px-4 py-3'>
                    <div className='flex justify-center gap-1'>
                      <Skeleton className='h-8 w-12' />
                      <Skeleton className='h-8 w-16' />
                      <Skeleton className='h-8 w-14' />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

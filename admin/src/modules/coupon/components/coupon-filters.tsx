'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Spinner } from '@/components/ui/spinner';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Search, X } from 'lucide-react';
import { useEffect, useState, useRef } from 'react';
import { ApplicabilityType, DiscountType } from '../types/coupon';
import { Label } from '@/components/ui/label';

export interface CouponFiltersProps {
  onSearchChange: (search: string) => void;
  onApplicabilityTypeChange: (type: ApplicabilityType | undefined) => void;
  onDiscountTypeChange: (type: DiscountType | undefined) => void;
  onActiveChange: (isActive: boolean | undefined) => void;
  onExpiredChange: (isExpired: boolean | undefined) => void;
  search: string;
  applicabilityType?: ApplicabilityType;
  discountType?: DiscountType;
  isActive?: boolean;
  isExpired?: boolean;
  isLoading?: boolean;
}

export function CouponFilters({
  onSearchChange,
  onApplicabilityTypeChange,
  onDiscountTypeChange,
  onActiveChange,
  onExpiredChange,
  search,
  applicabilityType,
  discountType,
  isActive,
  isExpired,
  isLoading,
}: CouponFiltersProps) {
  const [searchValue, setSearchValue] = useState(search || '');
  const [isSearching, setIsSearching] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Update local search state when props change
  useEffect(() => {
    setSearchValue(search || '');
  }, [search]);

  // Clean up timeouts on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Handle search input with debounce
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);

    // Show searching indicator
    setIsSearching(true);

    // Clear any existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set a new timeout
    searchTimeoutRef.current = setTimeout(() => {
      onSearchChange(value);
      searchTimeoutRef.current = null;
      setIsSearching(false);
    }, 500); // 500ms debounce time
  };

  // Clear all filters
  const handleClearFilters = () => {
    // Clear any pending timeouts
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
      searchTimeoutRef.current = null;
    }

    setIsSearching(false);
    setSearchValue('');
    onSearchChange('');
    onApplicabilityTypeChange(undefined);
    onDiscountTypeChange(undefined);
    onActiveChange(undefined);
    onExpiredChange(undefined);
  };

  // Check if any filters are active
  const hasActiveFilters =
    !!search ||
    applicabilityType !== undefined ||
    discountType !== undefined ||
    isActive !== undefined ||
    isExpired !== undefined;

  return (
     <div className='flex items-end gap-4 mb-4'>
        {/* Search field */}
        <div className='w-full'>
           <Label htmlFor='search' className='text-sm mb-2 block'>
              Search
           </Label>
           <div className='relative'>
              <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-gray-500' />
              <Input
                 id='search'
                 placeholder='Search by code, name, or description...'
                 value={searchValue}
                 onChange={handleSearchChange}
                 className='pl-8'
              />
              {(isSearching || (isLoading && searchValue)) && (
                 <div className='absolute right-2.5 top-2.5 text-gray-500'>
                    <Spinner className='h-4 w-4 text-primary' />
                 </div>
              )}
              {searchValue && !isSearching && !isLoading && (
                 <button
                    onClick={() => {
                       if (searchTimeoutRef.current) {
                          clearTimeout(searchTimeoutRef.current);
                          searchTimeoutRef.current = null;
                       }
                       setIsSearching(false);
                       setSearchValue('');
                       onSearchChange('');
                    }}
                    className='absolute right-2.5 top-2.5 text-gray-500 hover:text-gray-700'
                 >
                    <X className='h-4 w-4' />
                 </button>
              )}
           </div>
        </div>

        {/* Applicability Type filter */}
        <div className='w-full'>
           <Label htmlFor='applicability-type' className='text-sm mb-2 block'>
              Applicability
           </Label>
           <Select
              value={applicabilityType || 'all'}
              onValueChange={value =>
                 onApplicabilityTypeChange(
                    value === 'all' ? undefined : (value as ApplicabilityType)
                 )
              }
           >
              <SelectTrigger className='w-full' id='applicability-type'>
                 <SelectValue placeholder='All types' />
              </SelectTrigger>
              <SelectContent>
                 <SelectItem value='all'>All types</SelectItem>
                 <SelectItem value={ApplicabilityType.CITY}>City Zones</SelectItem>
                 <SelectItem value={ApplicabilityType.PRODUCT}>Products</SelectItem>
                 <SelectItem value={ApplicabilityType.CITY_PRODUCT}>City-Product</SelectItem>
                 <SelectItem value={ApplicabilityType.USER}>Specific Users</SelectItem>
              </SelectContent>
           </Select>
        </div>

        {/* Discount Type filter */}
        <div className='w-full'>
           <Label htmlFor='discount-type' className='text-sm mb-2 block'>
              Discount Type
           </Label>
           <Select
              value={discountType || 'all'}
              onValueChange={value =>
                 onDiscountTypeChange(value === 'all' ? undefined : (value as DiscountType))
              }
           >
              <SelectTrigger className='w-full' id='discount-type'>
                 <SelectValue placeholder='All types' />
              </SelectTrigger>
              <SelectContent>
                 <SelectItem value='all'>All types</SelectItem>
                 <SelectItem value={DiscountType.PERCENTAGE}>Percentage</SelectItem>
                 <SelectItem value={DiscountType.FLAT}>Flat Amount</SelectItem>
              </SelectContent>
           </Select>
        </div>

        {/* Status filter */}
        <div className='w-full'>
           <Label htmlFor='status' className='text-sm mb-2 block'>
              Status
           </Label>
           <Select
              value={isActive === undefined ? 'all' : isActive === true ? 'active' : 'inactive'}
              onValueChange={value => {
                 if (value === 'all') onActiveChange(undefined);
                 else if (value === 'active') onActiveChange(true);
                 else onActiveChange(false);
              }}
           >
              <SelectTrigger className='w-full' id='status'>
                 <SelectValue placeholder='All statuses' />
              </SelectTrigger>
              <SelectContent>
                 <SelectItem value='all'>All statuses</SelectItem>
                 <SelectItem value='active'>Active</SelectItem>
                 <SelectItem value='inactive'>Inactive</SelectItem>
              </SelectContent>
           </Select>
        </div>

        {/* Expiration filter */}
        <div className='w-full'>
           <Label htmlFor='expired' className='text-sm mb-2 block'>
              Expiration
           </Label>
           <Select
              value={isExpired === undefined ? 'all' : isExpired === true ? 'expired' : 'valid'}
              onValueChange={value => {
                 if (value === 'all') onExpiredChange(undefined);
                 else if (value === 'expired') onExpiredChange(true);
                 else onExpiredChange(false);
              }}
           >
              <SelectTrigger className='w-full' id='expired'>
                 <SelectValue placeholder='All coupons' />
              </SelectTrigger>
              <SelectContent>
                 <SelectItem value='all'>All coupons</SelectItem>
                 <SelectItem value='valid'>Valid only</SelectItem>
                 <SelectItem value='expired'>Expired only</SelectItem>
              </SelectContent>
           </Select>
        </div>

        {/* Clear Filters Button */}
        {hasActiveFilters && (
           <div className='pt-7'>
              <Button
                 variant='outline'
                 size='sm'
                 onClick={handleClearFilters}
                 className='text-gray-700 border-gray-300'
              >
                 <X className='h-4 w-4 mr-1' />
                 Clear Filters
              </Button>
           </div>
        )}
     </div>
  );
}

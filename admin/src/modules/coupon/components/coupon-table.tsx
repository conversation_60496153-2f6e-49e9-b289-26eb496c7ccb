'use client';

import { CustomPagination } from '@/components/pagination';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { useState } from 'react';
import { useDeleteCoupon, useToggleCouponStatus } from '../api/mutations';
import { Coupon, ListCouponResponse } from '../types/coupon';
import { CouponDeleteModal } from './coupon-delete-modal';
import { CouponToggleModal } from './coupon-toggle-modal';
import { CouponTableEmpty } from './coupon-table-empty';
import { CouponTableLoading } from './coupon-table-loading';
import {
   USE_ROLE_BASED_ACCESS,
   useRoleBasedAccess,
} from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { formatDiscountDisplay, isCouponExpired } from '../utils/coupon-helpers';

// ============================================
// COLUMN DEFINITIONS
// ============================================
const getColumns = ({
   handleEditClick,
   handleDeleteClick,
   handleToggleStatus,
   itemToDelete,
   deleteMutation,
   toggleStatusMutation,
   withPermission,
}: {
   handleEditClick: (id: string) => void;
   handleDeleteClick: (item: Coupon) => void;
   handleToggleStatus: (id: string) => void;
   itemToDelete: Coupon | null;
   deleteMutation: any;
   toggleStatusMutation: any;
   withPermission: USE_ROLE_BASED_ACCESS['withPermission'];
}): ColumnDef<Coupon>[] => [
   {
      accessorKey: 'code',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Code</div>,
      cell: ({ row }) => {
         const coupon = row.original;
         const isExpired = isCouponExpired(coupon.endDate);
         return (
            <div className='text-left'>
               <div className='text-sm font-bold break-words'>{coupon.code}</div>
               {isExpired && <span className='text-xs text-red-600 font-medium'>Expired</span>}
            </div>
         );
      },
      size: 100,
   },
   {
      accessorKey: 'name',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Name</div>,
      cell: ({ row }) => {
         const coupon = row.original;
         return (
            <div className='text-left'>
               <div className='text-sm font-medium break-words'>{coupon.name}</div>
            </div>
         );
      },
      size: 120,
   },
   {
      accessorKey: 'discountValue',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Discount</div>,
      cell: ({ row }) => {
         const coupon = row.original;
         const displayValue = formatDiscountDisplay(coupon.discountType, coupon.discountValue);
         return (
            <div className='text-center'>
               <div className='text-sm font-semibold'>{displayValue}</div>
               {coupon.discountType === 'PERCENTAGE' && coupon.maxDiscountLimit && (
                  <div className='text-xs text-gray-500'>
                     Max: ${parseFloat(coupon.maxDiscountLimit).toFixed(2)}
                  </div>
               )}
            </div>
         );
      },
      size: 120,
   },
   {
      accessorKey: 'usageCount',
      header: () => (
         <div className='text-center font-semibold text-gray-600 text-sm'>Usage Count</div>
      ),
      cell: ({ row }) => {
         const coupon = row.original;
         return (
            <div className='text-center'>
               <div className='text-sm font-medium'>{coupon.usageCount}</div>
            </div>
         );
      },
      size: 90,
   },
   {
      accessorKey: 'isActive',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Status</div>,
      cell: ({ row }) => {
         const coupon = row.original;
         return (
            <div className='flex justify-center'>
               <span
                  className={`px-2 py-1 rounded-full text-xs font-medium ${
                     coupon.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}
               >
                  {coupon.isActive ? 'Active' : 'Inactive'}
               </span>
            </div>
         );
      },
      size: 90,
   },
   {
      id: 'actions',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const coupon = row.original;
         const isDeleting = itemToDelete?.id === coupon.id && deleteMutation.isPending;
         const isToggling =
            toggleStatusMutation.variables === coupon.id && toggleStatusMutation.isPending;

         return (
            <div className='flex justify-center gap-1'>
               <button
                  className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() => {
                     withPermission(RBAC_PERMISSIONS.COUPON.EDIT, () => handleEditClick(coupon.id));
                  }}
                  disabled={isDeleting || isToggling}
               >
                  Edit
               </button>
               <button
                  className='text-sm font-medium text-gray-600 hover:text-green-600 border border-gray-300 hover:border-green-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() => {
                     withPermission(RBAC_PERMISSIONS.COUPON.STATUS_UPDATE, () =>
                        handleToggleStatus(coupon.id)
                     );
                  }}
                  disabled={isDeleting || isToggling}
               >
                  {coupon.isActive ? 'Deactivate' : 'Activate'}
               </button>
               <button
                  className='text-sm font-medium text-red-600 hover:text-red-700 border border-red-300 hover:border-red-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() => {
                     withPermission(RBAC_PERMISSIONS.COUPON.DELETE, () =>
                        handleDeleteClick(coupon)
                     );
                  }}
                  disabled={isDeleting || isToggling}
               >
                  {isDeleting ? '...' : 'Delete'}
               </button>
            </div>
         );
      },
      size: 240,
   },
];

// ============================================
// TABLE COMPONENT
// ============================================
interface CouponTableProps {
   data: ListCouponResponse | undefined;
   isLoading: boolean;
   currentPage: number;
   onPageChange: (page: number) => void;
   onOpenEditModal?: (couponId: string) => void;
}

export function CouponTable({
   data,
   isLoading,
   currentPage,
   onPageChange,
   onOpenEditModal,
}: CouponTableProps) {
   const [itemToDelete, setItemToDelete] = useState<Coupon | null>(null);
   const [itemToToggle, setItemToToggle] = useState<Coupon | null>(null);
   const deleteMutation = useDeleteCoupon();
   const toggleStatusMutation = useToggleCouponStatus();
   const queryClient = useQueryClient();
   const { withPermission } = useRoleBasedAccess();

   const handleEditClick = (id: string) => {
      onOpenEditModal?.(id);
   };

   const handleDeleteClick = (item: Coupon) => {
      setItemToDelete(item);
   };

   const handleToggleStatus = (id: string) => {
      const coupon = data?.data.find(c => c.id === id);
      if (coupon) {
         setItemToToggle(coupon);
      }
   };

   const handleToggleConfirm = () => {
      if (!itemToToggle) return;

      toggleStatusMutation.mutate(itemToToggle.id, {
         onSuccess: () => {
            toast.success('Coupon status updated successfully');
            queryClient.invalidateQueries({ queryKey: ['coupons'] });
         },
         onError: () => {
            toast.error('Failed to update coupon status');
         },
         onSettled: () => {
            setItemToToggle(null);
         },
      });
   };

   const handleDeleteConfirm = () => {
      if (!itemToDelete) return;

      deleteMutation.mutate(itemToDelete.id, {
         onSuccess: () => {
            toast.success('Coupon deleted successfully');
            queryClient.invalidateQueries({ queryKey: ['coupons'] });
         },
         onError: () => {
            toast.error('Failed to delete coupon');
         },
         onSettled: () => {
            setItemToDelete(null);
         },
      });
   };

   const columns = getColumns({
      handleEditClick,
      handleDeleteClick,
      handleToggleStatus,
      itemToDelete,
      deleteMutation,
      toggleStatusMutation,
      withPermission,
   });

   const table = useReactTable({
      data: data?.data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   if (isLoading) {
      return <CouponTableLoading />;
   }

   if (!data?.data?.length) {
      return <CouponTableEmpty />;
   }

   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full table-fixed'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize(), maxWidth: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                         header.column.columnDef.header,
                                         header.getContext()
                                      )}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td
                                 key={cell.id}
                                 className='px-4 py-3 align-middle'
                                 style={{
                                    width: cell.column.getSize(),
                                    maxWidth: cell.column.getSize(),
                                 }}
                              >
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>

         {data && data.meta && data.meta.totalPages > 1 && (
            <CustomPagination
               currentPage={currentPage}
               totalPages={data.meta.totalPages}
               onPageChange={onPageChange}
               hasNext={data.meta.hasNextPage}
               hasPrev={data.meta.hasPrevPage}
            />
         )}

         {/* Delete Confirmation Modal */}
         <CouponDeleteModal
            isOpen={!!itemToDelete}
            onClose={() => setItemToDelete(null)}
            onConfirm={handleDeleteConfirm}
            isLoading={deleteMutation.isPending}
            couponCode={itemToDelete?.code || ''}
            couponName={itemToDelete?.name || ''}
         />

         {/* Toggle Status Confirmation Modal */}
         <CouponToggleModal
            isOpen={!!itemToToggle}
            onClose={() => setItemToToggle(null)}
            onConfirm={handleToggleConfirm}
            isLoading={toggleStatusMutation.isPending}
            couponCode={itemToToggle?.code || ''}
            couponName={itemToToggle?.name || ''}
            isCurrentlyActive={itemToToggle?.isActive || false}
         />
      </div>
   );
}

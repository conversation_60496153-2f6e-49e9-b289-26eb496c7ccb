import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { CreateCouponRequest, CouponResponse, UpdateCouponRequest } from '../types/coupon';

/**
 * Hook for creating a new coupon
 */
export const useCreateCoupon = () => {
  return useMutation({
    mutationFn: async (data: CreateCouponRequest): Promise<CouponResponse> => {
      return apiClient.post('/admin/coupons', data);
    },
  });
};

/**
 * Hook for updating a coupon
 */
export const useUpdateCoupon = () => {
  return useMutation({
    mutationFn: async (data: { id: string } & UpdateCouponRequest): Promise<CouponResponse> => {
      const { id, ...payload } = data;
      return apiClient.put(`/admin/coupons/${id}`, payload);
    },
  });
};

/**
 * Hook for deleting a coupon (soft delete)
 */
export const useDeleteCoupon = () => {
  return useMutation({
    mutationFn: async (id: string): Promise<{ success: boolean; message: string; timestamp: number }> => {
      return apiClient.delete(`/admin/coupons/${id}`);
    },
  });
};

/**
 * Hook for toggling coupon active status
 */
export const useToggleCouponStatus = () => {
  return useMutation({
    mutationFn: async (
      id: string
    ): Promise<{ success: boolean; message: string; data: any; timestamp: number }> => {
      return apiClient.patch(`/admin/coupons/${id}/toggle-status`);
    },
  });
};

import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import {
  CouponResponse,
  ListCouponParams,
  ListCouponResponse,
  GetCityZonesResponse,
} from '../types/coupon';

/**
 * Hook for listing coupons with pagination and filters
 */
export const useListCoupons = ({
  page = 1,
  limit = 10,
  search,
  applicabilityType,
  discountType,
  isActive,
  cityId,
  productId,
  isExpired,
  sortBy,
  sortOrder,
}: ListCouponParams) => {
  const { hasPermission } = useRoleBasedAccess();

  return useQuery({
    placeholderData: keepPreviousData,
    enabled: hasPermission(RBAC_PERMISSIONS.COUPON.LIST),
    queryKey: [
      'coupons',
      page,
      limit,
      search,
      applicabilityType,
      discountType,
      isActive,
      cityId,
      productId,
      isExpired,
      sortBy,
      sortOrder,
    ],
    refetchOnWindowFocus: false,
    queryFn: async (): Promise<ListCouponResponse> => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      if (search) params.append('search', search);
      if (applicabilityType) params.append('applicabilityType', applicabilityType);
      if (discountType) params.append('discountType', discountType);
      if (isActive !== undefined) params.append('isActive', isActive.toString());
      if (cityId) params.append('cityId', cityId);
      if (productId) params.append('productId', productId);
      if (isExpired !== undefined) params.append('isExpired', isExpired.toString());
      if (sortBy) params.append('sortBy', sortBy);
      if (sortOrder) params.append('sortOrder', sortOrder);

      return apiClient.get(`/admin/coupons?${params.toString()}`);
    },
  });
};

/**
 * Hook for getting a single coupon by ID (for edit mode)
 */
export const useGetCoupon = (id: string | null) => {
  const { hasPermission } = useRoleBasedAccess();

  return useQuery({
    queryKey: ['coupon', id],
    queryFn: (): Promise<CouponResponse> => {
      return apiClient.get(`/admin/coupons/${id || ''}`);
    },
    enabled: !!id && hasPermission(RBAC_PERMISSIONS.COUPON.EDIT),
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook for getting zones for a specific city
 * Used in the zone selector component
 */
export const useGetCityZones = (cityId: string | null, enabled: boolean = true) => {
  return useQuery({
    queryKey: ['city-zones', cityId],
    queryFn: async (): Promise<GetCityZonesResponse> => {
      // Include relations to get city and zone type info
      return apiClient.get(`/cities/${cityId}/zones?includeRelations=true`);
    },
    enabled: !!cityId && enabled,
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes - zones don't change often
  });
};

/**
 * Hook for getting all active cities
 * Used for city dropdown in applicability selector
 */
export const useGetActiveCities = () => {
  return useQuery({
    queryKey: ['cities-active'],
    queryFn: async (): Promise<{ success: boolean; message: string; data: any[]; timestamp: number }> => {
      return apiClient.get('/cities/active');
    },
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook for getting all active products
 * Used for product multi-select in applicability selector
 */
export const useGetActiveProducts = () => {
  return useQuery({
    queryKey: ['products-active'],
    queryFn: async (): Promise<{ success: boolean; message: string; data: any[]; timestamp: number }> => {
      return apiClient.get('/products/active');
    },
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

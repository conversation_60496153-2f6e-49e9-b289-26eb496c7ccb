import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { UpdateRiderRequest, UpdateRiderResponse } from '../types/rider';

/**
 * Hook for updating a rider profile
 */
export const useUpdateRider = () => {
  return useMutation({
    mutationFn: async (data: { id: string } & UpdateRiderRequest): Promise<UpdateRiderResponse> => {
      const { id, ...payload } = data;
      return apiClient.patch(`/admin/riders/${id}`, payload);
    },
  });
};

/**
 * Hook for updating rider status (activate/deactivate)
 */
export const useUpdateRiderStatus = () => {
  return useMutation({
    mutationFn: async (data: {
      id: string;
      status: 'active' | 'disabled' | 'inactive';
    }): Promise<UpdateRiderResponse> => {
      return apiClient.patch(`/admin/riders/${data.id}`, { status: data.status });
    },
  });
};

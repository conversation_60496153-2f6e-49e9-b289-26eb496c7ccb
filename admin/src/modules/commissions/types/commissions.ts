// ============================================
// ENUMS
// ============================================
export type CommissionType = 'percentage' | 'flat';

// ============================================
// TAX GROUP INTERFACE (from relation)
// ============================================
export interface TaxGroupResponse {
  id: string;
  name: string;
  description?: string | null;
  totalPercentage: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
}

// ============================================
// CORE ENTITY INTERFACE
// ============================================
export interface Commission {
  id: string;
  name: string;
  description?: string | null;
  type: CommissionType;
  percentageValue?: string | null;
  flatValue?: string | null;
  taxGroupId?: string | null;
  taxGroup?: TaxGroupResponse | null;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
}

// ============================================
// API RESPONSE STRUCTURES
// ============================================
export interface ListCommissionsResponse {
  success: boolean;
  message: string;
  data: {
    data: Commission[];
    meta: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
    };
  };
  timestamp: number;
}

export interface CommissionResponse {
  success: boolean;
  message: string;
  data: Commission;
  timestamp: number;
}

export interface AllCommissionsResponse {
  success: boolean;
  message: string;
  data: Commission[];
  timestamp: number;
}

// ============================================
// REQUEST PAYLOADS
// ============================================
export interface CreateCommissionRequest {
  name: string;
  description?: string;
  type: CommissionType;
  percentageValue?: number | null;
  flatValue?: number | null;
  taxGroupId?: string;
}

export interface UpdateCommissionRequest {
  name?: string;
  description?: string;
  type?: CommissionType;
  percentageValue?: number | null;
  flatValue?: number | null;
  taxGroupId?: string;
}

// ============================================
// QUERY PARAMETERS
// ============================================
export interface ListCommissionsParams {
  page?: number;
  limit?: number;
  search?: string;
}

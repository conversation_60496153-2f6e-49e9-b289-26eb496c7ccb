'use client';

import { Card } from '@/components/ui/card';
import { useState } from 'react';
import { useListCommissions } from '../api/queries';
import { CommissionsFilters } from '../components/commissions-filters';
import { CommissionsModal } from '../components/commissions-modal';
import { CommissionsTable } from '../components/commissions-table';

export function CommissionsPage() {
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [search, setSearch] = useState('');

  // Reset to first page when filters change
  const handleSearchChange = (value: string) => {
    setSearch(value);
    setPage(1);
  };

  // Function to clear all filters
  const handleClearFilters = () => {
    setSearch('');
    setPage(1);
  };

  const listQuery = useListCommissions({
    page,
    limit,
    search: search || undefined,
  });

  return (
    <div className='flex flex-1 flex-col gap-4 p-6'>
      <div className='flex justify-between items-center'>
        <h2 className='text-2xl font-semibold text-gray-900'>Commissions</h2>
        <div className='flex items-center gap-4'>
          <CommissionsModal mode='create' />
        </div>
      </div>

      <Card className='overflow-hidden py-4 px-4 rounded-sm'>
        <CommissionsFilters
          search={search}
          onSearchChange={handleSearchChange}
          isLoading={listQuery.isFetching && !listQuery.isLoading}
        />

        <CommissionsTable
          data={listQuery.data}
          isLoading={listQuery.isLoading}
          currentPage={page}
          onPageChange={(newPage: number) => setPage(newPage)}
          hasFilters={!!search}
          hasSearch={!!search}
          onClearFilters={handleClearFilters}
        />
      </Card>
    </div>
  );
}

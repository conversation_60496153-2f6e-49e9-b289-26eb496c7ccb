'use client';

import { Card } from '@/components/ui/card';
import { useCallback, useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useListCities } from '../api/queries';
import { CityModal } from '../components/city-modal';
import { CityFilters } from '../components/city-filters';
import { CityTable } from '../components/city-table';

export function CityPage() {
   const router = useRouter();
   const searchParams = useSearchParams();
   const pageParam = searchParams.get('page');
   const [page, setPage] = useState(pageParam ? parseInt(pageParam, 10) : 1);
   const [limit] = useState(10);
   const [search, setSearch] = useState('');
   const [state, setState] = useState<string | undefined>(undefined);
   const [status, setStatus] = useState<string | undefined>(undefined);

   // Sync page state with URL params
   useEffect(() => {
      if (pageParam) {
         const parsedPage = parseInt(pageParam, 10);
         if (!isNaN(parsedPage) && parsedPage !== page) {
            setPage(parsedPage);
         }
      }
   }, [page, pageParam]);

   // Update URL when page changes
   const updatePage = useCallback(
      (newPage: number) => {
         setPage(newPage);
         const params = new URLSearchParams(searchParams.toString());
         params.set('page', newPage.toString());
         router.push(`?${params.toString()}`, { scroll: false });
      },
      [router, searchParams]
   );

   // Reset to first page when filters change
   const handleSearchChange = useCallback(
      (value: string) => {
         setSearch(value);
         updatePage(1);
      },
      [updatePage]
   );

   const handleStateChange = (value: string | undefined) => {
      setState(value);
      updatePage(1);
   };

   const handleStatusChange = (value: string | undefined) => {
      setStatus(value);
      updatePage(1);
   };

   // Function to clear all filters
   const handleClearFilters = () => {
      setSearch('');
      setState(undefined);
      setStatus(undefined);
      updatePage(1);
   };

   const listCities = useListCities({
      page,
      limit,
      search: search || undefined,
      state: state || undefined,
      status: status || undefined,
   });

   // Calculate total cities count from API meta data
   const totalCities = listCities.data?.meta?.total || 0;

   return (
      <div className='flex flex-1 flex-col gap-4 p-6'>
         <div className='flex justify-between items-center'>
            <h2 className='text-2xl font-semibold text-gray-900'>Cities</h2>
            <div className='flex items-center gap-4'>
               {/* City Info Cards */}
               <div className='flex gap-2'>
                  <div className='flex items-center gap-2 px-3 py-2 bg-gray-50 rounded-md border'>
                     <span className='text-sm text-gray-600'>Total Cities</span>
                     <span className='inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-blue-700 bg-blue-100 rounded-full'>
                        {totalCities}
                     </span>
                  </div>
               </div>
               <CityModal />
            </div>
         </div>

         <Card className='overflow-hidden py-4 px-4 rounded-sm'>
            <CityFilters
               search={search}
               state={state}
               status={status}
               onSearchChange={handleSearchChange}
               onStateChange={handleStateChange}
               onStatusChange={handleStatusChange}
               isLoading={listCities.isFetching && !listCities.isLoading}
            />

            <CityTable
               data={listCities.data}
               isLoading={listCities.isLoading}
               currentPage={page}
               onPageChange={updatePage}
               hasFilters={!!search || !!state || !!status}
               hasSearch={!!search}
               hasStatus={!!status}
               hasState={!!state}
               onClearFilters={handleClearFilters}
            />
         </Card>
      </div>
   );
}

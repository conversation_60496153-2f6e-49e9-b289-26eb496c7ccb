import { polygon, booleanIntersects, booleanContains, booleanOverlap } from '@turf/turf';
import { LatLng } from '../types/city';

/**
 * Converts LatLng coordinates to GeoJSON polygon format
 */
export function latLngToGeoJSON(coordinates: LatLng[]) {
  // Ensure the polygon is closed (first point equals last point)
  const coords = coordinates.length > 0 && 
    coordinates[0].lat === coordinates[coordinates.length - 1].lat &&
    coordinates[0].lng === coordinates[coordinates.length - 1].lng
    ? coordinates
    : [...coordinates, coordinates[0]];

  // Convert to GeoJSON format [lng, lat] (note: longitude first)
  const geoJsonCoords = coords.map(coord => [coord.lng, coord.lat]);
  
  return polygon([geoJsonCoords]);
}

/**
 * Checks if two polygons intersect
 */
export function doPolygonsIntersect(poly1: LatLng[], poly2: LatLng[]): boolean {
  if (poly1.length < 3 || poly2.length < 3) {
    return false;
  }

  try {
    const polygon1 = latLngToGeoJSON(poly1);
    const polygon2 = latLngToGeoJSON(poly2);
    
    // Check for any type of intersection
    return booleanIntersects(polygon1, polygon2) || 
           booleanContains(polygon1, polygon2) || 
           booleanContains(polygon2, polygon1) ||
           booleanOverlap(polygon1, polygon2);
  } catch (error) {
    console.warn('Error checking polygon intersection:', error);
    return false;
  }
}

/**
 * Checks if a new polygon intersects with any existing polygons
 */
export function checkPolygonIntersections(
  newPolygon: LatLng[], 
  existingPolygons: LatLng[][],
  excludeIndex?: number
): {
  hasIntersection: boolean;
  intersectingIndexes: number[];
} {
  const intersectingIndexes: number[] = [];
  
  for (let i = 0; i < existingPolygons.length; i++) {
    // Skip the polygon at excludeIndex (useful when editing existing polygons)
    if (excludeIndex !== undefined && i === excludeIndex) {
      continue;
    }
    
    if (doPolygonsIntersect(newPolygon, existingPolygons[i])) {
      intersectingIndexes.push(i);
    }
  }
  
  return {
    hasIntersection: intersectingIndexes.length > 0,
    intersectingIndexes
  };
}

/**
 * Validates polygon before saving
 */
export function validatePolygon(coordinates: LatLng[]): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  // Check minimum points
  if (coordinates.length < 4) { // 3 points + closing point
    errors.push('Polygon must have at least 3 points');
  }
  
  // Check if polygon is closed
  if (coordinates.length > 0) {
    const first = coordinates[0];
    const last = coordinates[coordinates.length - 1];
    if (first.lat !== last.lat || first.lng !== last.lng) {
      errors.push('Polygon must be closed (first and last points should be the same)');
    }
  }
  
  // Check for duplicate consecutive points
  for (let i = 1; i < coordinates.length; i++) {
    const curr = coordinates[i];
    const prev = coordinates[i - 1];
    if (curr.lat === prev.lat && curr.lng === prev.lng && i !== coordinates.length - 1) {
      errors.push('Polygon contains duplicate consecutive points');
      break;
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Checks if a zone polygon is completely within the city boundary
 */
export function checkZoneWithinCityBoundary(
  zonePolygon: LatLng[], 
  cityBoundary: LatLng[]
): {
  isWithin: boolean;
  error?: string;
} {
  // If no city boundary is defined, allow the zone
  if (!cityBoundary || cityBoundary.length < 4) {
    return { isWithin: true };
  }
  
  // If zone polygon is empty or invalid, reject
  if (!zonePolygon || zonePolygon.length < 4) {
    return { 
      isWithin: false, 
      error: 'Invalid zone polygon' 
    };
  }

  try {
    const zonePoly = latLngToGeoJSON(zonePolygon);
    const cityPoly = latLngToGeoJSON(cityBoundary);
    
    // Check if the zone is completely contained within the city boundary
    const isContained = booleanContains(cityPoly, zonePoly);
    
    if (!isContained) {
      // Also check if they're the same (edge case)
      const isEqual = booleanIntersects(zonePoly, cityPoly) && 
                     booleanIntersects(cityPoly, zonePoly) &&
                     !booleanOverlap(zonePoly, cityPoly);
      
      if (!isEqual) {
        return {
          isWithin: false,
          error: 'Zone boundary must be completely within the city boundary'
        };
      }
    }
    
    return { isWithin: true };
  } catch (error) {
    console.warn('Error checking zone within city boundary:', error);
    return {
      isWithin: false,
      error: 'Error validating zone boundary against city boundary'
    };
  }
}

/**
 * Checks if a city boundary contains all existing zone polygons
 */
export function checkCityBoundaryContainsZones(
  cityBoundary: LatLng[],
  zonePolygons: LatLng[][]
): {
  isValid: boolean;
  error?: string;
  excludedZones?: number[];
} {
  // If no zones exist, any city boundary is valid
  if (!zonePolygons || zonePolygons.length === 0) {
    return { isValid: true };
  }

  // If city boundary is empty or invalid, reject
  if (!cityBoundary || cityBoundary.length < 4) {
    return {
      isValid: false,
      error: 'Invalid city boundary'
    };
  }

  const excludedZones: number[] = [];

  try {
    const cityPoly = latLngToGeoJSON(cityBoundary);

    // Check each zone polygon
    for (let i = 0; i < zonePolygons.length; i++) {
      const zonePolygon = zonePolygons[i];
      
      // Skip empty or invalid zone polygons
      if (!zonePolygon || zonePolygon.length < 4) {
        continue;
      }

      const zonePoly = latLngToGeoJSON(zonePolygon);
      
      // Check if the zone is completely contained within the city boundary
      const isContained = booleanContains(cityPoly, zonePoly);
      
      if (!isContained) {
        // Also check if they're the same (edge case)
        const isEqual = booleanIntersects(zonePoly, cityPoly) && 
                       booleanIntersects(cityPoly, zonePoly) &&
                       !booleanOverlap(zonePoly, cityPoly);
        
        if (!isEqual) {
          excludedZones.push(i);
        }
      }
    }

    if (excludedZones.length > 0) {
      return {
        isValid: false,
        error: `City boundary must contain all existing zones. ${excludedZones.length} zone(s) would be outside the boundary.`,
        excludedZones
      };
    }

    return { isValid: true };
  } catch (error) {
    console.warn('Error checking city boundary contains zones:', error);
    return {
      isValid: false,
      error: 'Error validating city boundary against existing zones'
    };
  }
}
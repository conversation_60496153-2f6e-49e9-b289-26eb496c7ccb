import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';
import { DriverDueLimitResponse } from '../types/driver-due-limit';

/**
 * Hook for fetching driver due limit configuration for a specific city
 * Returns 404 if no configuration exists (which means we should create one)
 */
export const useGetDriverDueLimit = (cityId: string | null) => {
  return useQuery({
    queryKey: ['driver-due-limit', cityId],
    queryFn: async (): Promise<DriverDueLimitResponse> => {
      return apiClient.get(`/admin/config/driver-due-limit/${cityId}`);
    },
    enabled: !!cityId,
    refetchOnWindowFocus: false,
    retry: false, // Don't retry on 404 - it just means no config exists yet
  });
};

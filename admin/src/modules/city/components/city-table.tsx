'use client';

import { useQueryClient } from '@tanstack/react-query';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { City, ListCityResponse } from '../types/city';
import { CityModal } from './city-modal';
import { CityTableEmpty } from './city-table-empty';
import { CityTableFilteredEmpty } from './city-table-filtered-empty';
import { CityTableLoading } from './city-table-loading';
import { CityStatusModal } from './city-status-modal';
import { CityDueLimitModal } from './city-due-limit-modal';
import { CustomPagination } from '@/components/pagination';
import {
   USE_ROLE_BASED_ACCESS,
   useRoleBasedAccess,
} from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

// Define the columns for the table
const getColumns = ({
   handleEditClick,
   handleViewClick,
   handleStatusChange,
   handleDueLimitClick,
   withPermission,
   hasPermission,
   currentPage,
}: {
   handleEditClick: (id: string) => void;
   handleViewClick: (id: string) => void;
   handleStatusChange: (
      id: string,
      currentStatus: 'active' | 'inactive',
      newStatus: 'active' | 'inactive'
   ) => void;
   handleDueLimitClick: (id: string, name: string) => void;
   withPermission: USE_ROLE_BASED_ACCESS['withPermission'];
   hasPermission: (permission: string) => boolean;
   currentPage: number;
}): ColumnDef<City>[] => [
   {
      accessorKey: 'name',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>City</div>,
      cell: ({ row }) => {
         const city = row.original as City;
         return (
            <div className='text-left'>
               <div className='font-semibold text-sm'>{city.name}</div>
               <div className='text-xs text-gray-500'>ID: {city.id.slice(0, 8)}...</div>
            </div>
         );
      },
      size: 200,
   },
   {
      accessorKey: 'location',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Location</div>,
      cell: ({ row }) => {
         const city = row.original as City;
         return (
            <div className='text-left'>
               <div className='text-sm'>{city.state || '-'}</div>
               <div className='text-xs text-gray-500'>{city.country || '-'}</div>
            </div>
         );
      },
      size: 180,
   },
   {
      accessorKey: 'status',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Status</div>,
      cell: ({ row }) => {
         const city = row.original as City;
         const status = city.status || 'inactive';

         // Format the status for display
         const formatStatus = (status: string) => {
            return status.charAt(0).toUpperCase() + status.slice(1);
         };

         // Status color mapping
         const getStatusColor = (status: string) => {
            switch (status.toLowerCase()) {
               case 'active':
                  return 'bg-green-100 text-green-800';
               case 'inactive':
                  return 'bg-gray-100 text-gray-800';
               default:
                  return 'bg-gray-100 text-gray-800';
            }
         };

         return (
            <div className='text-left'>
               <span
                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                     status
                  )}`}
               >
                  {formatStatus(status)}
               </span>
            </div>
         );
      },
      size: 120,
   },
   {
      accessorKey: 'cityProducts',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Products</div>,
      cell: ({ row }) => {
         const city = row.original as City;
         const productCount = city.cityProducts?.length || 0;
         return (
            <div className='text-center'>
               <div className='text-sm font-medium'>{productCount}</div>
            </div>
         );
      },
      size: 180,
   },
   {
      id: 'actions',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const city = row.original as City;
         const currentStatus = city.status as 'active' | 'inactive';
         const newStatus = currentStatus === 'active' ? 'inactive' : 'active';

         return (
            <div className='flex justify-center gap-2 flex-wrap'>
               {hasPermission(RBAC_PERMISSIONS.CITY.MANAGE) ? (
                  <Link
                     href={`/dashboard/cities/${city.id}?returnPage=${currentPage}`}
                     className='text-sm font-medium text-gray-600 hover:text-gray-900 border border-gray-300 hover:border-gray-400 px-3 py-1.5 rounded-md transition-colors bg-white cursor-pointer whitespace-nowrap'
                  >
                     Manage
                  </Link>
               ) : (
                  <button
                     className='text-sm font-medium text-gray-600 hover:text-gray-900 border border-gray-300 hover:border-gray-400 px-3 py-1.5 rounded-md transition-colors bg-white cursor-pointer whitespace-nowrap'
                     onClick={() =>
                        withPermission(RBAC_PERMISSIONS.CITY.MANAGE, () => handleViewClick(city.id))
                     }
                  >
                     Manage
                  </button>
               )}

               <button
                  className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-3 py-1.5 rounded-md transition-colors bg-white cursor-pointer whitespace-nowrap'
                  onClick={() =>
                     withPermission(RBAC_PERMISSIONS.CITY.EDIT, () => handleEditClick(city.id))
                  }
               >
                  Edit
               </button>

               <button
                  className='text-sm font-medium text-blue-600 hover:text-blue-700 border border-blue-300 hover:border-blue-400 px-3 py-1.5 rounded-md transition-colors bg-white cursor-pointer whitespace-nowrap'
                  onClick={() =>
                     withPermission(RBAC_PERMISSIONS.CITY.EDIT, () =>
                        handleDueLimitClick(city.id, city.name)
                     )
                  }
               >
                  Due Limit
               </button>

               <button
                  className={`text-sm font-medium border px-3 py-1.5 rounded-md transition-colors bg-white cursor-pointer whitespace-nowrap ${
                     currentStatus === 'active'
                        ? 'text-orange-600 hover:text-orange-700 border-orange-300 hover:border-orange-400'
                        : 'text-green-600 hover:text-green-700 border-green-300 hover:border-green-400'
                  }`}
                  onClick={() =>
                     withPermission(RBAC_PERMISSIONS.CITY.STATUS_UPDATE, () =>
                        handleStatusChange(city.id, currentStatus, newStatus)
                     )
                  }
               >
                  {newStatus === 'active' ? 'Activate' : 'Deactivate'}
               </button>
            </div>
         );
      },
      size: 380,
   },
];

interface CityTableProps {
   data: ListCityResponse | undefined;
   isLoading: boolean;
   currentPage: number;
   onPageChange: (page: number) => void;
   hasFilters: boolean;
   hasSearch: boolean;
   hasStatus: boolean;
   hasState: boolean;
   onClearFilters: () => void;
}

export function CityTable({
   data,
   isLoading,
   currentPage,
   onPageChange,
   hasFilters,
   hasSearch,
   hasStatus,
   hasState,
   onClearFilters,
}: CityTableProps) {
   const [cityToEdit, setCityToEdit] = useState<string | null>(null);
   const [statusModal, setStatusModal] = useState<{
      isOpen: boolean;
      cityId: string;
      cityName: string;
      currentStatus: 'active' | 'inactive';
      newStatus: 'active' | 'inactive';
   }>({
      isOpen: false,
      cityId: '',
      cityName: '',
      currentStatus: 'inactive',
      newStatus: 'active',
   });
   const [dueLimitModal, setDueLimitModal] = useState<{
      isOpen: boolean;
      cityId: string;
      cityName: string;
   }>({
      isOpen: false,
      cityId: '',
      cityName: '',
   });

   const { withPermission, hasPermission } = useRoleBasedAccess();
   const queryClient = useQueryClient();
   const router = useRouter();

   const handleEditClick = (id: string) => {
      setCityToEdit(id);
   };

   const handleViewClick = (id: string) => {
      router.push(`/dashboard/cities/${id}?returnPage=${currentPage}`);
   };

   const handleStatusChange = (
      cityId: string,
      currentStatus: 'active' | 'inactive',
      newStatus: 'active' | 'inactive'
   ) => {
      const city = data?.data.find(c => c.id === cityId);
      if (!city) return;

      setStatusModal({
         isOpen: true,
         cityId,
         cityName: city.name,
         currentStatus,
         newStatus,
      });
   };

   const handleDueLimitClick = (cityId: string, cityName: string) => {
      setDueLimitModal({
         isOpen: true,
         cityId,
         cityName,
      });
   };

   const handleStatusUpdated = () => {
      queryClient.invalidateQueries({ queryKey: ['cities'] });
   };

   const cities = data?.data || [];
   const meta = data?.meta;

   const table = useReactTable({
      data: cities,
      columns: getColumns({
         handleEditClick,
         handleViewClick,
         handleStatusChange,
         handleDueLimitClick,
         withPermission,
         hasPermission,
         currentPage,
      }),
      getCoreRowModel: getCoreRowModel(),
   });

   if (isLoading) {
      return <CityTableLoading />;
   }

   if (!cities.length && hasFilters) {
      return (
         <CityTableFilteredEmpty
            hasSearch={hasSearch}
            hasStatus={hasStatus}
            hasState={hasState}
            onClearFilters={onClearFilters}
         />
      );
   }

   if (!cities.length) {
      return <CityTableEmpty />;
   }

   return (
      <div className='space-y-4'>
         <div className='overflow-x-auto'>
            <table className='w-full border-collapse'>
               <thead>
                  {table.getHeaderGroups().map(headerGroup => (
                     <tr key={headerGroup.id} className='border-b border-gray-200'>
                        {headerGroup.headers.map(header => (
                           <th
                              key={header.id}
                              className='text-left py-3 px-4 font-medium text-gray-600'
                              style={{ width: header.getSize() }}
                           >
                              {header.isPlaceholder
                                 ? null
                                 : flexRender(header.column.columnDef.header, header.getContext())}
                           </th>
                        ))}
                     </tr>
                  ))}
               </thead>
               <tbody>
                  {table.getRowModel().rows.map(row => (
                     <tr key={row.id} className='border-b border-gray-100 hover:bg-gray-50'>
                        {row.getVisibleCells().map(cell => (
                           <td key={cell.id} className='py-3 px-4'>
                              {flexRender(cell.column.columnDef.cell, cell.getContext())}
                           </td>
                        ))}
                     </tr>
                  ))}
               </tbody>
            </table>
         </div>

         {meta && (
            <CustomPagination
               currentPage={currentPage}
               totalPages={meta.totalPages}
               onPageChange={onPageChange}
               hasNext={meta.hasNextPage}
               hasPrev={meta.hasPreviousPage}
            />
         )}

         {/* Edit City Modal */}
         {cityToEdit && (
            <CityModal
               cityId={cityToEdit}
               mode='edit'
               isOpen={!!cityToEdit}
               onClose={() => setCityToEdit(null)}
               onCityUpdated={() => {
                  queryClient.invalidateQueries({ queryKey: ['cities'] });
                  setCityToEdit(null);
               }}
            />
         )}

         {/* Status Change Modal */}
         <CityStatusModal
            isOpen={statusModal.isOpen}
            onClose={() => setStatusModal(prev => ({ ...prev, isOpen: false }))}
            cityId={statusModal.cityId}
            cityName={statusModal.cityName}
            currentStatus={statusModal.currentStatus}
            newStatus={statusModal.newStatus}
            onStatusUpdated={handleStatusUpdated}
         />

         {/* Due Limit Modal */}
         <CityDueLimitModal
            isOpen={dueLimitModal.isOpen}
            onClose={() => setDueLimitModal(prev => ({ ...prev, isOpen: false }))}
            cityId={dueLimitModal.cityId}
            cityName={dueLimitModal.cityName}
         />
      </div>
   );
}

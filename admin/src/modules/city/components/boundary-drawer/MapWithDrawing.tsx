'use client';

import { useRef, useCallback, useEffect, useState, useMemo } from 'react';
import { GoogleMap, Data } from '@react-google-maps/api';
import { LatLng } from '../../types/city';
import { DrawingMode } from '../../types/drawing';
import { MapLabel } from './MapLabel';
import { MapPolygons } from './MapPolygons';
import { polygonOptions, zonePolygonOptions } from './polygon-constants';
// import { processPolygonCompletion } from '../../utils/geometry-utils';

interface MapWithDrawingProps {
   center: { lat: number; lng: number };
   zoom: number;
   drawingMode: DrawingMode;
   existingPolygon: LatLng[];
   otherCityPolygons?: LatLng[][];
   onPolygonComplete: (polygon: google.maps.Polygon) => void;
   onPolygonEdit: (coordinates: LatLng[]) => void;
   onZoomChange?: (zoom: number) => void;
   onMapLoad?: (map: google.maps.Map) => void;
   disableDrawing?: boolean;
   // Cancel drawing support
   cancelDrawingRef?: React.MutableRefObject<boolean>;
   // Zone-specific props
   isZoneMode?: boolean;
   cityBoundary?: LatLng[];
   otherZonePolygons?: LatLng[][];
   // City mode zone visualization
   zonePolygonsForCityMode?: LatLng[][];
   // Label props
   showLabels?: boolean;
   showCityZoneNames?: boolean;
   currentPolygonLabel?: string;
   otherCityNames?: string[];
   otherZoneNames?: string[];
   zoneNamesForCityMode?: string[];
   cityName?: string;
}

const mapContainerStyle = {
   width: '100%',
   height: '100%',
};

export function MapWithDrawing({
   center,
   zoom,
   drawingMode,
   existingPolygon,
   otherCityPolygons = [],
   onPolygonComplete,
   onPolygonEdit,
   onZoomChange,
   onMapLoad,
   disableDrawing = false,
   // Cancel drawing support
   cancelDrawingRef,
   // Zone-specific props
   isZoneMode = false,
   cityBoundary = [],
   otherZonePolygons = [],
   // City mode zone visualization
   zonePolygonsForCityMode = [],
   // Label props
   showLabels = false,
   showCityZoneNames = false,
   currentPolygonLabel,
   otherCityNames = [],
   otherZoneNames = [],
   zoneNamesForCityMode = [],
   cityName,
}: MapWithDrawingProps) {
   const mapRef = useRef<google.maps.Map | null>(null);
   const dataLayerRef = useRef<google.maps.Data | null>(null);
   const polygonRef = useRef<google.maps.Polygon | null>(null);
   const [polygonPaths, setPolygonPaths] = useState<google.maps.LatLng[]>([]);
   const [mouseCoordinates, setMouseCoordinates] = useState<{ lat: number; lng: number } | null>(
      null
   );

   // Convert LatLng[] to google.maps.LatLng[] for rendering
   useEffect(() => {
      if (existingPolygon.length > 0) {
         const paths = existingPolygon
            .slice(0, -1)
            .map(coord => new google.maps.LatLng(coord.lat, coord.lng));
         setPolygonPaths(paths);
      } else {
         setPolygonPaths([]);
      }
   }, [existingPolygon]);

   const onLoad = useCallback(
      (map: google.maps.Map) => {
         mapRef.current = map;

         // Call the parent's onMapLoad callback if provided
         if (onMapLoad) {
            onMapLoad(map);
         }

         // Add DOM-level event listener to capture mouse events in all modes
         const mapDiv = map.getDiv();
         const handleMouseMove = (event: MouseEvent) => {
            // Get the mouse position relative to the map
            const rect = mapDiv.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            // Convert to lat/lng using the map's current bounds
            const bounds = map.getBounds();
            if (bounds) {
               const ne = bounds.getNorthEast();
               const sw = bounds.getSouthWest();

               const lat = sw.lat() + (ne.lat() - sw.lat()) * (1 - y / rect.height);
               const lng = sw.lng() + (ne.lng() - sw.lng()) * (x / rect.width);

               setMouseCoordinates({ lat, lng });
            }
         };

         mapDiv.addEventListener('mousemove', handleMouseMove, true); // Use capture phase
      },
      [onMapLoad]
   );

   const handleZoomChanged = useCallback(() => {
      if (mapRef.current && onZoomChange) {
         const currentZoom = mapRef.current.getZoom();
         if (currentZoom !== undefined) {
            onZoomChange(currentZoom);
         }
      }
   }, [onZoomChange]);

   const updateCoordinates = useCallback(
      (polygon: google.maps.Polygon) => {
         const path = polygon.getPath();
         const coordinates: LatLng[] = [];

         for (let i = 0; i < path.getLength(); i++) {
            const point = path.getAt(i);
            coordinates.push({
               lat: point.lat(),
               lng: point.lng(),
            });
         }

         // Ensure closed polygon
         if (coordinates.length > 0) {
            coordinates.push(coordinates[0]);
         }

         onPolygonEdit(coordinates);
      },
      [onPolygonEdit]
   );

   // Feature factory callback - called when drawing is completed
   const handleFeatureFactory = useCallback(
      (geometry: google.maps.Data.Geometry) => {
         // Check if drawing was canceled (Escape key pressed)
         if (cancelDrawingRef?.current) {
            // Reset the cancel flag
            cancelDrawingRef.current = false;
            // return new google.maps.Data.Feature({ geometry });
            return null;
         }

         // Convert Data layer geometry to polygon coordinates for backward compatibility
         if (geometry.getType() === 'Polygon') {
            const polygon = geometry as google.maps.Data.Polygon;
            const coordinates = polygon.getArray()[0]; // Get outer ring coordinates

            // Extract coordinates in the format expected by parent
            const coords: LatLng[] = [];
            for (let i = 0; i < coordinates.getLength(); i++) {
               const point = coordinates.getAt(i);
               coords.push({
                  lat: point.lat(),
                  lng: point.lng(),
               });
            }

            // Ensure closed polygon
            if (coords.length > 0) {
               coords.push(coords[0]);
            }

            // Process the polygon with WKT/WKB conversion (for debugging if needed)
            // const polygonData = processPolygonCompletion(coords);

            // Create a mock polygon object that matches the expected interface
            const mockPath = {
               getLength: () => coordinates.getLength(),
               getAt: (index: number) => coordinates.getAt(index),
            };

            const mockPolygon = {
               getPath: () => mockPath,
               setMap: () => {}, // No-op since Data layer handles this
            } as unknown as google.maps.Polygon;

            // Clear any existing polygon from the map
            if (polygonRef.current) {
               polygonRef.current.setMap(null);
            }

            // Store the new polygon reference
            polygonRef.current = mockPolygon;

            // Call the parent handler (parent will handle cancel logic)
            onPolygonComplete(mockPolygon);
         }

         // Return null to prevent the Data layer from showing the polygon
         // The MapPolygons component will handle the display
         return null;
      },
      [onPolygonComplete, cancelDrawingRef]
   );

   const onDataLoad = useCallback(
      (data: google.maps.Data) => {
         dataLayerRef.current = data;

         // Apply styling for drawing lines
         data.setStyle({
            fillColor: isZoneMode
               ? zonePolygonOptions.fillColor || '#10B981'
               : polygonOptions.fillColor || '#FF6B6B',
            fillOpacity: isZoneMode
               ? zonePolygonOptions.fillOpacity || 0.35
               : polygonOptions.fillOpacity || 0.35,
            strokeColor: isZoneMode
               ? zonePolygonOptions.strokeColor || '#059669'
               : polygonOptions.strokeColor || '#E55555',
            strokeOpacity: isZoneMode
               ? zonePolygonOptions.strokeOpacity || 0.8
               : polygonOptions.strokeOpacity || 0.8,
            strokeWeight: isZoneMode
               ? zonePolygonOptions.strokeWeight || 2
               : polygonOptions.strokeWeight || 2,
            clickable: true,
            editable: drawingMode === DrawingMode.EDITING,
            draggable: false,
         });
      },
      [isZoneMode, drawingMode]
   );

   // Data layer options - configured like the working example
   const dataOptions = useMemo(
      () =>
         ({
            controlPosition: window.google?.maps?.ControlPosition?.TOP_LEFT,
            controls: ['Point'],
            featureFactory: handleFeatureFactory,
            clickable: true,
            draggable: false,
            editable: drawingMode === DrawingMode.EDITING,
            fillColor: isZoneMode
               ? zonePolygonOptions.fillColor || '#10B981'
               : polygonOptions.fillColor || '#FF6B6B',
            fillOpacity: isZoneMode
               ? zonePolygonOptions.fillOpacity || 0.35
               : polygonOptions.fillOpacity || 0.35,
            strokeColor: isZoneMode
               ? zonePolygonOptions.strokeColor || '#059669'
               : polygonOptions.strokeColor || '#E55555',
            strokeOpacity: isZoneMode
               ? zonePolygonOptions.strokeOpacity || 0.8
               : polygonOptions.strokeOpacity || 0.8,
            strokeWeight: isZoneMode
               ? zonePolygonOptions.strokeWeight || 2
               : polygonOptions.strokeWeight || 2,
            visible: true,
            zIndex: 2,
         } as any),
      [drawingMode, isZoneMode, handleFeatureFactory]
   );

   // Update Data layer drawing mode and styling when mode changes
   useEffect(() => {
      if (dataLayerRef.current) {
         const shouldEnableDrawing =
            (drawingMode === DrawingMode.DRAWING || drawingMode === DrawingMode.REDRAWING) &&
            !disableDrawing;
         dataLayerRef.current.setDrawingMode(shouldEnableDrawing ? 'Polygon' : null);

         // Update styling
         dataLayerRef.current.setStyle({
            fillColor: isZoneMode
               ? zonePolygonOptions.fillColor || '#10B981'
               : polygonOptions.fillColor || '#FF6B6B',
            fillOpacity: isZoneMode
               ? zonePolygonOptions.fillOpacity || 0.35
               : polygonOptions.fillOpacity || 0.35,
            strokeColor: isZoneMode
               ? zonePolygonOptions.strokeColor || '#059669'
               : polygonOptions.strokeColor || '#E55555',
            strokeOpacity: isZoneMode
               ? zonePolygonOptions.strokeOpacity || 0.8
               : polygonOptions.strokeOpacity || 0.8,
            strokeWeight: isZoneMode
               ? zonePolygonOptions.strokeWeight || 2
               : polygonOptions.strokeWeight || 2,
            clickable: true,
            editable: drawingMode === DrawingMode.EDITING,
            draggable: false,
         });
      }
   }, [drawingMode, disableDrawing, isZoneMode]);

   // Clear data layer features when canceling
   useEffect(() => {
      if (cancelDrawingRef?.current && dataLayerRef.current) {
         // Clear any drawn features that haven't been processed yet
         dataLayerRef.current.forEach(feature => {
            dataLayerRef.current!.remove(feature);
         });
      }
   }, [cancelDrawingRef]);

   // Event listeners effect
   useEffect(() => {
      if (!polygonRef.current || drawingMode !== DrawingMode.EDITING) {
         return;
      }

      const polygon = polygonRef.current;
      const path = polygon.getPath();

      const handleUpdate = () => updateCoordinates(polygon);

      // Add event listeners
      google.maps.event.addListener(path, 'set_at', handleUpdate);
      google.maps.event.addListener(path, 'insert_at', handleUpdate);
      google.maps.event.addListener(path, 'remove_at', handleUpdate);

      // Cleanup function
      return () => {
         google.maps.event.clearInstanceListeners(path);
      };
   }, [drawingMode, updateCoordinates]);

   const onPolygonLoad = useCallback((polygon: google.maps.Polygon) => {
      polygonRef.current = polygon;
   }, []);

   const handlePolygonEdit = useCallback(() => {
      if (polygonRef.current) {
         const path = polygonRef.current.getPath();
         const coordinates: LatLng[] = [];

         for (let i = 0; i < path.getLength(); i++) {
            const point = path.getAt(i);
            coordinates.push({
               lat: point.lat(),
               lng: point.lng(),
            });
         }

         if (coordinates.length > 0) {
            coordinates.push(coordinates[0]);
         }

         onPolygonEdit(coordinates);
      }
   }, [onPolygonEdit]);

   return (
      <GoogleMap
         mapContainerStyle={mapContainerStyle}
         center={center}
         zoom={zoom}
         onLoad={onLoad}
         onZoomChanged={handleZoomChanged}
         options={{
            gestureHandling: 'greedy',
            disableDefaultUI: true,
            zoomControl: true,
            mapTypeControl: false,
            streetViewControl: false,
            fullscreenControl: false,
         }}
      >
         {/* Data Layer - provides zoom-stable drawing functionality */}
         <Data onLoad={onDataLoad} options={dataOptions} />

         <MapPolygons
            polygonPaths={polygonPaths}
            drawingMode={drawingMode}
            isZoneMode={isZoneMode}
            onPolygonLoad={onPolygonLoad}
            handlePolygonEdit={handlePolygonEdit}
            cityBoundary={cityBoundary}
            otherZonePolygons={otherZonePolygons}
            otherCityPolygons={otherCityPolygons}
            zonePolygonsForCityMode={zonePolygonsForCityMode}
         />

         <MapLabel
            showLabels={showLabels}
            zoom={zoom}
            drawingMode={drawingMode}
            isZoneMode={isZoneMode}
            currentPolygonLabel={currentPolygonLabel}
            existingPolygon={existingPolygon}
            cityName={cityName}
            cityBoundary={cityBoundary}
            otherZonePolygons={otherZonePolygons}
            otherZoneNames={otherZoneNames}
            otherCityPolygons={otherCityPolygons}
            otherCityNames={otherCityNames}
            showCityZoneNames={showCityZoneNames}
            zonePolygonsForCityMode={zonePolygonsForCityMode}
            zoneNamesForCityMode={zoneNamesForCityMode}
         />

         {/* Coordinate Tracker */}
         {mouseCoordinates && (
            <div className='absolute bottom-4 left-4 bg-white rounded-lg shadow-md border border-gray-200 p-3 text-sm font-mono z-10'>
               <div className='text-gray-600 mb-1'>Coordinates</div>
               <div className='space-y-1'>
                  <div>Lat: {mouseCoordinates.lat.toFixed(6)}</div>
                  <div>Lng: {mouseCoordinates.lng.toFixed(6)}</div>
               </div>
            </div>
         )}
      </GoogleMap>
   );
}

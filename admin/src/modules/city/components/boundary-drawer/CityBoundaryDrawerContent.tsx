'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/lib/toast';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useUpdateCity } from '../../api/mutations';
import { useNearbyCities } from '../../api/queries';
import { useCityZones } from '../../api/city-zone-queries';
import { City, LatLng } from '../../types/city';
import { DrawingMode } from '../../types/drawing';
import {
   checkPolygonIntersections,
   validatePolygon,
   checkCityBoundaryContainsZones,
} from '../../utils/polygon-utils';
import { CitySearch } from './CitySearch';
import { DrawingControls } from './DrawingControls';
import { MapWithDrawing } from './MapWithDrawing';

interface CityBoundaryDrawerContentProps {
   cityDetails: City;
   isFullscreen?: boolean;
   onFullscreen?: () => void;
}

export function CityBoundaryDrawerContent({
   cityDetails,
   isFullscreen = false,
   onFullscreen,
}: CityBoundaryDrawerContentProps) {
   const existingPolygon = useMemo(() => cityDetails.polygon || [], [cityDetails.polygon]);

   const [drawingMode, setDrawingMode] = useState<DrawingMode>(DrawingMode.VIEWING);
   const [activePolygon, setActivePolygon] = useState<google.maps.Polygon | null>(null);
   const [polygonCoordinates, setPolygonCoordinates] = useState<LatLng[]>(existingPolygon);
   const [mapCenter, setMapCenter] = useState<{ lat: number; lng: number }>({
      lat: 10,
      lng: 77,
   });
   const [mapZoom, setMapZoom] = useState(9);
   const [hasAutoZoomed, setHasAutoZoomed] = useState(false);
   const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
   const [hasCompletedRedrawing, setHasCompletedRedrawing] = useState(false);
   const cancelDrawingRef = useRef<boolean>(false);
   const drawingModeRef = useRef<DrawingMode>(drawingMode);
   const mapRef = useRef<google.maps.Map | null>(null);

   // Update the ref whenever drawingMode changes
   useEffect(() => {
      drawingModeRef.current = drawingMode;
   }, [drawingMode]);

   // React Query hooks
   const { data: allCities, isLoading: allCitiesLoading } = useNearbyCities({});
   const { data: cityZones, isLoading: cityZonesLoading } = useCityZones(cityDetails.id, {
      includeRelations: false,
   });
   const updateCityMutation = useUpdateCity();

   // Derived data
   const zonePolygons = useMemo(() => {
      return (
         cityZones?.data
            ?.filter(zone => zone.polygon && zone.polygon.length > 0)
            .map(zone => zone.polygon!) || []
      );
   }, [cityZones?.data]);

   const zoneNames = useMemo(() => {
      return (
         cityZones?.data
            ?.filter(zone => zone.polygon && zone.polygon.length > 0)
            .map(zone => zone.name) || []
      );
   }, [cityZones?.data]);

   const otherCityNames = useMemo(() => {
      return (
         allCities?.data
            ?.filter(c => c.id !== cityDetails.id && c.polygon && c.polygon.length > 0)
            .map(c => c.name) || []
      );
   }, [allCities?.data, cityDetails.id]);


   const handleStartDrawing = useCallback(() => {
      if (activePolygon) {
         activePolygon.setMap(null);
      }
      setActivePolygon(null);

      // Check if we're redrawing an existing polygon
      const hasExistingPolygon = existingPolygon && existingPolygon.length > 0;
      if (hasExistingPolygon) {
         setDrawingMode(DrawingMode.REDRAWING);
         setPolygonCoordinates([]); // Clear for fresh drawing
         setHasCompletedRedrawing(false); // Reset redraw completion flag
      } else {
         setDrawingMode(DrawingMode.DRAWING);
         setPolygonCoordinates([]);
      }
   }, [activePolygon, existingPolygon]);

   const handleCancelDrawing = useCallback(() => {
      cancelDrawingRef.current = true;

      if (drawingMode === DrawingMode.DRAWING) {
         if (activePolygon) {
            activePolygon.setMap(null);
         }
         setActivePolygon(null);
         setPolygonCoordinates(cityDetails.polygon || []);
         setDrawingMode(DrawingMode.VIEWING);
         return;
      } else if (drawingMode === DrawingMode.REDRAWING) {
         if (activePolygon) {
            activePolygon.setMap(null);
         }
         setActivePolygon(null);
         setPolygonCoordinates(cityDetails.polygon || []); // Revert to original
         setDrawingMode(DrawingMode.VIEWING);
         setHasCompletedRedrawing(false); // Reset redraw completion flag
         return;
      } else if (drawingMode === DrawingMode.EDITING) {
         setDrawingMode(DrawingMode.VIEWING);
         setPolygonCoordinates(cityDetails.polygon || []);
      }
   }, [drawingMode, activePolygon, cityDetails.polygon]);

   useEffect(() => {
      const handleKeyDown = (event: KeyboardEvent) => {
         if (
            event.key === 'Escape' &&
            (drawingMode === DrawingMode.DRAWING ||
               drawingMode === DrawingMode.EDITING ||
               drawingMode === DrawingMode.REDRAWING)
         ) {
            event.preventDefault();
            // Set cancel flag for drawing/redrawing mode to prevent onPolygonComplete
            if (drawingMode === DrawingMode.DRAWING || drawingMode === DrawingMode.REDRAWING) {
               cancelDrawingRef.current = true;
            }
            handleCancelDrawing();
         }
      };

      document.addEventListener('keydown', handleKeyDown);
      return () => {
         document.removeEventListener('keydown', handleKeyDown);
      };
   }, [drawingMode, handleCancelDrawing]);

   useEffect(() => {
      if (hasAutoZoomed) return;

      if (existingPolygon.length > 0) {
         const firstPoint = existingPolygon[0];
         setMapCenter({
            lat: firstPoint.lat,
            lng: firstPoint.lng,
         });

         const savedZoomLevel = cityDetails.meta?.zoomLevel;
         if (savedZoomLevel && !isNaN(Number(savedZoomLevel))) {
            setMapZoom(Number(savedZoomLevel));
         } else {
            setMapZoom(6);
         }
      }

      setHasAutoZoomed(true);
   }, [existingPolygon, cityDetails.meta?.zoomLevel, hasAutoZoomed]);

   const handleEditPolygon = useCallback(() => {
      setDrawingMode(DrawingMode.EDITING);
   }, []);

   const validatePolygonAndHandleErrors = useCallback(
      ({
         coordinates,
         isRedrawing,
         citiesData,
      }: {
         coordinates: LatLng[];
         isRedrawing: boolean;
         citiesData?: City[];
      }) => {
         // Basic polygon validation
         const validation = validatePolygon(coordinates);
         if (!validation.isValid) {
            toast.error(`Invalid polygon: ${validation.errors.join(', ')}`);
            return false;
         }

         // Check if cities data is available
         if (!citiesData) {
            toast.error('City data is not available. Please wait for data to load.');
            return false;
         }

         // Check intersections with other cities
         if (citiesData) {
            const existingPolygons = citiesData
               .filter(c => c.id !== cityDetails.id && c.polygon && c.polygon.length > 0)
               .map(c => c.polygon!);

            const intersectionCheck = checkPolygonIntersections(coordinates, existingPolygons);

            if (intersectionCheck.hasIntersection) {
               const otherCitiesWithPolygons = citiesData.filter(
                  c => c.id !== cityDetails.id && c.polygon && c.polygon.length > 0
               );
               const intersectingCities = intersectionCheck.intersectingIndexes
                  .map(index => otherCitiesWithPolygons[index]?.name)
                  .filter(Boolean)
                  .join(', ');

               toast.error(
                  `This boundary intersects with existing city areas: ${intersectingCities}. Please adjust the boundary to avoid overlaps.`
               );

               // Always revert to existing polygon on intersection error  
               const currentPolygon = cityDetails.polygon || [];
               setPolygonCoordinates(currentPolygon);
               if (activePolygon) {
                  activePolygon.setMap(null);
               }
               setActivePolygon(null);
               setDrawingMode(DrawingMode.VIEWING);
               return false;
            }
         }

         // Check if the new city boundary contains all existing zones
         const zoneContainmentCheck = checkCityBoundaryContainsZones(coordinates, zonePolygons);
         if (!zoneContainmentCheck.isValid) {
            toast.error(
               zoneContainmentCheck.error || 'City boundary must contain all existing zones'
            );

            // Always revert to existing polygon on zone containment error
            const currentPolygon = cityDetails.polygon || [];
            setPolygonCoordinates(currentPolygon);
            if (activePolygon) {
               activePolygon.setMap(null);
            }
            setActivePolygon(null);
            setDrawingMode(DrawingMode.VIEWING);
            if (isRedrawing) {
               setHasCompletedRedrawing(false);
            }
            return false;
         }

         return true;
      },
      [cityDetails.id, cityDetails.polygon, activePolygon, zonePolygons]
   );

   const handlePolygonComplete = useCallback(
      async (polygon: google.maps.Polygon) => {
         // Check if drawing was canceled (Escape key pressed)
         if (cancelDrawingRef.current) {
            // Reset the cancel flag
            cancelDrawingRef.current = false;
            // Remove the unwanted polygon from the map
            polygon.setMap(null);
            return;
         }

         const path = polygon.getPath();
         const coordinates: LatLng[] = [];

         for (let i = 0; i < path.getLength(); i++) {
            const point = path.getAt(i);
            coordinates.push({
               lat: point.lat(),
               lng: point.lng(),
            });
         }

         if (coordinates.length > 0) {
            coordinates.push(coordinates[0]);
         }

         const originalDrawingMode = drawingModeRef.current;
         const isRedrawing = originalDrawingMode === DrawingMode.REDRAWING;

         /**
          * Validate polygon and handle errors
          * We are only validating this for the drawing mode becuase for
          * REDRAWING mode the validation is done on handlePolygonSave function
          */
         if (
            originalDrawingMode === DrawingMode.DRAWING &&
            !validatePolygonAndHandleErrors({
               coordinates,
               isRedrawing,
               citiesData: allCities?.data,
            })
         ) {
            return;
         }

         // For redrawing mode, store coordinates but keep in redrawing mode for manual save
         if (originalDrawingMode === DrawingMode.REDRAWING) {
            setPolygonCoordinates(coordinates);
            setActivePolygon(polygon);
            setHasCompletedRedrawing(true); // Mark that we've completed a redraw polygon
            // Keep in REDRAWING mode to show save/cancel buttons, but disable further drawing
         } else if (originalDrawingMode === DrawingMode.DRAWING) {
            // For initial drawing, get current zoom from map and auto-save
            setPolygonCoordinates(coordinates);
            setActivePolygon(polygon);
            setDrawingMode(DrawingMode.VIEWING);

            // Get the current zoom level directly from the map to ensure accuracy
            const currentZoom = mapRef.current?.getZoom() || mapZoom;

            updateCityMutation.mutate(
               {
                  id: cityDetails.id,
                  polygon: coordinates,
                  meta: {
                     ...cityDetails.meta,
                     zoomLevel: currentZoom.toString(),
                  },
               },
               {
                  onSuccess: () => {
                     toast.success('City boundary saved successfully!');
                  },
                  onError: error => {
                     console.error('Failed to save city boundary:', error);
                     toast.error('Failed to save city boundary');
                  },
               }
            );
         }
      },
      [
         validatePolygonAndHandleErrors,
         allCities?.data,
         mapZoom,
         updateCityMutation,
         cityDetails.id,
         cityDetails.meta,
      ]
   );

   const handleSavePolygon = useCallback(async () => {
      if (polygonCoordinates.length < 4) {
         toast.error('Please draw a polygon with at least 3 points');
         return;
      }

      // Validate polygon and handle errors
      if (
         !validatePolygonAndHandleErrors({
            coordinates: polygonCoordinates,
            isRedrawing: true,
            citiesData: allCities?.data,
         })
      ) {
         return;
      }

      // Get the current zoom level directly from the map to ensure accuracy
      const currentZoom = mapRef.current?.getZoom() || mapZoom;

      updateCityMutation.mutate(
         {
            id: cityDetails.id,
            polygon: polygonCoordinates,
            meta: {
               ...cityDetails.meta,
               zoomLevel: currentZoom.toString(),
            },
         },
         {
            onSuccess: () => {
               setDrawingMode(DrawingMode.VIEWING);
               setHasCompletedRedrawing(false); // Reset redraw completion flag
               toast.success('City boundary saved successfully!');
            },
         }
      );
   }, [
      polygonCoordinates,
      validatePolygonAndHandleErrors,
      allCities?.data,
      mapZoom,
      updateCityMutation,
      cityDetails.id,
      cityDetails.meta,
   ]);

   const handleZoomChange = useCallback((zoom: number) => {
      setMapZoom(zoom);
   }, []);

   const handleMapLoad = useCallback((map: google.maps.Map) => {
      mapRef.current = map;
   }, []);

   const handleCitySelect = useCallback((place: google.maps.places.PlaceResult) => {
      if (place.geometry?.location) {
         setMapCenter({
            lat: place.geometry.location.lat(),
            lng: place.geometry.location.lng(),
         });
         setMapZoom(12);
      }
   }, []);

   const handleDeletePolygon = useCallback(() => {
      // Check if there are any zones at all (regardless of whether they have polygons/H3 data)
      const totalZones = cityZones?.data?.length || 0;

      if (totalZones > 0) {
         toast.error(
            `Cannot delete city boundary. There are ${totalZones} zone(s) in this city that depend on this city boundary. Please delete all zones first.`
         );
         return;
      }

      setShowDeleteConfirmation(true);
   }, [cityZones?.data?.length]);

   const handleConfirmDelete = useCallback(async () => {
      if (activePolygon) {
         activePolygon.setMap(null);
      }
      setActivePolygon(null);
      setPolygonCoordinates([]);
      setDrawingMode(DrawingMode.VIEWING);
      setShowDeleteConfirmation(false);

      updateCityMutation.mutate(
         {
            id: cityDetails.id,
            polygon: [],
         },
         {
            onSuccess: () => {
               toast.success('City boundary deleted successfully!');
            },
         }
      );
   }, [activePolygon, cityDetails.id, updateCityMutation]);

   const handleCancelDelete = useCallback(() => {
      setShowDeleteConfirmation(false);
   }, []);

   if (allCitiesLoading || cityZonesLoading) {
      return (
         <div className='h-full w-full bg-gray-100 flex items-center justify-center'>
            <div className='text-center'>
               <p className='text-gray-600'>Loading map...</p>
            </div>
         </div>
      );
   }

   return (
      <div className='h-full w-full relative'>
         {/* City Search */}
         <div className='absolute top-[14px] left-4 z-10 bg-white rounded-lg shadow-lg'>
            <CitySearch onCitySelect={handleCitySelect} />
         </div>

         {/* Drawing Controls */}
         <div className='absolute -top-1 -right-2 z-10 bg-white rounded-lg shadow-lg'>
            <DrawingControls
               mode={drawingMode}
               onStartDrawing={handleStartDrawing}
               onCancelDrawing={handleCancelDrawing}
               onEditPolygon={handleEditPolygon}
               onSavePolygon={handleSavePolygon}
               onDeletePolygon={handleDeletePolygon}
               onFullscreen={onFullscreen || (() => {})}
               isSaving={updateCityMutation.isPending}
               hasPolygon={polygonCoordinates.length > 0}
               cityName={cityDetails.name}
               isFullscreen={isFullscreen}
            />
         </div>

         {/* Map Component */}
         <MapWithDrawing
            center={mapCenter}
            zoom={mapZoom}
            drawingMode={drawingMode}
            existingPolygon={polygonCoordinates}
            otherCityPolygons={
               allCities?.data
                  ?.filter(c => c.id !== cityDetails.id && c.polygon && c.polygon.length > 0)
                  .map(c => c.polygon!) || []
            }
            zonePolygonsForCityMode={zonePolygons}
            onPolygonComplete={handlePolygonComplete}
            onPolygonEdit={setPolygonCoordinates}
            onZoomChange={handleZoomChange}
            onMapLoad={handleMapLoad}
            disableDrawing={drawingMode === DrawingMode.REDRAWING && hasCompletedRedrawing}
            cancelDrawingRef={cancelDrawingRef}
            showLabels={true}
            showCityZoneNames={false}
            currentPolygonLabel={cityDetails.name}
            otherCityNames={otherCityNames}
            zoneNamesForCityMode={zoneNames}
         />

         {/* Delete Confirmation Modal */}
         <Dialog open={showDeleteConfirmation} onOpenChange={setShowDeleteConfirmation}>
            <DialogContent>
               <DialogHeader>
                  <DialogTitle>Delete City Boundary</DialogTitle>
                  <DialogDescription>
                     Are you sure you want to delete the boundary for "{cityDetails.name}"? This
                     action cannot be undone.
                  </DialogDescription>
               </DialogHeader>
               <DialogFooter>
                  <Button
                     variant='outline'
                     onClick={handleCancelDelete}
                     disabled={updateCityMutation.isPending}
                  >
                     Cancel
                  </Button>
                  <Button
                     variant='destructive'
                     onClick={handleConfirmDelete}
                     disabled={updateCityMutation.isPending}
                  >
                     {updateCityMutation.isPending ? 'Deleting...' : 'Delete'}
                  </Button>
               </DialogFooter>
            </DialogContent>
         </Dialog>
      </div>
   );
}

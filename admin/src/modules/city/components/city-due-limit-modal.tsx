'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/lib/toast';
import { handleNumberInputChange, getNumberDisplayValue } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { DollarSign } from 'lucide-react';
import React, { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as z from 'zod';
import { useCreateDriverDueLimit, useUpdateDriverDueLimit } from '../api/driver-due-limit-mutations';
import { useGetDriverDueLimit } from '../api/driver-due-limit-queries';

const dueLimitSchema = z.object({
  maxDueLimit: z
    .union([z.number(), z.string()])
    .refine(val => val !== '' && val !== null && val !== undefined, {
      message: 'Due limit is required',
    })
    .transform(val => (typeof val === 'string' ? Number(val) : val))
    .refine(val => !isNaN(val), {
      message: 'Due limit must be a valid number',
    })
    .refine(val => val >= 0, {
      message: 'Due limit must be 0 or greater',
    })
    .refine(val => val <= 1000000, {
      message: 'Due limit cannot exceed 1,000,000',
    }),
});

type DueLimitFormValues = z.infer<typeof dueLimitSchema>;

interface CityDueLimitModalProps {
  isOpen: boolean;
  onClose: () => void;
  cityId: string;
  cityName: string;
}

export const CityDueLimitModal = ({
  isOpen,
  onClose,
  cityId,
  cityName,
}: CityDueLimitModalProps) => {
  const createMutation = useCreateDriverDueLimit();
  const updateMutation = useUpdateDriverDueLimit();
  const dueLimitQuery = useGetDriverDueLimit(cityId);

  // Determine if this is edit mode (config exists) or create mode (404 or no data)
  const isEditMode = dueLimitQuery.isSuccess && dueLimitQuery.data?.data;
  const configExists = !!dueLimitQuery.data?.data;

  const form = useForm<DueLimitFormValues>({
    resolver: zodResolver(dueLimitSchema) as any, // Type assertion needed due to Zod v4 coerce unknown input type
    defaultValues: {
      maxDueLimit: 0,
    },
  });

  const {
    formState: { errors },
    reset,
    control,
    handleSubmit,
    setValue,
  } = form;

  // Pre-populate form when due limit data is loaded
  useEffect(() => {
    if (dueLimitQuery.data?.data) {
      setValue('maxDueLimit', dueLimitQuery.data.data.maxDueLimit);
    } else {
      // Reset form for create mode
      reset({
        maxDueLimit: 0,
      });
    }
  }, [dueLimitQuery.data, setValue, reset]);

  const onSubmit = (data: DueLimitFormValues) => {
    if (isEditMode && configExists) {
      // Update existing configuration
      updateMutation.mutate(
        {
          cityId,
          payload: {
            maxDueLimit: data.maxDueLimit,
          },
        },
        {
          onSuccess: () => {
            toast.success('Driver due limit updated successfully');
            reset();
            onClose();
          },
          onError: (error: any) => {
            toast.error(error?.response?.data?.message || 'Failed to update due limit');
          },
        }
      );
    } else {
      // Create new configuration
      createMutation.mutate(
        {
          cityId,
          maxDueLimit: data.maxDueLimit,
        },
        {
          onSuccess: () => {
            toast.success('Driver due limit created successfully');
            reset();
            onClose();
          },
          onError: (error: any) => {
            toast.error(error?.response?.data?.message || 'Failed to create due limit');
          },
        }
      );
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  const isLoading = dueLimitQuery.isLoading;
  const mutation = isEditMode ? updateMutation : createMutation;

  // Show loading state while fetching existing config
  if (isLoading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className='sm:max-w-md'>
          <DialogHeader>
            <DialogTitle>Loading Configuration</DialogTitle>
          </DialogHeader>
          <div className='flex items-center justify-center py-8'>
            <Spinner className='h-8 w-8' />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <div className='flex items-center gap-3 mb-2'>
            <div className='w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center'>
              <DollarSign className='w-5 h-5 text-blue-600' />
            </div>
            <div>
              <DialogTitle>
                {isEditMode ? 'Update' : 'Set'} Driver Due Limit
              </DialogTitle>
              <DialogDescription>
                Configure maximum due limit for {cityName}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className='space-y-4'>
          <div className='bg-blue-50 rounded-lg p-3 mb-4'>
            <p className='text-sm text-blue-800'>
              <strong>Note:</strong> Drivers will be restricted from going online when their
              due balance exceeds this limit (becomes more negative).
            </p>
          </div>

          {/* Max Due Limit */}
          <div className='space-y-2'>
            <Label htmlFor='maxDueLimit'>Maximum Due Limit *</Label>
            <Controller
              name='maxDueLimit'
              control={control}
              render={({ field }) => (
                <div className='relative'>
                  <span className='absolute left-3 top-1/2 -translate-y-1/2 text-gray-500'>
                    ₹
                  </span>
                  <Input
                    id='maxDueLimit'
                    type='number'
                    step='0.01'
                    placeholder='Enter maximum due limit'
                    disabled={mutation.isPending}
                    className='pl-8'
                    value={getNumberDisplayValue(field.value)}
                    onChange={(e) =>
                      handleNumberInputChange(e.target.value, field.onChange)
                    }
                  />
                </div>
              )}
            />
            <ErrorMessage error={errors.maxDueLimit?.message} />
            <p className='text-xs text-gray-500'>
              Example: ₹5,000 means drivers can owe up to ₹5,000 before being restricted
            </p>
          </div>

          {/* Form Actions */}
          <div className='flex justify-end gap-3 pt-4'>
            <Button
              type='button'
              variant='outline'
              onClick={handleClose}
              disabled={mutation.isPending}
            >
              Cancel
            </Button>
            <Button type='submit' disabled={mutation.isPending}>
              {mutation.isPending ? (
                <>
                  <Spinner className='h-4 w-4 mr-2' />
                  {isEditMode ? 'Updating...' : 'Creating...'}
                </>
              ) : isEditMode ? (
                'Update Limit'
              ) : (
                'Set Limit'
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

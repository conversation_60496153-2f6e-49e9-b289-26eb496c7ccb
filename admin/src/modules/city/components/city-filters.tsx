'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { Spinner } from '@/components/ui/spinner';
import { STATES } from '@/lib/location-data';
import { Search, X, MapPin } from 'lucide-react';
import { useEffect, useState, useRef } from 'react';

export interface CityFiltersProps {
   onSearchChange: (search: string) => void;
   onStateChange: (state: string | undefined) => void;
   onStatusChange: (status: string | undefined) => void;
   search: string;
   state: string | undefined;
   status: string | undefined;
}

export function CityFilters({
   onSearchChange,
   onStateChange,
   onStatusChange,
   search,
   state,
   status,
   isLoading,
}: CityFiltersProps & { isLoading?: boolean }) {
   const [localSearch, setLocalSearch] = useState(search);
   const searchTimeoutRef = useRef<NodeJS.Timeout>(null);

   // Debounced search
   useEffect(() => {
      if (searchTimeoutRef.current) {
         clearTimeout(searchTimeoutRef.current);
      }

      searchTimeoutRef.current = setTimeout(() => {
         onSearchChange(localSearch);
      }, 300);

      return () => {
         if (searchTimeoutRef.current) {
            clearTimeout(searchTimeoutRef.current);
         }
      };
   }, [localSearch, onSearchChange]);

   // Update local search when prop changes (for clear filters)
   useEffect(() => {
      setLocalSearch(search);
   }, [search]);

   const hasActiveFilters = !!search || !!state || !!status;

   return (
      <div className='mb-6'>
         {/* All Filters in One Row */}
         <div className='flex flex-wrap gap-6 items-center'>
            {/* Search Bar */}
            <div className='relative flex-1 min-w-[300px]'>
               <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4' />
               <Input
                  placeholder='Search cities by name...'
                  value={localSearch}
                  onChange={e => setLocalSearch(e.target.value)}
                  className='pl-10 pr-10 h-10'
               />
               {localSearch && (
                  <button
                     onClick={() => setLocalSearch('')}
                     className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'
                  >
                     <X className='w-4 h-4' />
                  </button>
               )}
               {isLoading && (
                  <div className='absolute right-3 top-1/2 transform -translate-y-1/2'>
                     <Spinner />
                  </div>
               )}
            </div>

            {/* Filter Controls */}
            <div className='flex flex-wrap gap-3 items-center'>
               {/* State Filter */}
               <div className='flex items-center min-w-0'>
                  <Select
                     value={state || 'all'}
                     onValueChange={value => onStateChange(value === 'all' ? undefined : value)}
                  >
                     <SelectTrigger className='w-[180px] h-9'>
                        <MapPin className='w-4 h-4 text-gray-500 mr-1' />
                        <SelectValue placeholder='All States' />
                     </SelectTrigger>
                     <SelectContent>
                        <SelectItem value='all'>All States</SelectItem>
                        {STATES.india.map((state) => (
                           <SelectItem key={state.value} value={state.label}>
                              {state.label}
                           </SelectItem>
                        ))}
                     </SelectContent>
                  </Select>
               </div>

               {/* Status Filter */}
               <div className='flex items-center gap-2 min-w-0'>
                  <Select value={status || 'all'} onValueChange={value => onStatusChange(value === 'all' ? undefined : value)}>
                     <SelectTrigger className='w-[140px] h-9'>
                        <SelectValue placeholder='All Status' />
                     </SelectTrigger>
                     <SelectContent>
                        <SelectItem value='all'>All Status</SelectItem>
                        <SelectItem value='active'>Active</SelectItem>
                        <SelectItem value='inactive'>Inactive</SelectItem>
                     </SelectContent>
                  </Select>
               </div>

               {/* Clear Filters Button */}
               {hasActiveFilters && (
                  <Button
                     variant='outline'
                     size='sm'
                     onClick={() => {
                        setLocalSearch('');
                        onStateChange(undefined);
                        onStatusChange(undefined);
                     }}
                     className='h-9 px-3'
                  >
                     <X className='w-4 h-4 mr-1' />
                     Clear Filters
                  </Button>
               )}
            </div>
         </div>
      </div>
   );
}

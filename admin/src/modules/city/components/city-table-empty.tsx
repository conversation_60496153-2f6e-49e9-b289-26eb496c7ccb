'use client';

import { But<PERSON> } from '@/components/ui/button';
import { MapPin, Plus } from 'lucide-react';

interface CityTableEmptyProps {
  onCreateClick?: () => void;
}

export function CityTableEmpty({ onCreateClick }: CityTableEmptyProps) {
  return (
    <div className="flex flex-col items-center justify-center py-16 px-4">
      <div className="flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
        <MapPin className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">No cities found</h3>
      <p className="text-gray-500 text-center mb-6 max-w-md">
        Get started by adding your first city to the platform. You can manage city information, 
        set their status, and configure service areas.
      </p>
      {onCreateClick && (
        <Button onClick={onCreateClick} className="flex items-center gap-2">
          <Plus className="w-4 h-4" />
          Add First City
        </Button>
      )}
    </div>
  );
}
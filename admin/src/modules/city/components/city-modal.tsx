'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
   DialogTrigger,
} from '@/components/ui/dialog';
import { toast } from '@/lib/toast';
import { COUNTRIES, STATES, CountryValue } from '@/lib/location-data';
import { handleNumberInputChange, getNumberDisplayValue } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { Plus } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useQueryClient } from '@tanstack/react-query';
import * as z from 'zod';
import { useCreateCity, useUpdateCity } from '../api/mutations';
import { useGetCity } from '../api/queries';
import { useCreateDriverDueLimit } from '../api/driver-due-limit-mutations';
import { useRouter } from 'next/navigation';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';

const citySchema = z.object({
   name: z
      .string()
      .min(1, 'City name is required')
      .min(2, 'City name must be at least 2 characters')
      .max(100, 'City name must not exceed 100 characters')
      .regex(
         /^[a-zA-Z\s.'-]+$/,
         'City name can only contain letters, spaces, dots, apostrophes, and hyphens'
      ),
   country: z.string().min(1, 'Country is required'),
   state: z.string().min(1, 'State is required'),
   status: z.enum(['active', 'inactive']).optional(),
   dueLimit: z
      .union([z.number(), z.string()])
      .refine(val => val !== '' && val !== null && val !== undefined, {
         message: 'Due limit is required',
      })
      .transform(val => (typeof val === 'string' ? Number(val) : val))
      .refine(val => !isNaN(val), {
         message: 'Due limit must be a valid number',
      })
      .refine(val => val >= 0, {
         message: 'Due limit must be 0 or greater',
      }),
});

type CityFormValues = z.infer<typeof citySchema>;

interface CityModalProps {
   cityId?: string | null;
   isOpen?: boolean;
   onClose?: () => void;
   onCityUpdated?: () => void;
   mode?: 'create' | 'edit';
}

export const CityModal = ({
   cityId,
   isOpen,
   onClose,
   onCityUpdated,
   mode = 'create',
}: CityModalProps) => {
   const [internalOpen, setInternalOpen] = useState(false);
   const [selectedCountry, setSelectedCountry] = useState<CountryValue>('india');
   const createCityMutation = useCreateCity();
   const updateCityMutation = useUpdateCity();
   const createDueLimitMutation = useCreateDriverDueLimit();
   const cityQuery = useGetCity(cityId || null);
   const queryClient = useQueryClient();
   const router = useRouter();

   // Determine if this is edit mode
   const isEditMode = mode === 'edit' || !!cityId;

   // Use external open state if provided, otherwise use internal state
   const modalOpen = isOpen !== undefined ? isOpen : internalOpen;
   const setModalOpen = onClose ? (open: boolean) => !open && onClose() : setInternalOpen;

   const form = useForm<CityFormValues>({
      resolver: zodResolver(citySchema) as any, // Type assertion needed due to Zod v4 coerce unknown input type
      defaultValues: {
         name: '',
         country: 'india',
         state: '',
         status: 'active',
         dueLimit: 0,
      },
   });

   const {
      formState: { errors },
      reset,
      control,
      handleSubmit,
      setValue,
   } = form;

   // Pre-populate form when city data is loaded for edit mode
   useEffect(() => {
      if (isEditMode && cityQuery.data?.data) {
         const city = cityQuery.data.data;
         setValue('name', city.name);
         setValue('country', city.country || 'india');
         setValue('state', city.state || '');
         setSelectedCountry((city.country as CountryValue) || 'india');
      } else if (!isEditMode) {
         // Reset form for create mode
         reset({
            name: '',
            country: 'india',
            state: 'kerala',
            status: 'active',
            dueLimit: 0,
         });
         setSelectedCountry('india');
      }
   }, [isEditMode, cityQuery.data, setValue, reset]);

   const onSubmit = (data: CityFormValues) => {
      if (isEditMode && cityId) {
         // Update existing city
         updateCityMutation.mutate(
            {
               id: cityId,
               name: data.name,
               country: data.country,
               state: data.state,
            },
            {
               onSuccess: () => {
                  toast.success('City updated successfully');
                  queryClient.invalidateQueries({ queryKey: ['city', cityId] });
                  queryClient.invalidateQueries({ queryKey: ['cities'] });
                  reset();
                  setModalOpen(false);
                  onCityUpdated?.();
               },
            }
         );
      } else {
         // Create new city
         createCityMutation.mutate(
            {
               name: data.name,
               country: data.country,
               state: data.state,
               status: data.status || 'active',
            },
            {
               onSuccess: response => {
                  if (response?.data?.id) {
                     const cityId = response.data.id;

                     // Create due limit configuration (now required)
                     createDueLimitMutation.mutate(
                        {
                           cityId,
                           maxDueLimit: data.dueLimit,
                        },
                        {
                           onSuccess: () => {
                              router.push(`/dashboard/cities/${cityId}`);
                              toast.success('City created successfully');
                              queryClient.invalidateQueries({ queryKey: ['cities'] });
                              reset();
                              setModalOpen(false);
                           },
                           onError: () => {
                              // City was created but due limit configuration failed
                              router.push(`/dashboard/cities/${cityId}`);
                              toast.error('City created but failed to configure due limit');
                              queryClient.invalidateQueries({ queryKey: ['cities'] });
                              reset();
                              setModalOpen(false);
                           },
                        }
                     );
                  } else {
                     toast.error('City id missing, please contact dev team');
                  }
               },
            }
         );
      }
   };

   const handleClose = () => {
      reset();
      setModalOpen(false);
   };

   const isLoading = isEditMode ? cityQuery.isLoading : false;
   const isError = isEditMode ? cityQuery.isError || !cityQuery.data?.data : false;
   const mutation = isEditMode ? updateCityMutation : createCityMutation;
   const { withPermission } = useRoleBasedAccess();

   const TriggerButton = () => (
      <Button
         onClick={() => withPermission(RBAC_PERMISSIONS.CITY.CREATE, () => setModalOpen(true))}
         className='flex items-center gap-2'
         variant='outline'
      >
         <Plus className='w-4 h-4' />
         Add City
      </Button>
   );

   // Show loading state for edit mode
   if (isLoading) {
      return (
         <Dialog open={modalOpen} onOpenChange={setModalOpen}>
            <DialogContent className='sm:max-w-md'>
               <DialogHeader>
                  <DialogTitle>Loading City Data</DialogTitle>
               </DialogHeader>
               <div className='flex items-center justify-center py-8'>
                  <Spinner className='h-8 w-8' />
               </div>
            </DialogContent>
         </Dialog>
      );
   }

   // Show error state for edit mode
   if (isError) {
      return (
         <Dialog open={modalOpen} onOpenChange={setModalOpen}>
            <DialogContent className='sm:max-w-md'>
               <DialogHeader>
                  <DialogTitle>Error</DialogTitle>
                  <DialogDescription>Failed to load city data. Please try again.</DialogDescription>
               </DialogHeader>
               <div className='flex justify-end pt-4'>
                  <Button variant='outline' onClick={handleClose}>
                     Close
                  </Button>
               </div>
            </DialogContent>
         </Dialog>
      );
   }

   return (
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
         {!isOpen && !isEditMode && (
            <DialogTrigger asChild>
               <TriggerButton />
            </DialogTrigger>
         )}

         <DialogContent className='sm:max-w-md'>
            <DialogHeader>
               <DialogTitle>{isEditMode ? 'Edit City' : 'Create New City'}</DialogTitle>
               <DialogDescription>
                  {isEditMode
                     ? 'Update the city information below.'
                     : 'Add a new city to the platform. Fill in the details below.'}
               </DialogDescription>
            </DialogHeader>

            <form onSubmit={handleSubmit(onSubmit)} className='space-y-4'>
               {/* City Name */}
               <div className='space-y-2'>
                  <Label htmlFor='name'>City Name *</Label>
                  <Controller
                     name='name'
                     control={control}
                     render={({ field }) => (
                        <Input
                           {...field}
                           id='name'
                           placeholder='Enter city name'
                           disabled={mutation.isPending}
                        />
                     )}
                  />
                  <ErrorMessage error={errors.name?.message} />
               </div>

               {/* Country */}
               <div className='space-y-2'>
                  <Label htmlFor='country'>Country *</Label>
                  <Controller
                     name='country'
                     control={control}
                     render={({ field }) => (
                        <Select
                           value={field.value}
                           onValueChange={value => {
                              field.onChange(value);
                              setSelectedCountry(value as CountryValue);
                              setValue('state', ''); // Reset state when country changes
                           }}
                           disabled={mutation.isPending}
                        >
                           <SelectTrigger className='w-full'>
                              <SelectValue placeholder='Select country' />
                           </SelectTrigger>
                           <SelectContent>
                              {COUNTRIES.map(country => (
                                 <SelectItem key={country.value} value={country.value}>
                                    {country.label}
                                 </SelectItem>
                              ))}
                           </SelectContent>
                        </Select>
                     )}
                  />
                  <ErrorMessage error={errors.country?.message} />
               </div>

               {/* State */}
               <div className='space-y-2'>
                  <Label htmlFor='state'>State *</Label>
                  <Controller
                     name='state'
                     control={control}
                     render={({ field }) => (
                        <Select
                           value={field.value}
                           onValueChange={field.onChange}
                           disabled={mutation.isPending}
                        >
                           <SelectTrigger className='w-full'>
                              <SelectValue placeholder='Select state' />
                           </SelectTrigger>
                           <SelectContent>
                              {STATES[selectedCountry]?.map(state => (
                                 <SelectItem key={state.value} value={state.value}>
                                    {state.label}
                                 </SelectItem>
                              ))}
                           </SelectContent>
                        </Select>
                     )}
                  />
                  <ErrorMessage error={errors.state?.message} />
               </div>

               {/* Due Limit - Only show for create mode */}
               {!isEditMode && (
                  <div className='space-y-2'>
                     <Label htmlFor='dueLimit'>Driver Due Limit *</Label>
                     <Controller
                        name='dueLimit'
                        control={control}
                        render={({ field }) => (
                           <Input
                              id='dueLimit'
                              type='number'
                              step='0.01'
                              placeholder='Enter maximum due limit'
                              disabled={mutation.isPending}
                              value={getNumberDisplayValue(field.value)}
                              onChange={e => handleNumberInputChange(e.target.value, field.onChange)}
                           />
                        )}
                     />
                     <ErrorMessage error={errors.dueLimit?.message} />
                     <p className='text-xs text-muted-foreground'>
                        Maximum amount drivers can owe before being restricted
                     </p>
                  </div>
               )}

               {/* Status - Only show for create mode */}
               {!isEditMode && (
                  <div className='space-y-2'>
                     <Label htmlFor='status'>Status *</Label>
                     <Controller
                        name='status'
                        control={control}
                        render={({ field }) => (
                           <Select
                              value={field.value}
                              onValueChange={field.onChange}
                              disabled={mutation.isPending}
                           >
                              <SelectTrigger className='w-full'>
                                 <SelectValue placeholder='Select status' />
                              </SelectTrigger>
                              <SelectContent>
                                 <SelectItem value='active'>Active</SelectItem>
                                 <SelectItem value='inactive'>Inactive</SelectItem>
                              </SelectContent>
                           </Select>
                        )}
                     />
                     <ErrorMessage error={errors.status?.message} />
                  </div>
               )}

               {/* Form Actions */}
               <div className='flex justify-end gap-3 pt-4'>
                  <Button
                     type='button'
                     variant='outline'
                     onClick={handleClose}
                     disabled={mutation.isPending}
                  >
                     Cancel
                  </Button>
                  <Button type='submit' disabled={mutation.isPending}>
                     {mutation.isPending ? (
                        <>
                           <Spinner className='h-4 w-4 mr-2' />
                           {isEditMode ? 'Updating...' : 'Creating...'}
                        </>
                     ) : isEditMode ? (
                        'Update City'
                     ) : (
                        'Create City'
                     )}
                  </Button>
               </div>
            </form>
         </DialogContent>
      </Dialog>
   );
};

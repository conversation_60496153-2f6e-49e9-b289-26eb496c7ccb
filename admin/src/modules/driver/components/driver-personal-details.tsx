'use client';

import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Driver } from '../types/driver';
import { formatDateWithMonth } from '@/lib/date-utils';
import { EmailVerificationModal } from './email-verification-modal';

interface DriverPersonalDetailsProps {
   driver: Driver;
   onRefresh?: () => void;
}

export function DriverPersonalDetails({ driver, onRefresh }: DriverPersonalDetailsProps) {
   const [isEmailVerificationModalOpen, setIsEmailVerificationModalOpen] = useState(false);

   const handleEmailVerificationSuccess = () => {
      onRefresh?.();
   };

   return (
      <div className='space-y-6'>
         {/* Main 2-column grid for basic and contact info */}
         <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            {/* Left Column - Basic Information */}
            <div className='space-y-4'>
               <h3 className='text-lg font-semibold text-gray-900'>Basic Information</h3>
               <div className='space-y-3'>
                  <div className='bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 min-h-14 flex items-center justify-between gap-2'>
                     <span className='text-sm font-medium text-gray-600'>Full Name</span>
                     <span className='text-sm text-gray-900'>
                        {`${driver.firstName || ''} ${driver.lastName || ''}`.trim() ||
                           'Not provided'}
                     </span>
                  </div>
                  <div className='bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 min-h-14 flex items-center justify-between gap-2'>
                     <span className='text-sm font-medium text-gray-600'>Gender</span>
                     <span className='text-sm text-gray-900'>
                        {driver.gender
                           ? driver.gender.charAt(0) + driver.gender.slice(1).toLowerCase()
                           : 'Not provided'}
                     </span>
                  </div>
                  <div className='bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 min-h-14 flex items-center justify-between gap-2'>
                     <span className='text-sm font-medium text-gray-600'>Date of Birth</span>
                     <span className='text-sm text-gray-900'>
                        {formatDateWithMonth(driver.dob)}
                     </span>
                  </div>
               </div>
            </div>

            {/* Right Column - Contact Information */}
            <div className='space-y-4'>
               <h3 className='text-lg font-semibold text-gray-900'>Contact Information</h3>
               <div className='space-y-3'>
                  <div className='bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 min-h-14 flex items-center justify-between gap-2'>
                     <span className='text-sm font-medium text-gray-600'>Phone Number</span>
                     <div className='flex items-center gap-2'>
                        <span className='text-sm text-gray-900'>{driver.phoneNumber}</span>
                        <Badge
                           className={`text-xs ${
                              driver.phoneVerified
                                 ? 'bg-green-100 text-green-700'
                                 : 'bg-gray-100 text-gray-700'
                           }`}
                        >
                           {driver.phoneVerified ? 'Verified' : 'Unverified'}
                        </Badge>
                     </div>
                  </div>

                  <div className='bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 min-h-14 flex items-center justify-between gap-2'>
                     <span className='text-sm font-medium text-gray-600 flex-shrink-0'>Email Address</span>
                     <div className='flex items-center gap-2 min-w-0'>
                        <span className='text-sm text-gray-900 truncate'>
                           {driver.email || 'Not provided'}
                        </span>
                        {driver.email && (
                           <>
                              <Badge
                                 className={`text-xs flex-shrink-0 ${
                                    driver.emailVerified
                                       ? 'bg-green-100 text-green-700'
                                       : 'bg-gray-100 text-gray-700'
                                 }`}
                              >
                                 {driver.emailVerified ? 'Verified' : 'Unverified'}
                              </Badge>
                              {!driver.emailVerified && (
                                 <Button
                                    size='sm'
                                    variant='outline'
                                    onClick={() => setIsEmailVerificationModalOpen(true)}
                                    className='h-7 px-3 text-xs flex-shrink-0'
                                 >
                                    Verify
                                 </Button>
                              )}
                           </>
                        )}
                     </div>
                  </div>
                  <div className='bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 min-h-14 flex items-center justify-between gap-2'>
                     <span className='text-sm font-medium text-gray-600'>City</span>
                     <span className='text-sm text-gray-900'>
                        {driver.cityName || 'Not specified'}
                     </span>
                  </div>
               </div>
            </div>
         </div>

         {/* Email Verification Modal */}
         {driver.email && !driver.emailVerified && (
            <EmailVerificationModal
               isOpen={isEmailVerificationModalOpen}
               onClose={() => setIsEmailVerificationModalOpen(false)}
               onSuccess={handleEmailVerificationSuccess}
               driverEmail={driver.email}
               userProfileId={driver.id}
               driverId={driver.id}
            />
         )}
      </div>
   );
}

'use client';

import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { useState } from 'react';
import { useDeleteLanguage } from '../api/mutations';
import { Language, ListLanguageResponse } from '../types/language';
import { DeleteLanguageDialog } from './delete-language-dialog';
import { LanguageModal } from './language-modal';
import { LanguageTableEmpty } from './language-table-empty';
import { LanguageTableLoading } from './language-table-loading';
import { CustomPagination } from '@/components/pagination';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import {
   USE_ROLE_BASED_ACCESS,
   useRoleBasedAccess,
} from '@/role-based-access/use-role-based-access';

const getColumns = ({
   deleteLanguageMutation,
   handleDeleteClick,
   handleEditClick,
   languageToDelete,
   withPermission,
}: {
   handleDeleteClick: (id: string) => void;
   handleEditClick: (id: string) => void;
   deleteLanguageMutation: any;
   languageToDelete: string | null;
   withPermission: USE_ROLE_BASED_ACCESS['withPermission'];
}): ColumnDef<Language>[] => [
   {
      accessorKey: 'code',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Code</div>,
      cell: ({ row }) => {
         const language = row.original as Language;
         return (
            <div className='text-left'>
               <div className='font-mono text-sm font-semibold'>{language.code}</div>
            </div>
         );
      },
      size: 100,
   },
   {
      accessorKey: 'name',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Name</div>,
      cell: ({ row }) => {
         const language = row.original as Language;
         return (
            <div className='text-left'>
               <div className='text-sm'>{language.name}</div>
            </div>
         );
      },
      size: 200,
   },
   {
      accessorKey: 'nameInNative',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Native Name</div>
      ),
      cell: ({ row }) => {
         const language = row.original as Language;
         return (
            <div className='text-left'>
               <div className='text-sm'>{language.nameInNative}</div>
            </div>
         );
      },
      size: 200,
   },
   {
      id: 'actions',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const language = row.original as Language;
         return (
            <div className='flex justify-center gap-1'>
               <button
                  className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() =>
                     withPermission(RBAC_PERMISSIONS.LANGUAGE.EDIT, () =>
                        handleEditClick(language.id)
                     )
                  }
               >
                  Edit
               </button>
               <button
                  className='text-sm font-medium text-gray-600 hover:text-red-600 border border-gray-300 hover:border-red-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() => handleDeleteClick(language.id)}
                  disabled={deleteLanguageMutation.isPending && languageToDelete === language.id}
                  style={{ display: 'none' }}
               >
                  Delete
               </button>
            </div>
         );
      },
      size: 150,
   },
];

interface LanguageTableProps {
   data: ListLanguageResponse | undefined;
   isLoading: boolean;
   currentPage: number;
   onPageChange: (page: number) => void;
}

export function LanguageTable({ data, isLoading, currentPage, onPageChange }: LanguageTableProps) {
   const [languageToDelete, setLanguageToDelete] = useState<string | null>(null);
   const [languageToEdit, setLanguageToEdit] = useState<string | null>(null);
   const deleteLanguageMutation = useDeleteLanguage();
   const queryClient = useQueryClient();
   const { withPermission } = useRoleBasedAccess();

   const handleDeleteClick = (id: string) => {
      setLanguageToDelete(id);
   };

   const handleEditClick = (id: string) => {
      setLanguageToEdit(id);
   };

   const handleDeleteConfirm = () => {
      if (!languageToDelete) return;

      deleteLanguageMutation.mutate(languageToDelete, {
         onSuccess: () => {
            toast.success('Language deleted successfully');
            queryClient.invalidateQueries({ queryKey: ['languages'] });
         },
         onSettled: () => {
            setLanguageToDelete(null);
         },
      });
   };

   const columns = getColumns({
      deleteLanguageMutation,
      handleDeleteClick,
      handleEditClick,
      languageToDelete,
      withPermission,
   });

   const table = useReactTable({
      data: data?.data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   if (isLoading) {
      return <LanguageTableLoading />;
   }

   if (!data?.data?.length) {
      return <LanguageTableEmpty />;
   }

   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                         header.column.columnDef.header,
                                         header.getContext()
                                      )}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td key={cell.id} className='px-4 py-3 align-middle'>
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>

         {data && data.meta && data.meta.totalPages > 1 && (
            <CustomPagination
               currentPage={currentPage}
               totalPages={data.meta.totalPages}
               onPageChange={onPageChange}
               hasNext={data.meta.hasNextPage}
               hasPrev={data.meta.hasPrevPage}
            />
         )}

         <DeleteLanguageDialog
            isOpen={!!languageToDelete}
            onClose={() => setLanguageToDelete(null)}
            onConfirm={handleDeleteConfirm}
            isLoading={deleteLanguageMutation.isPending}
         />

         <LanguageModal
            mode='edit'
            languageId={languageToEdit}
            isOpen={!!languageToEdit}
            onClose={() => setLanguageToEdit(null)}
         />
      </div>
   );
}

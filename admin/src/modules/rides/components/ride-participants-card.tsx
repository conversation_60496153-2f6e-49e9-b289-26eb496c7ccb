import { Card } from '@/components/ui/card';
import { Phone, Mail, User } from 'lucide-react';
import { UserProfile, Product, DriverVehicle } from '../types/ride';

interface RideParticipantsCardProps {
   rider: UserProfile;
   driver?: UserProfile | null;
   product: Product;
   driverVehicle?: DriverVehicle | null;
   bookFor?: 'me' | 'other';
   createdByUser?: UserProfile | null;
}

export function RideParticipantsCard({
   rider,
   driver,
   product,
   driverVehicle,
   bookFor,
   createdByUser,
}: RideParticipantsCardProps) {
   return (
      <Card className='p-4 rounded-sm'>
         <h3 className='text-base font-semibold text-gray-900 mb-2'>Participants</h3>

         {/* Rider & Driver in same row */}
         <div className='grid grid-cols-1 md:grid-cols-2 gap-6 mb-0'>
            {/* Rider */}
            <div>
               <div className='text-xs font-semibold text-blue-600 uppercase mb-2'>Rider</div>
               <div className='text-sm space-y-1.5'>
                  <div className='flex items-center gap-2'>
                     <span className='text-gray-900 font-medium'>
                        {rider.firstName} {rider.lastName}
                     </span>
                     {rider.averageRating && (
                        <span className='text-xs text-gray-600'>
                           ⭐ {rider.averageRating.toFixed(1)}
                        </span>
                     )}
                  </div>
                  {rider?.phoneNumber && (
                     <div className='flex items-center gap-1.5 text-gray-600'>
                        <Phone className='w-3 h-3' />
                        <span>{rider.phoneNumber}</span>
                     </div>
                  )}
                  {rider?.email && (
                     <div className='flex items-center gap-1.5 text-gray-600 break-all'>
                        <Mail className='w-3 h-3 flex-shrink-0' />
                        <span className='text-xs'>{rider.email}</span>
                     </div>
                  )}
               </div>
            </div>

            {/* Driver */}
            {driver && (
               <div>
                  <div className='text-xs font-semibold text-green-600 uppercase mb-2'>Driver</div>
                  <div className='text-sm space-y-1.5'>
                     <div className='flex items-center gap-2'>
                        <span className='text-gray-900 font-medium'>
                           {driver.firstName} {driver.lastName}
                        </span>
                        {driver.averageRating && (
                           <span className='text-xs text-gray-600'>
                              ⭐ {driver.averageRating.toFixed(1)}
                           </span>
                        )}
                     </div>
                     {driver?.phoneNumber && (
                        <div className='flex items-center gap-1.5 text-gray-600'>
                           <Phone className='w-3 h-3' />
                           <span>{driver.phoneNumber}</span>
                        </div>
                     )}
                     {driver?.email && (
                        <div className='flex items-center gap-1.5 text-gray-600 break-all'>
                           <Mail className='w-3 h-3 flex-shrink-0' />
                           <span className='text-xs'>{driver.email}</span>
                        </div>
                     )}
                  </div>
               </div>
            )}
         </div>

         {/* Product & Vehicle + Booking Info - Two Column Layout */}
         <div className='pt-3 border-t border-gray-200'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
               {/* Column 1: Product & Vehicle */}
               <div>
                  <div className='text-xs font-semibold text-purple-600 uppercase mb-2'>
                     Product & Vehicle
                  </div>
                  <div className='text-sm space-y-1.5'>
                     <div>
                        <div className='text-gray-500 text-xs mb-0.5'>Product</div>
                        <div className='text-gray-900 font-medium'>{product.name}</div>
                     </div>
                     {driverVehicle && (
                        <>
                           <div>
                              <div className='text-gray-500 text-xs mb-0.5'>Vehicle</div>
                              <div className='text-gray-900 font-bold tracking-wider'>
                                 {driverVehicle.vehicleNumber}
                              </div>
                           </div>
                           <div>
                              <div className='text-gray-500 text-xs mb-0.5'>Type</div>
                              <div className='text-gray-900 font-medium'>
                                 {driverVehicle.vehicleType.name}
                              </div>
                           </div>
                        </>
                     )}
                  </div>
               </div>

               {/* Column 2: Booking Information */}
               <div>
                  <div className='text-xs font-semibold text-orange-600 uppercase mb-2'>
                     Booking Info
                  </div>
                  <div className='text-sm space-y-1.5'>
                     <div>
                        <div className='text-gray-500 text-xs mb-0.5'>Booking Type</div>
                        <div className='text-gray-900 font-medium'>
                           {bookFor === 'me' ? 'Self Booked' : 'Third-Party Booking'}
                        </div>
                     </div>
                     {bookFor === 'other' && createdByUser && (
                        <>
                           <div>
                              <div className='text-gray-500 text-xs mb-0.5'>Booked By</div>
                              <div className='flex items-center gap-1.5'>
                                 <User className='w-3 h-3 text-gray-600' />
                                 <span className='text-gray-900 font-medium'>
                                    {createdByUser.firstName} {createdByUser.lastName}
                                 </span>
                              </div>
                           </div>
                           {createdByUser.phoneNumber && (
                              <div>
                                 <div className='text-gray-500 text-xs mb-0.5'>Contact</div>
                                 <div className='flex items-center gap-1.5 text-gray-600'>
                                    <Phone className='w-3 h-3' />
                                    <span>{createdByUser.phoneNumber}</span>
                                 </div>
                              </div>
                           )}
                        </>
                     )}
                  </div>
               </div>
            </div>
         </div>
      </Card>
   );
}

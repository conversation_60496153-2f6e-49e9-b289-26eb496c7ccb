export function RideTableLoading() {
   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full'>
                  <thead>
                     <tr className='border-b bg-gray-50'>
                        <th className='h-11 px-4 text-left align-middle'>
                           <div className='h-4 w-20 bg-gray-200 rounded animate-pulse' />
                        </th>
                        <th className='h-11 px-4 text-left align-middle'>
                           <div className='h-4 w-20 bg-gray-200 rounded animate-pulse' />
                        </th>
                        <th className='h-11 px-4 text-left align-middle'>
                           <div className='h-4 w-20 bg-gray-200 rounded animate-pulse' />
                        </th>
                        <th className='h-11 px-4 text-left align-middle'>
                           <div className='h-4 w-20 bg-gray-200 rounded animate-pulse' />
                        </th>
                        <th className='h-11 px-4 text-left align-middle'>
                           <div className='h-4 w-20 bg-gray-200 rounded animate-pulse' />
                        </th>
                        <th className='h-11 px-4 text-left align-middle'>
                           <div className='h-4 w-20 bg-gray-200 rounded animate-pulse' />
                        </th>
                        <th className='h-11 px-4 text-center align-middle'>
                           <div className='h-4 w-20 bg-gray-200 rounded animate-pulse mx-auto' />
                        </th>
                     </tr>
                  </thead>
                  <tbody>
                     {[...Array(5)].map((_, i) => (
                        <tr key={i} className='border-b'>
                           <td className='px-4 py-3'>
                              <div className='h-4 w-32 bg-gray-200 rounded animate-pulse' />
                           </td>
                           <td className='px-4 py-3'>
                              <div className='h-4 w-24 bg-gray-200 rounded animate-pulse' />
                           </td>
                           <td className='px-4 py-3'>
                              <div className='h-4 w-24 bg-gray-200 rounded animate-pulse' />
                           </td>
                           <td className='px-4 py-3'>
                              <div className='h-4 w-28 bg-gray-200 rounded animate-pulse' />
                           </td>
                           <td className='px-4 py-3'>
                              <div className='h-4 w-20 bg-gray-200 rounded animate-pulse' />
                           </td>
                           <td className='px-4 py-3'>
                              <div className='h-4 w-32 bg-gray-200 rounded animate-pulse' />
                           </td>
                           <td className='px-4 py-3'>
                              <div className='flex justify-center'>
                                 <div className='h-8 w-24 bg-gray-200 rounded animate-pulse' />
                              </div>
                           </td>
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>
      </div>
   );
}

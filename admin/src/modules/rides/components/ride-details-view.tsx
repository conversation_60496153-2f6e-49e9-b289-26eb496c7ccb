'use client';

import { RideDetails } from '../types/ride';
import { RideOverviewCard } from './ride-overview-card';
import { RideTripDetailsCard } from './ride-trip-details-card';
import { RideLifecycleCard } from './ride-lifecycle-card';
import { RideParticipantsCard } from './ride-participants-card';
import { RidePricingCard } from './ride-pricing-card';
import { RideRouteMapCard } from './ride-route-map-card';
import { RideReviewCard } from './ride-review-card';

interface RideDetailsViewProps {
   rideDetails: RideDetails;
}

export function RideDetailsView({ rideDetails }: RideDetailsViewProps) {
   return (
      <div className='space-y-4'>
         {/* Row 1: Ride Overview + Trip Details + Ride Lifecycle */}
         <div className='grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4'>
            <RideOverviewCard
               status={rideDetails.status}
               duration={rideDetails.duration}
               actualDuration={rideDetails.actualDuration}
               distance={rideDetails.distance}
               verificationCode={rideDetails.verificationCode}
            />

            <RideTripDetailsCard
               pickupLocation={rideDetails.pickupLocation}
               destinationLocation={rideDetails.destinationLocation}
               stops={rideDetails.stops}
            />

            <RideLifecycleCard rideLifecycles={rideDetails.rideLifecycles} />
         </div>

         {/* Row 2: Participants + Pricing */}
         <div className='grid grid-cols-1 lg:grid-cols-2 gap-4'>
            <RideParticipantsCard
               rider={rideDetails.rider}
               driver={rideDetails.driver}
               product={rideDetails.product}
               driverVehicle={rideDetails.driverVehicle}
               bookFor={rideDetails.bookFor}
               createdByUser={rideDetails.createdByUser}
            />

            <RidePricingCard fareSpec={rideDetails.fareSpec} />
         </div>

         {/* Row 3: Route Map & Reviews */}
         <div className='grid grid-cols-1 lg:grid-cols-2 gap-4'>
            <RideRouteMapCard
               pickupLocation={rideDetails.pickupLocation}
               destinationLocation={rideDetails.destinationLocation}
               stops={rideDetails.stops}
            />

            <RideReviewCard reviews={rideDetails.reviews} />
         </div>
      </div>
   );
}

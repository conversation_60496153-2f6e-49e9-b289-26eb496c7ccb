'use client';

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { AlertTriangle } from 'lucide-react';
import { useCancelRide } from '../api/mutations';
import { toast } from 'sonner';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useEffect } from 'react';

const cancelRideSchema = z.object({
  reason: z
    .string()
    .min(10, 'Cancellation reason must be at least 10 characters')
    .max(500, 'Cancellation reason must not exceed 500 characters')
    .trim(),
});

type CancelRideFormData = z.infer<typeof cancelRideSchema>;

interface CancelRideModalProps {
  isOpen: boolean;
  onClose: () => void;
  rideId: string;
  onCancelSuccess: () => void;
}

export function CancelRideModal({
  isOpen,
  onClose,
  rideId,
  onCancelSuccess,
}: CancelRideModalProps) {
  const cancelRideMutation = useCancelRide();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<CancelRideFormData>({
    resolver: zodResolver(cancelRideSchema),
    mode: 'onChange',
  });

  // Reset form when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      reset();
    }
  }, [isOpen, reset]);

  const onSubmit = (data: CancelRideFormData) => {
    cancelRideMutation.mutate(
      {
        rideId,
        reason: data.reason,
      },
      {
        onSuccess: () => {
          toast.success('Ride cancelled successfully');
          reset();
          onCancelSuccess();
          onClose();
        },
        onError: (error: any) => {
          toast.error(error?.response?.data?.message || 'Failed to cancel ride');
        },
      }
    );
  };

  const handleClose = () => {
    if (!cancelRideMutation.isPending) {
      reset();
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className='sm:max-w-md'>
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogHeader>
            <div className='flex items-center gap-3 mb-2'>
              <div className='w-10 h-10 rounded-full bg-red-100 flex items-center justify-center'>
                <AlertTriangle className='w-5 h-5 text-red-600' />
              </div>
              <DialogTitle className='text-lg font-semibold'>Cancel Ride</DialogTitle>
            </div>
            <DialogDescription className='text-base text-gray-600'>
              Are you sure you want to cancel this ride? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <div className='space-y-2 py-4'>
            <Label htmlFor='reason' className='text-sm font-medium text-gray-700'>
              Reason for Cancellation <span className='text-red-500'>*</span>
            </Label>
            <Textarea
              id='reason'
              placeholder='Enter the reason for cancelling this ride (minimum 10 characters)...'
              {...register('reason')}
              disabled={cancelRideMutation.isPending}
              rows={4}
              className={`resize-none ${errors.reason ? 'border-red-500 focus:border-red-500' : ''}`}
            />
            {errors.reason && (
              <p className='text-xs text-red-600 mt-1'>{errors.reason.message}</p>
            )}
            {!errors.reason && (
              <p className='text-xs text-gray-500'>
                This reason will be recorded in the ride lifecycle.
              </p>
            )}
          </div>

          <div className='p-3 bg-red-50 rounded-lg'>
            <div className='text-sm text-red-800'>
              <strong>Warning:</strong> Cancelling this ride will update its status to cancelled and
              notify relevant parties.
            </div>
          </div>

          <DialogFooter className='flex gap-3 sm:gap-3'>
            <Button
              type='button'
              variant='outline'
              onClick={handleClose}
              disabled={cancelRideMutation.isPending}
              className='flex-1'
            >
              No, Keep Ride
            </Button>
            <Button
              type='submit'
              disabled={cancelRideMutation.isPending}
              className='flex-1 bg-red-600 hover:bg-red-700 text-white'
            >
              {cancelRideMutation.isPending ? 'Cancelling...' : 'Yes, Cancel Ride'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

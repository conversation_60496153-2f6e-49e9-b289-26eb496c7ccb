'use client';

import {
   Di<PERSON>,
   DialogContent,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { FareSpec } from '../types/ride';
import { formatCurrency, formatChargeType } from '../utils/ride-formatters';

interface FareBreakdownModalProps {
   open: boolean;
   onOpenChange: (open: boolean) => void;
   fareSpec: FareSpec;
}

// Helper function to get meter unit
const getMeterUnit = (meter: string | null | undefined): string => {
   if (!meter) return '';

   const distanceMeters = ['pickup_distance', 'trip_distance'];
   const timeMeters = [
      'pickup_duration',
      'pickup_wait_duration',
      'trip_duration',
      'trip_wait_duration',
   ];

   if (distanceMeters.includes(meter)) return 'km';
   if (timeMeters.includes(meter)) return 'min';

   return '';
};

export function FareBreakdownModal({
   open,
   onOpenChange,
   fareSpec,
}: FareBreakdownModalProps) {
   return (
      <Dialog open={open} onOpenChange={onOpenChange}>
         <DialogContent className='max-w-3xl max-h-[80vh] overflow-y-auto'>
            <DialogHeader>
               <DialogTitle>Fare Breakdown</DialogTitle>
            </DialogHeader>
            <div className='space-y-4 mt-4'>
               {fareSpec.chargeBreakdown?.map(item => (
                  <div key={item.chargeId} className='border rounded-md p-4 bg-gray-50'>
                     <div className='flex justify-between items-start mb-3'>
                        <div>
                           <div className='text-sm font-semibold text-gray-900 mb-1'>
                              {item.chargeName}
                           </div>
                           <div className='flex gap-2 text-xs text-gray-600'>
                              <span className='px-2 py-0.5 bg-blue-50 text-blue-700 rounded'>
                                 {formatChargeType(item.chargeType)}
                              </span>
                              <span className='px-2 py-0.5 bg-purple-50 text-purple-700 rounded'>
                                 {formatChargeType(item.priceModel)}
                              </span>
                              {item.meter && (
                                 <span className='px-2 py-0.5 bg-green-50 text-green-700 rounded'>
                                    {formatChargeType(item.meter)}
                                 </span>
                              )}
                           </div>
                        </div>
                        <div className='text-right'>
                           <div className='text-sm font-bold text-gray-900'>
                              {formatCurrency(item.calculatedAmount, fareSpec.currency)}
                           </div>
                           {item.meterValue && (
                              <div className='text-xs text-gray-500'>
                                 {item.meterValue.toFixed(3)}{' '}
                                 {getMeterUnit(item.meter) || 'units'}
                              </div>
                           )}
                        </div>
                     </div>

                     {/* Calculation Breakdown */}
                     <div className='bg-white rounded p-3 space-y-2 text-xs'>
                        <div className='font-semibold text-gray-700 mb-2'>
                           Calculation Details
                        </div>

                        {item.calculationBreakdown.flatAmount !== undefined && (
                           <div className='flex justify-between text-gray-600'>
                              <span>Flat Amount:</span>
                              <span className='font-medium'>
                                 {formatCurrency(
                                    item.calculationBreakdown.flatAmount,
                                    fareSpec.currency
                                 )}
                              </span>
                           </div>
                        )}

                        {item.calculationBreakdown.tieredBreakdown &&
                           item.calculationBreakdown.tieredBreakdown.length > 0 && (
                              <div className='space-y-1'>
                                 <div className='font-medium text-gray-700'>
                                    Tiered Breakdown:
                                 </div>
                                 {item.calculationBreakdown.tieredBreakdown.map(
                                    (tier, tierIndex) => (
                                       <div
                                          key={tierIndex}
                                          className='pl-3 py-1 border-l-2 border-gray-300'
                                       >
                                          <div className='flex justify-between text-gray-600'>
                                             <span>
                                                Tier {tier.tierIndex + 1} ({tier.from} -{' '}
                                                {tier.to}):
                                             </span>
                                             <span className='font-medium'>
                                                {formatCurrency(
                                                   tier.tierAmount,
                                                   fareSpec.currency
                                                )}
                                             </span>
                                          </div>
                                          <div className='text-gray-500 text-xs'>
                                             {tier.unitsInTier.toFixed(3)}{' '}
                                             {getMeterUnit(item.meter) || 'units'}
                                             {tier.flatFee && ` (flat: ${tier.flatFee})`}
                                             {tier.unitPrice &&
                                                ` (rate: ${tier.unitPrice})`}
                                          </div>
                                       </div>
                                    )
                                 )}
                              </div>
                           )}

                        <div className='flex justify-between text-gray-900 font-semibold pt-2 border-t border-gray-200'>
                           <span>Final Amount:</span>
                           <span>
                              {formatCurrency(
                                 item.calculationBreakdown.finalAmount,
                                 fareSpec.currency
                              )}
                           </span>
                        </div>
                     </div>

                     {/* Applied Taxes */}
                     {item.appliedTaxes?.length > 0 && (
                        <div className='mt-3 bg-yellow-50 rounded p-3'>
                           <div className='text-xs font-semibold text-gray-700 mb-2'>
                              Applied Taxes
                           </div>
                           <div className='space-y-2 text-xs'>
                              {item.appliedTaxes?.map(tax => (
                                 <div key={tax.taxGroupId} className='space-y-1'>
                                    <div className='flex justify-between text-gray-700 font-medium'>
                                       <span>{tax.taxGroupName}</span>
                                       <span>
                                          {formatCurrency(
                                             tax.totalTaxAmount,
                                             fareSpec.currency
                                          )}
                                       </span>
                                    </div>
                                    {tax.subcategoryResults?.map(subcategory => (
                                       <div
                                          key={subcategory.subcategoryId}
                                          className='flex justify-between text-gray-600 pl-3'
                                       >
                                          <span>
                                             {subcategory.subcategoryName} (
                                             {subcategory.appliedPercentage}%)
                                          </span>
                                          <span className='font-medium'>
                                             {formatCurrency(
                                                subcategory.taxAmount,
                                                fareSpec.currency
                                             )}
                                          </span>
                                       </div>
                                    ))}
                                 </div>
                              ))}
                           </div>
                        </div>
                     )}

                     {/* Applied Commission */}
                     {item.appliedCommission && (
                        <div className='mt-3 bg-blue-50 rounded p-3'>
                           <div className='text-xs font-semibold text-gray-700 mb-2'>
                              Applied Commissions
                           </div>
                           {/* Commission Header */}
                           <div className='flex justify-between items-start mb-3'>
                              <div>
                                 <div className='text-sm font-semibold text-gray-900'>
                                    {item.appliedCommission.commissionName}
                                 </div>
                                 {item.appliedCommission.commissionRate && (
                                    <div className='text-xs text-gray-500 mt-0.5'>
                                       {item.appliedCommission.commissionRate}% on{' '}
                                       {formatCurrency(
                                          item.appliedCommission.baseAmount,
                                          fareSpec.currency
                                       )}
                                    </div>
                                 )}
                              </div>
                              <div className='text-right'>
                                 <div className='text-sm font-bold text-gray-900'>
                                    {formatCurrency(
                                       item.appliedCommission.finalCommissionAmount,
                                       fareSpec.currency
                                    )}
                                 </div>
                              </div>
                           </div>

                           {/* Show discounts/bonuses/caps only if they exist */}
                           {(item.appliedCommission.calculationBreakdown.totalDiscounts >
                              0 ||
                              item.appliedCommission.calculationBreakdown.totalBonuses >
                                 0 ||
                              item.appliedCommission.calculationBreakdown.capAdjustment !==
                                 0) && (
                              <div className='bg-white rounded p-3 space-y-1.5 text-xs mb-3'>
                                 {item.appliedCommission.calculationBreakdown
                                    .totalDiscounts > 0 && (
                                    <div className='flex justify-between text-red-600'>
                                       <span>Discounts Applied:</span>
                                       <span className='font-medium'>
                                          -
                                          {formatCurrency(
                                             item.appliedCommission.calculationBreakdown
                                                .totalDiscounts,
                                             fareSpec.currency
                                          )}
                                       </span>
                                    </div>
                                 )}

                                 {item.appliedCommission.calculationBreakdown
                                    .totalBonuses > 0 && (
                                    <div className='flex justify-between text-green-600'>
                                       <span>Bonuses Applied:</span>
                                       <span className='font-medium'>
                                          +
                                          {formatCurrency(
                                             item.appliedCommission.calculationBreakdown
                                                .totalBonuses,
                                             fareSpec.currency
                                          )}
                                       </span>
                                    </div>
                                 )}

                                 {item.appliedCommission.calculationBreakdown
                                    .capAdjustment !== 0 && (
                                    <div className='flex justify-between text-orange-600'>
                                       <span>Cap Adjustment:</span>
                                       <span className='font-medium'>
                                          {item.appliedCommission.calculationBreakdown
                                             .capAdjustment > 0
                                             ? '+'
                                             : ''}
                                          {formatCurrency(
                                             item.appliedCommission.calculationBreakdown
                                                .capAdjustment,
                                             fareSpec.currency
                                          )}
                                       </span>
                                    </div>
                                 )}
                              </div>
                           )}

                           {/* Commission Taxes */}
                           {(item.appliedCommission.commissionTaxAmount > 0 ||
                              item.appliedCommission.taxCalculation) && (
                              <div className='bg-teal-50 rounded p-3 mb-3'>
                                 <div className='text-xs font-semibold text-gray-700 mb-2'>
                                    Commission Taxes
                                 </div>
                                 <div className='space-y-2 text-xs'>
                                    {item.appliedCommission.taxCalculation ? (
                                       <div className='space-y-1'>
                                          <div className='flex justify-between text-gray-700 font-medium'>
                                             <span>
                                                {
                                                   item.appliedCommission.taxCalculation
                                                      .taxGroupName
                                                }
                                             </span>
                                             <span>
                                                {formatCurrency(
                                                   item.appliedCommission.taxCalculation
                                                      .totalTaxAmount,
                                                   fareSpec.currency
                                                )}
                                             </span>
                                          </div>
                                          {item.appliedCommission.taxCalculation.subcategoryResults?.map(
                                             subcategory => (
                                                <div
                                                   key={subcategory.subcategoryId}
                                                   className='flex justify-between text-gray-600 pl-3'
                                                >
                                                   <span>
                                                      {subcategory.subcategoryName} (
                                                      {subcategory.appliedPercentage}%)
                                                   </span>
                                                   <span className='font-medium'>
                                                      {formatCurrency(
                                                         subcategory.taxAmount,
                                                         fareSpec.currency
                                                      )}
                                                   </span>
                                                </div>
                                             )
                                          )}
                                       </div>
                                    ) : (
                                       <div className='flex justify-between text-gray-700 font-medium'>
                                          <span>Tax Amount</span>
                                          <span>
                                             {formatCurrency(
                                                item.appliedCommission.commissionTaxAmount,
                                                fareSpec.currency
                                             )}
                                          </span>
                                       </div>
                                    )}
                                 </div>
                              </div>
                           )}

                           {/* Total Commission with Tax */}
                           <div className='bg-blue-100 rounded p-3 border-t-2 border-blue-300'>
                              <div className='flex justify-between items-center'>
                                 <span className='text-sm font-bold text-gray-900'>
                                    Total Commission (incl. Tax):
                                 </span>
                                 <span className='text-base font-bold text-blue-700'>
                                    {formatCurrency(
                                       item.appliedCommission.totalCommissionWithTax,
                                       fareSpec.currency
                                    )}
                                 </span>
                              </div>
                           </div>
                        </div>
                     )}

                     {/* Status Indicators */}
                     <div className='flex gap-2 mt-3 text-xs'>
                        <span
                           className={`px-2 py-1 rounded ${
                              item.success
                                 ? 'bg-green-50 text-green-700'
                                 : 'bg-red-50 text-red-700'
                           }`}
                        >
                           {item.success ? 'Success' : 'Failed'}
                        </span>
                        <span
                           className={`px-2 py-1 rounded ${
                              item.conditionMet
                                 ? 'bg-green-50 text-green-700'
                                 : 'bg-gray-50 text-gray-700'
                           }`}
                        >
                           {item.conditionMet ? 'Condition Met' : 'Condition Not Met'}
                        </span>
                     </div>
                  </div>
               ))}

               {/* Enhanced Summary */}
               <div className='border-t pt-4 space-y-4'>
                  {/* Charge Summary Section */}
                  <div className='space-y-2'>
                     <div className='text-base font-semibold text-gray-900 mb-3'>
                        Charge Summary
                     </div>
                     <div className='flex justify-between text-sm text-gray-700'>
                        <span>Total Charge</span>
                        <span className='font-medium'>
                           {formatCurrency(fareSpec.subtotal, fareSpec.currency)}
                        </span>
                     </div>
                     {fareSpec.totalTaxes > 0 && (
                        <div className='flex justify-between text-sm text-gray-700'>
                           <span>Total Tax on Charge</span>
                           <span className='font-medium'>
                              {formatCurrency(fareSpec.totalTaxes, fareSpec.currency)}
                           </span>
                        </div>
                     )}
                     {fareSpec.grandTotal && (
                        <div className='flex justify-between text-sm font-medium text-gray-900 pt-2 border-t mt-2'>
                           <span>Total Fare</span>
                           <span>
                              {formatCurrency(fareSpec.grandTotal, fareSpec.currency)}
                           </span>
                        </div>
                     )}
                  </div>

                  {/* Revenue Distribution Section */}
                  <div className='space-y-2.5 pt-4 border-t mt-6'>
                     <div className='text-base font-semibold text-gray-900 mb-3'>
                        Revenue Distribution
                     </div>
                     {/* <div className='flex justify-between text-sm'>
                        <span className='text-gray-700'>Driver Earnings</span>
                        <span className='font-semibold text-green-600'>
                           {formatCurrency(fareSpec.driverEarnings, fareSpec.currency)}
                        </span>
                     </div> */}
                     {fareSpec.totalCommissions > 0 && (
                        <div className='flex justify-between text-sm'>
                           <span className='text-gray-700'>
                              Total Commissions{' '}
                              <span className='text-blue-600 text-xs'>(inc tax)</span>
                           </span>
                           <span className='font-medium text-gray-900'>
                              {formatCurrency(
                                 fareSpec.totalCommissions,
                                 fareSpec.currency
                              )}
                           </span>
                        </div>
                     )}
                     {fareSpec.totalTaxes > 0 && (
                        <div className='flex justify-between text-sm'>
                           <span className='text-gray-700'>Total Taxes on Charge</span>
                           <span className='font-medium text-gray-900'>
                              {formatCurrency(fareSpec.totalTaxes, fareSpec.currency)}
                           </span>
                        </div>
                     )}
                     {/* Net Deduction - calculated as commissions + taxes */}
                     <div className='flex justify-between text-sm'>
                        <span className='text-gray-700'>Net Deduction</span>
                        <span className='font-semibold text-red-600'>
                           {formatCurrency(
                              fareSpec.totalCommissions + fareSpec.totalTaxes,
                              fareSpec.currency
                           )}
                        </span>
                     </div>
                     {/* Final Driver Earnings */}
                     <div className='pt-2 border-t mt-2'>
                        <div className='flex justify-between text-sm mb-1'>
                           <span className='font-semibold text-gray-900'>Driver Earnings</span>
                           <span className='font-bold text-green-600'>
                              {formatCurrency(fareSpec.driverEarnings, fareSpec.currency)}
                           </span>
                        </div>
                        <div className='text-xs text-gray-500 italic'>
                           (Total Fare - Deduction)
                        </div>
                     </div>
                  </div>

                  {/* Final Total Section */}
                  <div className='flex justify-between text-base font-bold text-gray-900 pt-4 border-t mt-4'>
                     <span>Total Passenger Fare:</span>
                     <span>
                        {formatCurrency(fareSpec.passengerFare, fareSpec.currency)}
                     </span>
                  </div>
               </div>
            </div>
         </DialogContent>
      </Dialog>
   );
}

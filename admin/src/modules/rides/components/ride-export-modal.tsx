'use client';

import { useEffect } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { format, startOfToday } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
   DialogFooter,
} from '@/components/ui/dialog';
import { Spinner } from '@/components/ui/spinner';
import { cn } from '@/lib/utils';
import { toast } from '@/lib/toast';
import { downloadRidesCSV } from '../api/export';
import { useListCities } from '@/modules/city/api/queries';
import { useListCityProducts } from '@/modules/city/api/city-product-queries';

// Zod validation schema
const exportFormSchema = z
   .object({
      dateRange: z
         .object({
            from: z.date(),
            to: z.date(),
         })
         .refine(data => data.from && data.to, {
            message: 'Please select both start and end dates',
         })
         .refine(data => data.to >= data.from, {
            message: 'End date must be after or equal to start date',
         }),
      cityId: z.string().refine(val => val !== 'all', {
         message: 'Please select a city',
      }),
      cityProductId: z.string().refine(val => val !== 'all', {
         message: 'Please select a city product',
      }),
      status: z.string().optional(),
   })
   .strict();

type ExportFormData = z.infer<typeof exportFormSchema>;

interface RideExportModalProps {
   isOpen: boolean;
   onClose: () => void;
}

export function RideExportModal({ isOpen, onClose }: RideExportModalProps) {
   const {
      control,
      handleSubmit,
      reset,
      watch,
      setValue,
      formState: { isSubmitting },
   } = useForm<ExportFormData>({
      resolver: zodResolver(exportFormSchema),
      defaultValues: {
         dateRange: {
            from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            to: new Date(),
         },
         cityId: 'all',
         cityProductId: 'all',
         status: 'all',
      },
   });

   // Watch cityId to fetch city products
   const cityId = watch('cityId');

   // Fetch cities
   const citiesQuery = useListCities({
      page: 1,
      limit: 100,
   });

   // Fetch city products based on selected city
   const cityProductsQuery = useListCityProducts(cityId, {
      page: 1,
      limit: 100,
   });

   // Reset form when modal opens
   useEffect(() => {
      if (isOpen) {
         reset({
            dateRange: {
               from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
               to: new Date(),
            },
            cityId: 'all',
            cityProductId: 'all',
            status: 'all',
         });
      }
   }, [isOpen, reset]);

   // Reset city product when city changes
   useEffect(() => {
      setValue('cityProductId', 'all');
   }, [cityId, setValue]);

   const onSubmit = async (data: ExportFormData) => {
      try {
         const fromDate = format(data.dateRange.from, 'yyyy-MM-dd');
         const toDate = format(data.dateRange.to, 'yyyy-MM-dd');

         await downloadRidesCSV({
            fromDate,
            toDate,
            cityProductId: data.cityProductId,
            status: data.status !== 'all' ? data.status : undefined,
         });

         toast.success('Rides CSV downloaded successfully');
         onClose();
      } catch (error: any) {
         console.error('Export error:', error);
         toast.error(error?.message || 'Failed to download CSV');
      }
   };

   const onError = () => {
      // Errors will be shown inline, no need for toast
   };

   return (
      <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent className='max-w-md'>
            <DialogHeader>
               <DialogTitle>Export Rides to CSV</DialogTitle>
               <DialogDescription>
                  Export ride data with detailed fare, commission, and tax breakdown for the
                  selected period
               </DialogDescription>
            </DialogHeader>

            <div className='space-y-4 py-4'>
               {/* Date Range Picker */}
               <div className='flex flex-col gap-2'>
                  <Label>
                     Date Range <span className='text-red-500'>*</span>
                  </Label>
                  <Controller
                     control={control}
                     name='dateRange'
                     render={({ field, fieldState }) => (
                        <div className='relative'>
                           <Popover>
                              <PopoverTrigger asChild>
                                 <Button
                                    variant='outline'
                                    className={cn(
                                       'w-full justify-start text-left font-normal',
                                       !field.value && 'text-muted-foreground',
                                       fieldState.error && 'border-red-500'
                                    )}
                                 >
                                    <CalendarIcon className='mr-2 h-4 w-4' />
                                    {field.value?.from ? (
                                       field.value.to ? (
                                          <>
                                             {format(field.value.from, 'MMM dd, yyyy')} -{' '}
                                             {format(field.value.to, 'MMM dd, yyyy')}
                                          </>
                                       ) : (
                                          format(field.value.from, 'MMM dd, yyyy')
                                       )
                                    ) : (
                                       'Pick a date range'
                                    )}
                                 </Button>
                              </PopoverTrigger>
                              <PopoverContent className='w-auto p-2' align='start'>
                                 <Calendar
                                    mode='range'
                                    selected={field.value}
                                    onSelect={field.onChange}
                                    disabled={{ after: startOfToday() }}
                                 />
                              </PopoverContent>
                           </Popover>
                           {fieldState.error && (
                              <p className='text-sm text-red-500 mt-1'>
                                 {fieldState.error.message}
                              </p>
                           )}
                        </div>
                     )}
                  />
               </div>

               {/* City Selector */}
               <div className='flex flex-col gap-2'>
                  <Label>
                     City <span className='text-red-500'>*</span>
                  </Label>
                  <Controller
                     control={control}
                     name='cityId'
                     render={({ field, fieldState }) => (
                        <div>
                           <Select value={field.value} onValueChange={field.onChange}>
                              <SelectTrigger
                                 className={cn('w-full', fieldState.error && 'border-red-500')}
                              >
                                 <SelectValue placeholder='Select a city' />
                              </SelectTrigger>
                              <SelectContent>
                                 <SelectItem value='all'>Select a city</SelectItem>
                                 {citiesQuery.data?.data?.map(city => (
                                    <SelectItem key={city.id} value={city.id}>
                                       {city.name}
                                    </SelectItem>
                                 ))}
                              </SelectContent>
                           </Select>
                           {fieldState.error && (
                              <p className='text-sm text-red-500 mt-1'>
                                 {fieldState.error.message}
                              </p>
                           )}
                        </div>
                     )}
                  />
               </div>

               {/* City Product Selector */}
               <div className='flex flex-col gap-2'>
                  <Label>
                     City Product <span className='text-red-500'>*</span>
                  </Label>
                  <Controller
                     control={control}
                     name='cityProductId'
                     render={({ field, fieldState }) => (
                        <div>
                           <Select
                              value={field.value}
                              onValueChange={field.onChange}
                              disabled={cityId === 'all'}
                           >
                              <SelectTrigger
                                 className={cn('w-full', fieldState.error && 'border-red-500')}
                              >
                                 <SelectValue placeholder='Select a city product' />
                              </SelectTrigger>
                              <SelectContent>
                                 <SelectItem value='all'>Select a city product</SelectItem>
                                 {cityProductsQuery.data?.data?.map(cityProduct => (
                                    <SelectItem key={cityProduct.id} value={cityProduct.id}>
                                       {cityProduct.product?.name || 'Unknown Product'}
                                    </SelectItem>
                                 ))}
                              </SelectContent>
                           </Select>
                           {fieldState.error && (
                              <p className='text-sm text-red-500 mt-1'>
                                 {fieldState.error.message}
                              </p>
                           )}
                        </div>
                     )}
                  />
               </div>

               {/* Status Filter */}
               <div className='flex flex-col gap-2'>
                  <Label>Ride Status (Optional)</Label>
                  <Controller
                     control={control}
                     name='status'
                     render={({ field }) => (
                        <Select value={field.value} onValueChange={field.onChange}>
                           <SelectTrigger className='w-full'>
                              <SelectValue placeholder='All Statuses' />
                           </SelectTrigger>
                           <SelectContent>
                              <SelectItem value='all'>All Statuses</SelectItem>
                              <SelectItem value='trip_completed'>Trip Completed</SelectItem>
                              <SelectItem value='cancelled'>Cancelled</SelectItem>
                           </SelectContent>
                        </Select>
                     )}
                  />
               </div>
            </div>

            <DialogFooter className='!flex !gap-2'>
               <Button
                  type='button'
                  variant='outline'
                  onClick={onClose}
                  disabled={isSubmitting}
                  className=''
               >
                  Cancel
               </Button>

               <Button
                  type='button'
                  onClick={handleSubmit(onSubmit, onError)}
                  disabled={isSubmitting}
                  className=''
               >
                  {isSubmitting ? (
                     <>
                        Exporting...
                        <Spinner className='ml-2 h-4 w-4' />
                     </>
                  ) : (
                     'Export CSV'
                  )}
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
}

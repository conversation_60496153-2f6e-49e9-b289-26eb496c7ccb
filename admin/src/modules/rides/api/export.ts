import { apiClient } from '@/lib/api-client';

interface DownloadRidesCSVParams {
   fromDate: string;
   toDate: string;
   cityProductId: string;
   status?: string;
}

export const downloadRidesCSV = async ({
   fromDate,
   toDate,
   cityProductId,
   status,
}: DownloadRidesCSVParams): Promise<void> => {
   try {
      const response = await apiClient.post<Blob>(
         '/admin/reports/rides/generate',
         {
            fromDate,
            toDate,
            cityProductId,
            status: status && status !== 'all' ? status : undefined,
         },
         {
            responseType: 'blob',
         }
      );

      // Create blob from response
      const blob = new Blob([response as unknown as BlobPart], { type: 'text/csv;charset=utf-8;' });

      // Generate filename with date range
      const fileName = `rides_report_${fromDate}_to_${toDate}.csv`;

      // Create download link and trigger download
      const link = document.createElement('a');
      const url = window.URL.createObjectURL(blob);
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
   } catch (error: any) {
      console.error('Download CSV error:', error);
      throw new Error(error?.response?.data?.message || 'Failed to download CSV report');
   }
};

'use client';

import { useState, useEffect, useId } from 'react';
import { CheckIcon, ChevronDownIcon } from 'lucide-react';
import {
   Dialog,
   DialogContent,
   DialogHeader,
   DialogTitle,
   DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
   Command,
   CommandEmpty,
   CommandGroup,
   CommandInput,
   CommandItem,
   CommandList,
} from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Spinner } from '@/components/ui/spinner';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { useAttachCharge } from '../api/charge-mutations';
import { useListAllCharges, useListCharges } from '@/modules/charge/api/charge-queries';
import { cn } from '@/lib/utils';
import { Charge } from '../types/charge';

interface AttachChargeModalProps {
   chargeGroupId: string;
   isOpen: boolean;
   onClose: () => void;
}

export function AttachChargeModal({ chargeGroupId, isOpen, onClose }: AttachChargeModalProps) {
   const id = useId();
   const [open, setOpen] = useState<boolean>(false);
   const [selectedChargeId, setSelectedChargeId] = useState<string>('');
   const [searchInput, setSearchInput] = useState<string>('');
   const [debouncedSearch, setDebouncedSearch] = useState<string>('');

   const queryClient = useQueryClient();
   const attachChargeMutation = useAttachCharge();
   const { data: allChargesData, isLoading: isLoadingCharges } = useListAllCharges(
      1,
      10,
      debouncedSearch || undefined,
      true
   );
   const { data: existingChargesData } = useListCharges(chargeGroupId);

   // Filter out charges that are already attached to this charge group
   const availableCharges =
      allChargesData?.data?.data?.filter(
         (charge: Charge) =>
            !existingChargesData?.data?.some(
               (existingCharge: Charge) => existingCharge.id === charge.id
            )
      ) || [];

   // Debounce search input
   useEffect(() => {
      const timer = setTimeout(() => {
         setDebouncedSearch(searchInput);
      }, 300);

      return () => clearTimeout(timer);
   }, [searchInput]);

   // Reset form when modal opens

   useEffect(() => {
      if (isOpen) {
         setSelectedChargeId('');
         setSearchInput('');
         setDebouncedSearch('');
         setOpen(false);
      }
   }, [isOpen]);

   const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();

      if (!selectedChargeId) {
         toast.error('Please select a charge');
         return;
      }

      attachChargeMutation.mutate(
         {
            chargeGroupId,
            request: {
               chargeId: selectedChargeId,
            },
         },
         {
            onSuccess: () => {
               toast.success('Charge attached successfully');
               queryClient.invalidateQueries({ queryKey: ['charges', chargeGroupId] });
               onClose();
            },
         }
      );
   };

   return (
      <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent className='sm:max-w-[500px]'>
            <DialogHeader>
               <DialogTitle>Attach Shared Charge</DialogTitle>
               <p className='text-sm text-muted-foreground mt-2'>
                  Select a centrally-managed charge to attach to this group
               </p>
            </DialogHeader>

            <form onSubmit={handleSubmit}>
               <div className='space-y-4 py-4'>
                  {/* Charge Selection */}
                  <div className='space-y-2'>
                     <Label htmlFor={id}>Select Shared Charge</Label>
                     <Popover open={open} onOpenChange={setOpen}>
                        <PopoverTrigger asChild>
                           <Button
                              id={id}
                              variant='outline'
                              role='combobox'
                              aria-expanded={open}
                              className='bg-background hover:bg-background border-input w-full justify-between px-3 font-normal outline-offset-0 outline-none focus-visible:outline-[3px]'
                           >
                              <span
                                 className={cn(
                                    'truncate',
                                    !selectedChargeId && 'text-muted-foreground'
                                 )}
                              >
                                 {selectedChargeId
                                    ? availableCharges?.find(
                                         (charge: Charge) => charge.id === selectedChargeId
                                      )?.name +
                                      (availableCharges?.find(
                                         (charge: Charge) => charge.id === selectedChargeId
                                      )?.identifier
                                         ? ` (${
                                              availableCharges?.find(
                                                 (charge: Charge) => charge.id === selectedChargeId
                                              )?.identifier
                                           })`
                                         : '')
                                    : 'Select a shared charge'}
                              </span>
                              <ChevronDownIcon
                                 size={16}
                                 className='text-muted-foreground/80 shrink-0'
                                 aria-hidden='true'
                              />
                           </Button>
                        </PopoverTrigger>
                        <PopoverContent
                           className='border-input w-full min-w-[var(--radix-popper-anchor-width)] p-0'
                           align='start'
                        >
                           <Command shouldFilter={false}>
                              <div className='relative'>
                                 <CommandInput
                                    placeholder='Search charge...'
                                    value={searchInput}
                                    onValueChange={setSearchInput}
                                 />
                                 {isLoadingCharges && searchInput !== debouncedSearch && (
                                    <div className='absolute right-3 top-1/2 -translate-y-1/2'>
                                       <Spinner className='h-4 w-4' />
                                    </div>
                                 )}
                              </div>
                              <CommandList>
                                 <CommandEmpty>No charge found.</CommandEmpty>
                                 <CommandGroup>
                                    {availableCharges?.map((charge: Charge) => (
                                       <CommandItem
                                          key={charge.id}
                                          value={charge.id}
                                          onSelect={(currentValue: string) => {
                                             setSelectedChargeId(
                                                currentValue === selectedChargeId
                                                   ? ''
                                                   : currentValue
                                             );
                                             setOpen(false);
                                          }}
                                       >
                                          {charge.name}{' '}
                                          {charge.identifier ? `(${charge.identifier})` : ''}
                                          {selectedChargeId === charge.id && (
                                             <CheckIcon size={16} className='ml-auto' />
                                          )}
                                       </CommandItem>
                                    ))}
                                 </CommandGroup>
                              </CommandList>
                           </Command>
                        </PopoverContent>
                     </Popover>
                  </div>
               </div>

               <DialogFooter>
                  <Button
                     type='button'
                     variant='outline'
                     onClick={onClose}
                     disabled={attachChargeMutation.isPending}
                  >
                     Cancel
                  </Button>
                  <Button
                     type='submit'
                     disabled={attachChargeMutation.isPending || !selectedChargeId}
                  >
                     {attachChargeMutation.isPending ? (
                        <>
                           <Spinner className='h-4 w-4 mr-2' />
                           Attaching...
                        </>
                     ) : (
                        'Attach Charge'
                     )}
                  </Button>
               </DialogFooter>
            </form>
         </DialogContent>
      </Dialog>
   );
}

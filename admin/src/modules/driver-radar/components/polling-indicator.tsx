'use client';

import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import { Pause, Play, RefreshCw } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { MAP_CONSTANTS } from '../types/radar';
import { formatCountdown } from '../utils/map-utils';

interface PollingIndicatorProps {
   isFetching: boolean;
   nextUpdate: number;
   onManualRefresh: () => void;
   isPaused: boolean;
   onTogglePolling: () => void;
}

export const PollingIndicator: React.FC<PollingIndicatorProps> = ({
   isFetching,
   nextUpdate,
   onManualRefresh,
   isPaused,
   onTogglePolling,
}) => {
   const [countdown, setCountdown] = useState(0);

   useEffect(() => {
      if (isPaused) {
         setCountdown(0);
         return;
      }

      const interval = setInterval(() => {
         const remaining = Math.max(0, Math.ceil((nextUpdate - Date.now()) / 1000));
         setCountdown(remaining);

         // Reset when countdown reaches 0
         if (remaining === 0) {
            setCountdown(Math.ceil(MAP_CONSTANTS.POLLING_INTERVAL_MS / 1000));
         }
      }, 100);

      return () => clearInterval(interval);
   }, [nextUpdate, isPaused]);

   return (
      <div className='absolute top-4 right-4 z-10'>
         <div className='bg-white rounded-lg shadow-lg border border-gray-200 px-3 py-2.5 min-w-[240px]'>
            {/* Header */}
            <div className='flex items-center justify-between mb-2'>
               <h3 className='text-sm font-semibold text-gray-800'>Live Updates</h3>
               {isFetching && (
                  <div className='flex items-center'>
                     <Spinner className='h-4 w-4 text-blue-600' />
                     <span className='ml-1 text-xs text-blue-600 animate-pulse'>Updating...</span>
                  </div>
               )}
            </div>

            {/* Status */}
            <div className='mb-2.5'>
               {!isPaused && (
                  <div className='flex items-center justify-between text-xs'>
                     <span className='text-gray-500'>Next update:</span>
                     <span className='text-gray-700 font-medium tabular-nums'>
                        {formatCountdown(countdown)}
                     </span>
                  </div>
               )}

               {isPaused && (
                  <div className='text-xs text-amber-600 font-medium'>Auto-refresh paused</div>
               )}
            </div>

            {/* Action Buttons - Side by Side */}
            <div className='flex gap-2'>
               <Button
                  onClick={onManualRefresh}
                  disabled={isFetching}
                  variant='default'
                  size='sm'
                  className='flex-1 text-xs'
               >
                  <RefreshCw className={`mr-1 h-3 w-3 ${isFetching ? 'animate-spin' : ''}`} />
                  Refresh
               </Button>

               <Button
                  onClick={onTogglePolling}
                  variant='outline'
                  size='sm'
                  className='flex-1 text-xs'
               >
                  {isPaused ? (
                     <>
                        <Play className='mr-1 h-3 w-3' />
                        Resume
                     </>
                  ) : (
                     <>
                        <Pause className='mr-1 h-3 w-3' />
                        Pause
                     </>
                  )}
               </Button>
            </div>

            {/* Pulse Animation Indicator */}
            {isFetching && (
               <div className='absolute -top-1 -right-1 w-3 h-3'>
                  <span className='absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75 animate-ping' />
                  <span className='relative inline-flex rounded-full h-3 w-3 bg-blue-500' />
               </div>
            )}
         </div>
      </div>
   );
};

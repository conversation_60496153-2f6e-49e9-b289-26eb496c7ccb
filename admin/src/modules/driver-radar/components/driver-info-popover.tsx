'use client';

import React from 'react';
import { InfoWindow } from '@react-google-maps/api';
import { Button } from '@/components/ui/button';
import { usePathname, useRouter } from 'next/navigation';
import { RadarDriver, Coordinate } from '../types/radar';
import { getStatusColor } from '../utils/map-utils';
import { ExternalLink, Mail, Phone, MapPin, Loader2 } from 'lucide-react';
import { useGetDriver } from '@/modules/driver/api/queries';
import { RideInfoSection } from './ride-info-section';

interface DriverInfoPopoverProps {
   driver: RadarDriver | null;
   isOpen: boolean;
   onClose: () => void;
   position: Coordinate | null;
   currentZoom: number;
}

export const DriverInfoPopover: React.FC<DriverInfoPopoverProps> = ({
   driver,
   isOpen,
   onClose,
   position,
   currentZoom,
}) => {
   const router = useRouter();
   const currentPath = usePathname();

   // Fetch detailed driver information
   const { data: driverDetails, isLoading } = useGetDriver(driver?.id || null);

   if (!isOpen || !driver || !position) return null;

   const statusColor = getStatusColor(driver.status);

   const handleViewDetails = () => {
      router.push(`/dashboard/drivers/${driver.id}?returnUrl=${currentPath}&lat=${driver.lat}&lng=${driver.lng}&zoom=${currentZoom}`);
   };

   const driverData = driverDetails?.data;
   const fullName =
      driverData?.firstName && driverData?.lastName
         ? `${driverData.firstName} ${driverData.lastName}`
         : null;

   return (
      <InfoWindow position={position} onCloseClick={onClose}>
         <div className='p-3 min-w-[200px] space-y-2.5'>
            {isLoading ? (
               <div className='flex items-center justify-center py-6'>
                  <Loader2 className='h-4 w-4 animate-spin text-gray-400' />
               </div>
            ) : (
               <>
                  {/* Header with Name and Status */}
                  <div className='flex items-start justify-between gap-2'>
                     <div className='flex-1 min-w-0'>
                        {fullName ? (
                           <p className='text-sm font-bold text-gray-900 truncate'>{fullName}</p>
                        ) : (
                           <p className='text-sm font-bold text-gray-900'>Driver</p>
                        )}
                     </div>
                     <span
                        className='inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-semibold text-white shadow-sm flex-shrink-0'
                        style={{ backgroundColor: statusColor }}
                     >
                        {driver.status
                           .split('_')
                           .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                           .join(' ')}
                     </span>
                  </div>

                  {/* Divider */}
                  <div className='border-t border-gray-100' />

                  {/* Driver Details */}
                  <div className='space-y-2'>
                     {/* Product */}
                     <div className='flex items-center gap-1.5'>
                        <div className='flex-shrink-0 w-4 h-4 rounded-full bg-gray-100 flex items-center justify-center'>
                           <div className='w-1.5 h-1.5 rounded-full bg-gray-400' />
                        </div>
                        <div className='flex-1 min-w-0'>
                           <p className='text-[10px] text-gray-500'>Product</p>
                           <p className='text-xs font-medium text-gray-900 truncate'>
                              {driver.productName}
                           </p>
                        </div>
                     </div>

                     {/* Phone Number */}
                     {driverData?.phoneNumber && (
                        <div className='flex items-center gap-1.5'>
                           <div className='flex-shrink-0 w-4 h-4 rounded-full bg-blue-50 flex items-center justify-center'>
                              <Phone className='w-2.5 h-2.5 text-blue-600' />
                           </div>
                           <div className='flex-1 min-w-0'>
                              <p className='text-[10px] text-gray-500'>Phone</p>
                              <p className='text-xs font-medium text-gray-900'>
                                 {driverData.phoneNumber}
                              </p>
                           </div>
                        </div>
                     )}

                     {/* Email */}
                     {driverData?.email && (
                        <div className='flex items-center gap-1.5'>
                           <div className='flex-shrink-0 w-4 h-4 rounded-full bg-purple-50 flex items-center justify-center'>
                              <Mail className='w-2.5 h-2.5 text-purple-600' />
                           </div>
                           <div className='flex-1 min-w-0'>
                              <p className='text-[10px] text-gray-500'>Email</p>
                              <p className='text-xs font-medium text-gray-900 truncate'>
                                 {driverData.email}
                              </p>
                           </div>
                        </div>
                     )}

                     {/* City */}
                     {driverData?.cityName && (
                        <div className='flex items-center gap-1.5'>
                           <div className='flex-shrink-0 w-4 h-4 rounded-full bg-green-50 flex items-center justify-center'>
                              <MapPin className='w-2.5 h-2.5 text-green-600' />
                           </div>
                           <div className='flex-1 min-w-0'>
                              <p className='text-[10px] text-gray-500'>City</p>
                              <p className='text-xs font-medium text-gray-900 truncate'>
                                 {driverData.cityName}
                              </p>
                           </div>
                        </div>
                     )}
                  </div>

                  {/* View Details Button */}
                  <Button
                     onClick={handleViewDetails}
                     variant='outline'
                     size='sm'
                     className='w-full text-[11px] font-medium hover:bg-gray-50 transition-colors border-gray-200 h-8 mt-1'
                  >
                     <ExternalLink className='mr-1 h-3 w-3' />
                     View Driver Profile
                  </Button>

                  {/* Ride Details Section - Show when driver has an active ride */}
                  {driver.rideId && driver.status === 'in_ride' && (
                     <RideInfoSection
                        rideId={driver.rideId}
                        driverLat={driver.lat}
                        driverLng={driver.lng}
                        currentZoom={currentZoom}
                     />
                  )}
               </>
            )}
         </div>
      </InfoWindow>
   );
};

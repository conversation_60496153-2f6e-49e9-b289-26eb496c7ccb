'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { useRouter, usePathname } from 'next/navigation';
import { ExternalLink, MapPin, Navigation, Clock, Loader2, User } from 'lucide-react';
import { useGetRideDetails } from '@/modules/rides/api/queries';

interface RideInfoSectionProps {
   rideId: string;
   driverLat: number;
   driverLng: number;
   currentZoom: number;
}

const getRideStatusColor = (status: string): string => {
   const statusColors: Record<string, string> = {
      pending: '#F59E0B', // amber-500
      accepted: '#3B82F6', // blue-500
      arriving: '#8B5CF6', // violet-500
      arrived: '#6366F1', // indigo-500
      started: '#10B981', // green-500
      completed: '#059669', // emerald-600
      cancelled: '#EF4444', // red-500
      rejected: '#DC2626', // red-600
   };
   return statusColors[status.toLowerCase()] || '#6B7280'; // gray-500 as fallback
};

const formatDuration = (minutes: number | null | undefined): string => {
   if (!minutes) return 'N/A';
   const hours = Math.floor(minutes / 60);
   const mins = Math.round(minutes % 60);
   if (hours > 0) {
      return `${hours}h ${mins}m`;
   }
   return `${mins}m`;
};

const formatDistance = (meters: number | null | undefined): string => {
   if (!meters) return 'N/A';
   const km = (meters / 1000).toFixed(1);
   return `${km} km`;
};

const truncateAddress = (address: string | null | undefined, maxLength: number = 35): string => {
   if (!address) return 'N/A';
   if (address.length <= maxLength) return address;
   return `${address.substring(0, maxLength)}...`;
};

const formatStatusText = (status: string): string => {
   // Replace underscores with spaces and capitalize each word
   return status
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
};

export const RideInfoSection: React.FC<RideInfoSectionProps> = ({
   rideId,
   driverLat,
   driverLng,
   currentZoom,
}) => {
   const router = useRouter();
   const currentPath = usePathname();

   // Fetch ride details
   const { data: rideDetailsResponse, isLoading, isError } = useGetRideDetails(rideId);

   if (isLoading) {
      return (
         <div className='flex items-center justify-center py-6'>
            <Loader2 className='h-4 w-4 animate-spin text-gray-400' />
         </div>
      );
   }

   if (isError || !rideDetailsResponse?.data) {
      return (
         <div className='p-3 text-center'>
            <p className='text-xs text-red-500'>Failed to load ride details</p>
         </div>
      );
   }

   const rideDetails = rideDetailsResponse.data;
   const riderFullName = `${rideDetails.rider.firstName} ${rideDetails.rider.lastName}`;
   const statusColor = getRideStatusColor(rideDetails.status);

   const handleViewRideDetails = () => {
      router.push(
         `/dashboard/rides?rideId=${rideId}?returnUrl=${currentPath}&lat=${driverLat}&lng=${driverLng}&zoom=${currentZoom}`
      );
   };

   return (
      <>
         {/* Divider between driver and ride sections */}
         <div className='border-t-2 border-gray-200 my-2' />

         {/* Ride Header */}
         <div className='flex items-start justify-between gap-2'>
            <div className='flex-1 min-w-0'>
               <p className='text-xs font-semibold text-gray-700'>Current Ride</p>
            </div>
            <span
               className='inline-flex items-center px-2 py-0.5 rounded-full text-[10px] font-semibold text-white shadow-sm flex-shrink-0'
               style={{ backgroundColor: statusColor }}
            >
               {formatStatusText(rideDetails.status)}
            </span>
         </div>

         {/* Divider */}
         <div className='border-t border-gray-100 mt-2' />

         {/* Ride Details */}
         <div className='space-y-2 mt-2'>
            {/* Rider Name */}
            <div className='flex items-center gap-1.5'>
               <div className='flex-shrink-0 w-4 h-4 rounded-full bg-indigo-50 flex items-center justify-center'>
                  <User className='w-2.5 h-2.5 text-indigo-600' />
               </div>
               <div className='flex-1 min-w-0'>
                  <p className='text-[10px] text-gray-500'>Rider</p>
                  <p className='text-xs font-medium text-gray-900 truncate'>{riderFullName}</p>
               </div>
            </div>

            {/* Pickup Location */}
            {rideDetails.pickupLocation?.address && (
               <div className='flex items-center gap-1.5'>
                  <div className='flex-shrink-0 w-4 h-4 rounded-full bg-green-50 flex items-center justify-center'>
                     <MapPin className='w-2.5 h-2.5 text-green-600' />
                  </div>
                  <div className='flex-1 min-w-0'>
                     <p className='text-[10px] text-gray-500'>Pickup</p>
                     <p
                        className='text-xs font-medium text-gray-900 truncate'
                        title={rideDetails.pickupLocation.address}
                     >
                        {truncateAddress(rideDetails.pickupLocation.address)}
                     </p>
                  </div>
               </div>
            )}

            {/* Destination Location */}
            {rideDetails.destinationLocation?.address && (
               <div className='flex items-center gap-1.5'>
                  <div className='flex-shrink-0 w-4 h-4 rounded-full bg-red-50 flex items-center justify-center'>
                     <Navigation className='w-2.5 h-2.5 text-red-600' />
                  </div>
                  <div className='flex-1 min-w-0'>
                     <p className='text-[10px] text-gray-500'>Destination</p>
                     <p
                        className='text-xs font-medium text-gray-900 truncate'
                        title={rideDetails.destinationLocation.address}
                     >
                        {truncateAddress(rideDetails.destinationLocation.address)}
                     </p>
                  </div>
               </div>
            )}

            {/* Duration and Distance */}
            <div className='flex items-center gap-1.5'>
               <div className='flex-shrink-0 w-4 h-4 rounded-full bg-amber-50 flex items-center justify-center'>
                  <Clock className='w-2.5 h-2.5 text-amber-600' />
               </div>
               <div className='flex-1 min-w-0'>
                  <p className='text-[10px] text-gray-500'>Trip Info</p>
                  <p className='text-xs font-medium text-gray-900'>
                     {formatDuration(rideDetails.duration || rideDetails.actualDuration)} •{' '}
                     {formatDistance(rideDetails.distance)}
                  </p>
               </div>
            </div>
         </div>

         {/* View Ride Details Button */}
         <Button
            onClick={handleViewRideDetails}
            variant='outline'
            size='sm'
            className='w-full text-[11px] font-medium hover:bg-gray-50 transition-colors border-gray-200 h-8 mt-2'
         >
            <ExternalLink className='mr-1 h-3 w-3' />
            View Ride Details
         </Button>
      </>
   );
};

'use client';

import React, { useEffect, useRef } from 'react';
import { Coordinate, DriverStatus, STATUS_COLORS } from '../types/radar';

interface StatusCircleOverlayProps {
   position: Coordinate;
   status: DriverStatus;
   map: google.maps.Map | null;
}

// Calculate radius in meters that corresponds to a pixel size on screen
// This ensures circles scale proportionally to the marker icon (28x28 px)
const getRadiusInMetersForPixels = (
   map: google.maps.Map,
   position: { lat: number; lng: number },
   pixelRadius: number
): number => {
   const scale = Math.pow(2, map.getZoom() || 11);
   const metersPerPixel = (156543.03392 * Math.cos((position.lat * Math.PI) / 180)) / scale;
   return pixelRadius * metersPerPixel;
};

// Circle opacity value
const CIRCLE_OPACITY = 0.3;

export const StatusCircleOverlay = React.memo(({ position, status, map }: StatusCircleOverlayProps) => {
   const circleRef = useRef<google.maps.Circle | null>(null);

   useEffect(() => {
      if (!map) return;

      const statusColor = STATUS_COLORS[status];

      // Calculate radius based on pixel size to match marker icon (28x28 px)
      // Circle: 22px radius
      const radius = getRadiusInMetersForPixels(map, position, 22);

      // Cleanup existing circle
      if (circleRef.current) {
         circleRef.current.setMap(null);
         circleRef.current = null;
      }

      // Create status circle overlay
      const circle = new google.maps.Circle({
         center: { lat: position.lat, lng: position.lng },
         radius: radius,
         strokeColor: statusColor,
         strokeOpacity: 0,
         strokeWeight: 0,
         fillColor: statusColor,
         fillOpacity: CIRCLE_OPACITY,
         clickable: false,
         draggable: false,
         editable: false,
         map: map,
         zIndex: 1,
      });

      circleRef.current = circle;

      // Listen to zoom changes and update circle radius dynamically
      const zoomListener = map.addListener('zoom_changed', () => {
         const newRadius = getRadiusInMetersForPixels(map, position, 22);

         if (circleRef.current) {
            circleRef.current.setRadius(newRadius);
         }
      });

      // Cleanup on unmount or when dependencies change
      return () => {
         if (zoomListener) {
            google.maps.event.removeListener(zoomListener);
         }
         if (circleRef.current) {
            circleRef.current.setMap(null);
            circleRef.current = null;
         }
      };
   }, [map, position, position.lat, position.lng, status]);

   // This component doesn't render anything directly
   return null;
});

StatusCircleOverlay.displayName = 'StatusCircleOverlay';

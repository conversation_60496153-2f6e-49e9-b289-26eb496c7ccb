'use client';

import { Cluster, ClusterStats, Renderer } from '@googlemaps/markerclusterer';
import { STATUS_COLORS } from '../types/radar';

/**
 * Custom renderer for marker clusters
 * Creates SVG-based cluster markers with count badges
 */
export class CustomClusterRenderer implements Renderer {
  /**
   * Render method called by MarkerClusterer for each cluster
   */
  render(
    { count, position }: Cluster,
    stats: ClusterStats,
    map: google.maps.Map
  ): google.maps.Marker {
    // Calculate cluster size based on marker count
    const size = this.calculateClusterSize(count, stats);

    // Get dominant status color from markers in cluster
    const color = this.getDominantStatusColor(count, stats);

    // Create SVG icon for cluster
    const svg = this.createClusterSvg(size, color, count);

    // Create and return marker
    return new google.maps.Marker({
      position,
      icon: {
        url: `data:image/svg+xml;base64,${btoa(svg)}`,
        scaledSize: new google.maps.Size(size, size),
        anchor: new google.maps.Point(size / 2, size / 2),
      },
      label: {
        text: String(count),
        color: 'white',
        fontSize: this.getFontSize(size),
        fontWeight: 'bold',
      },
      zIndex: Number(google.maps.Marker.MAX_ZINDEX) + count,
      map,
    });
  }

  /**
   * Calculate cluster marker size based on count
   * Returns size in pixels
   */
  private calculateClusterSize(count: number, stats: ClusterStats): number {
    const minSize = 32;
    const maxSize = 50;
    const ratio = Math.min(count / stats.clusters.markers.max, 1);
    return Math.floor(minSize + (maxSize - minSize) * ratio);
  }

  /**
   * Get dominant status color from clustered markers
   * Prioritizes: in_ride > busy > online > idle > offline
   */
  private getDominantStatusColor(_count: number, _stats: ClusterStats): string {
    // Try to get status from markers in cluster
    // Note: MarkerClusterer doesn't expose marker data directly in stats
    // We'll use a default color scheme based on count for now
    // This can be enhanced by extending the Cluster type with driver data

    // Default: use online color for clusters
    // In a future enhancement, we could aggregate status data from the cluster
    return STATUS_COLORS.online;
  }

  /**
   * Create SVG icon for cluster marker
   */
  private createClusterSvg(size: number, _color: string, _count: number): string {
    return `
      <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
        <!-- Solid blue background for cluster -->
        <circle
          cx="${size / 2}"
          cy="${size / 2}"
          r="${size / 2}"
          fill="#306aff"
          opacity="1"
        />
      </svg>
    `;
  }

  /**
   * Calculate appropriate font size based on cluster size
   */
  private getFontSize(size: number): string {
    return `${Math.floor(size * 0.4)}px`;
  }
}

/**
 * Factory function to create cluster renderer instance
 */
export const createClusterRenderer = (): Renderer => {
  return new CustomClusterRenderer();
};

'use client';

import React from 'react';
import { RadarDriver } from '../types/radar';
import { getStatusColor } from '../utils/map-utils';
import {
   Dialog,
   DialogContent,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ExternalLink } from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';
import Image from 'next/image';

interface ClusterDriversPopupProps {
   drivers: RadarDriver[];
   isOpen: boolean;
   onClose: () => void;
   currentZoom: number;
}

export const ClusterDriversPopup: React.FC<ClusterDriversPopupProps> = ({
   drivers,
   isOpen,
   onClose,
   currentZoom,
}) => {
   const router = useRouter();
   const currentPath = usePathname();

   const handleViewProfile = (driver: RadarDriver) => {
      router.push(
         `/dashboard/drivers/${driver.id}?returnUrl=${currentPath}&lat=${driver.lat}&lng=${driver.lng}&zoom=${currentZoom}`
      );
   };

   return (
      <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent className='sm:max-w-[500px] max-h-[80vh]'>
            <DialogHeader>
               <DialogTitle>
                  Drivers at this location ({drivers.length})
               </DialogTitle>
            </DialogHeader>
            <ScrollArea className='max-h-[60vh] pr-4'>
               <div className='space-y-3'>
                  {drivers.map((driver) => {
                     const statusColor = getStatusColor(driver.status);

                     return (
                        <div
                           key={driver.id}
                           className='p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors'
                        >
                           <div className='flex items-start gap-3'>
                              {/* Product Icon */}
                              {driver.productIcon ? (
                                 <div className='flex-shrink-0 w-10 h-10 relative'>
                                    <Image
                                       src={driver.productIcon}
                                       alt={driver.productName}
                                       fill
                                       className='object-contain'
                                    />
                                 </div>
                              ) : (
                                 <div className='flex-shrink-0 w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center'>
                                    <div className='w-4 h-4 rounded-full bg-gray-400' />
                                 </div>
                              )}

                              {/* Driver Info */}
                              <div className='flex-1 min-w-0 space-y-2'>
                                 {/* Status Badge */}
                                 <div className='flex items-center gap-2'>
                                    <span
                                       className='inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold text-white shadow-sm'
                                       style={{ backgroundColor: statusColor }}
                                    >
                                       {driver.status.charAt(0).toUpperCase() +
                                          driver.status.slice(1).replace('_', ' ')}
                                    </span>
                                 </div>

                                 {/* Product Name */}
                                 <div>
                                    <p className='text-xs text-gray-500'>Product</p>
                                    <p className='text-sm font-medium text-gray-900'>
                                       {driver.productName}
                                    </p>
                                 </div>

                                 {/* View Profile Button */}
                                 <Button
                                    onClick={() => handleViewProfile(driver)}
                                    variant='outline'
                                    size='sm'
                                    className='w-full text-xs hover:bg-gray-50'
                                 >
                                    <ExternalLink className='mr-1.5 h-3.5 w-3.5' />
                                    View Profile
                                 </Button>
                              </div>
                           </div>
                        </div>
                     );
                  })}
               </div>
            </ScrollArea>
         </DialogContent>
      </Dialog>
   );
};

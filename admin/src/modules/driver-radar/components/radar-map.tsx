'use client';

import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { GoogleMap } from '@react-google-maps/api';
import { MarkerClusterer } from '@googlemaps/markerclusterer';
import { toast } from '@/lib/toast';
import { useRadarMapDrivers } from '../api/queries';
import { DriverInfoPopover } from './driver-info-popover';
import { ClusterDriversPopup } from './cluster-drivers-popup';
import { PollingIndicator } from './polling-indicator';
import { MapControls } from './map-controls';
import { LocationSearch } from './location-search';
import { StatusCircleOverlay } from './status-circle-overlay';
import { createClusterRenderer } from './cluster-renderer';
import CarIcon from '@/assets/car.svg';
import {
   KOCHI_CENTER,
   MAP_CONSTANTS,
   RadarDriver,
   RadarMapBounds,
   Coordinate,
} from '../types/radar';
import { calculateBoundsFromCenter, getCenterFromMap } from '../utils/map-utils';

const mapContainerStyle = {
   width: '100%',
   height: '100%',
};

const mapOptions: google.maps.MapOptions = {
   gestureHandling: 'greedy',
   disableDefaultUI: true,
   zoomControl: true,
   mapTypeControl: false,
   streetViewControl: false,
   fullscreenControl: false,
   minZoom: MAP_CONSTANTS.MIN_ZOOM,
   maxZoom: MAP_CONSTANTS.MAX_ZOOM,
};

export const RadarMap: React.FC = () => {
   const searchParams = useSearchParams();
   const mapRef = useRef<google.maps.Map | null>(null);
   const circleRef = useRef<google.maps.Circle | null>(null);
   const rippleCircleRef = useRef<google.maps.Circle | null>(null);
   const rippleAnimationRef = useRef<number | null>(null);
   const clustererRef = useRef<MarkerClusterer | null>(null);
   const markersRef = useRef<google.maps.Marker[]>([]);
   const [mapBounds, setMapBounds] = useState<RadarMapBounds | null>(null);
   const [mapCenter, setMapCenter] = useState<Coordinate | null>(null);
   const [mapZoom, setMapZoom] = useState<number>(KOCHI_CENTER.zoom);
   const [isPollingEnabled, setIsPollingEnabled] = useState(true);
   const [selectedDriver, setSelectedDriver] = useState<RadarDriver | null>(null);
   const [selectedPosition, setSelectedPosition] = useState<Coordinate | null>(null);
   const [clusteredDrivers, setClusteredDrivers] = useState<RadarDriver[]>([]);
   const [nextUpdate, setNextUpdate] = useState<number>(
      Date.now() + MAP_CONSTANTS.POLLING_INTERVAL_MS
   );
   const isInitialLoadRef = useRef(true);
   const isDragUpdateRef = useRef(false);

   // Read coordinates and zoom from URL params (set when returning from driver details)
   const lat = searchParams.get('lat');
   const lng = searchParams.get('lng');
   const zoom = searchParams.get('zoom');

   // Parse and validate coordinates for initial center
   const initialCenter: Coordinate | null = useMemo(
      () =>
         lat && lng
            ? {
                 lat: parseFloat(lat),
                 lng: parseFloat(lng),
              }
            : null,
      [lat, lng]
   );

   // Parse zoom from URL
   const initialZoom: number | null = useMemo(
      () => (zoom ? parseFloat(zoom) : null),
      [zoom]
   );

   // Update bounds based on current map center
   // This should be called periodically, not on every drag
   const updateBoundsFromMapCenter = useCallback(() => {
      if (!mapRef.current) return;

      const center = getCenterFromMap(mapRef.current);
      if (!center) return;

      // Calculate fixed radius bounds from current center
      const newBounds = calculateBoundsFromCenter(center, MAP_CONSTANTS.MAX_RADIUS_KM);
      setMapBounds(newBounds);
      setMapCenter(center);
   }, []);

   // Trigger ripple animation on the boundary circle
   const triggerRippleAnimation = useCallback(() => {
      if (!mapRef.current || !mapCenter) return;

      // Cancel any existing animation
      if (rippleAnimationRef.current) {
         cancelAnimationFrame(rippleAnimationRef.current);
      }

      // Remove old ripple circle if exists
      if (rippleCircleRef.current) {
         rippleCircleRef.current.setMap(null);
         rippleCircleRef.current = null;
      }

      // Create ripple circle
      const ripple = new google.maps.Circle({
         center: { lat: mapCenter.lat, lng: mapCenter.lng },
         radius: MAP_CONSTANTS.MAX_RADIUS_KM * 1000,
         strokeColor: '#10B981',
         strokeOpacity: 0.7,
         strokeWeight: 3,
         fillColor: '#10B981',
         fillOpacity: 0.15,
         clickable: false,
         draggable: false,
         editable: false,
         map: mapRef.current,
      });

      rippleCircleRef.current = ripple;

      // Animate the ripple
      const startTime = Date.now();
      const duration = 500; // 500ms animation
      const startRadius = MAP_CONSTANTS.MAX_RADIUS_KM * 1000;
      const endRadius = startRadius * 1.05; // Expand by 5%

      const animate = () => {
         const elapsed = Date.now() - startTime;
         const progress = Math.min(elapsed / duration, 1);

         if (rippleCircleRef.current) {
            // Ease out animation
            const easeProgress = 1 - Math.pow(1 - progress, 3);

            // Expand radius
            const currentRadius = startRadius + (endRadius - startRadius) * easeProgress;
            rippleCircleRef.current.setRadius(currentRadius);

            // Fade out opacity
            const currentOpacity = 0.15 * (1 - progress);
            rippleCircleRef.current.setOptions({
               fillOpacity: currentOpacity,
               strokeOpacity: 0.7 * (1 - progress),
            });
         }

         if (progress < 1) {
            rippleAnimationRef.current = requestAnimationFrame(animate);
         } else {
            // Clean up after animation
            if (rippleCircleRef.current) {
               rippleCircleRef.current.setMap(null);
               rippleCircleRef.current = null;
            }
            rippleAnimationRef.current = null;
         }
      };

      rippleAnimationRef.current = requestAnimationFrame(animate);
   }, [mapCenter]);

   // Fetch driver locations with polling
   const { data, isFetching, refetch } = useRadarMapDrivers(mapBounds, isPollingEnabled);

   // Handle driver marker click
   const handleDriverClick = useCallback((driver: RadarDriver) => {
      setSelectedDriver(driver);
      setSelectedPosition({ lat: driver.lat, lng: driver.lng });
   }, []);

   // Initialize map and set initial bounds
   const onLoad = useCallback(
      (map: google.maps.Map) => {
         mapRef.current = map;

         // Use coordinates from URL if available (returning from driver details), otherwise use Kochi default
         const centerToUse = initialCenter || {
            lat: KOCHI_CENTER.lat,
            lng: KOCHI_CENTER.lng,
         };

         // Use zoom from URL if available, otherwise use default
         const zoomToUse = initialZoom !== null ? initialZoom : KOCHI_CENTER.zoom;

         // Set initial center and zoom
         map.setCenter(centerToUse);
         map.setZoom(zoomToUse);
         setMapZoom(zoomToUse);

         // Set initial bounds and center
         const initialBounds = calculateBoundsFromCenter(centerToUse, MAP_CONSTANTS.MAX_RADIUS_KM);
         setMapBounds(initialBounds);
         setMapCenter(centerToUse);

         // Initialize MarkerClusterer
         clustererRef.current = new MarkerClusterer({
            map,
            renderer: createClusterRenderer(),
            // Click on cluster zooms in to reveal individual markers
            onClusterClick: (_event, cluster, map) => {
               const currentZoom = map.getZoom();

               // If at max zoom, show popup with driver list instead of zooming
               if (currentZoom && currentZoom >= MAP_CONSTANTS.MAX_ZOOM) {
                  // Extract driver data from cluster markers
                  const drivers = cluster.markers
                     .map(marker => (marker as any).driverData as RadarDriver)
                     .filter(Boolean); // Filter out any undefined

                  // Open popup with driver list
                  setClusteredDrivers(drivers);
                  return;
               }

               // Otherwise, zoom in as usual
               const bounds = cluster.bounds;
               if (bounds) {
                  map.fitBounds(bounds);
                  // Wait for fitBounds to complete, then reduce zoom by 1 for context
                  setTimeout(() => {
                     const newZoom = map.getZoom();
                     if (newZoom) {
                        map.setZoom(newZoom - 1);
                     }
                  }, 100);
               }
            },
         });
      },
      [initialCenter, initialZoom]
   );

   // Create a native marker for a driver
   const createDriverMarker = useCallback(
      (driver: RadarDriver): google.maps.Marker => {
         // Determine marker icon
         const markerIcon: google.maps.Icon = driver.productIcon
            ? {
                 url: driver.productIcon as string,
                 scaledSize: new google.maps.Size(28, 28),
                 anchor: new google.maps.Point(14, 14),
              }
            : {
                 url: typeof CarIcon === 'string' ? CarIcon : CarIcon.src,
                 scaledSize: new google.maps.Size(32, 32),
                 anchor: new google.maps.Point(16, 16),
              };

         // Create marker
         const marker = new google.maps.Marker({
            position: { lat: driver.lat, lng: driver.lng },
            icon: markerIcon,
            title: `${driver.productName} - ${driver.status}`,
         });

         // Add click listener
         marker.addListener('click', () => {
            handleDriverClick(driver);
         });

         // Store driver data on marker for later reference
         (marker as any).driverData = driver;

         return marker;
      },
      [handleDriverClick]
   );

   // Handle zoom changes - enforce min zoom and warn user
   const handleZoomChanged = useCallback(() => {
      if (!mapRef.current) return;

      const currentZoom = mapRef.current.getZoom();
      if (currentZoom !== undefined) {
         if (currentZoom < MAP_CONSTANTS.MIN_ZOOM) {
            toast.error('Zoom limit reached. Maximum area is restricted to reduce server load.');
            mapRef.current.setZoom(MAP_CONSTANTS.MIN_ZOOM);
            setMapZoom(MAP_CONSTANTS.MIN_ZOOM);
         } else {
            setMapZoom(currentZoom);
         }

         // Force clusterer to recalculate and update cluster counts on zoom change
         if (clustererRef.current) {
            clustererRef.current.render();
         }
      }
   }, []);

   // Handle map drag/pan - update bounds to follow current view
   const handleDragEnd = useCallback(() => {
      isDragUpdateRef.current = true; // Mark this as a drag update (no ripple)
      updateBoundsFromMapCenter();
   }, [updateBoundsFromMapCenter]);

   // Manage circle overlay lifecycle
   useEffect(() => {
      if (!mapRef.current || !mapCenter) return;

      // Remove old circle if it exists
      if (circleRef.current) {
         circleRef.current.setMap(null);
         circleRef.current = null;
      }

      // Create new circle with current center and radius
      const circle = new google.maps.Circle({
         center: { lat: mapCenter.lat, lng: mapCenter.lng },
         radius: MAP_CONSTANTS.MAX_RADIUS_KM * 1000, // Convert km to meters
         strokeColor: '#10B981',
         strokeOpacity: 0.7,
         strokeWeight: 2,
         fillColor: '#10B981',
         fillOpacity: 0.08,
         clickable: false,
         draggable: false,
         editable: false,
         map: mapRef.current,
      });

      circleRef.current = circle;

      // Cleanup on unmount
      return () => {
         if (circleRef.current) {
            circleRef.current.setMap(null);
            circleRef.current = null;
         }
      };
   }, [mapCenter]);

   // Update last updated timestamp and bounds when data changes
   useEffect(() => {
      if (data) {
         setNextUpdate(Date.now() + MAP_CONSTANTS.POLLING_INTERVAL_MS);

         // Update bounds from current map center for next poll
         // ONLY if this is NOT from a drag (drag already updated bounds)
         if (!isDragUpdateRef.current) {
            updateBoundsFromMapCenter();

            // Trigger ripple animation ONLY on automatic 5-second refresh
            if (!isInitialLoadRef.current) {
               triggerRippleAnimation();
            } else {
               isInitialLoadRef.current = false;
            }
         }

         // Reset the drag flag after processing
         isDragUpdateRef.current = false;
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
   }, [data, updateBoundsFromMapCenter]);

   // Handle popover close
   const handlePopoverClose = useCallback(() => {
      setSelectedDriver(null);
      setSelectedPosition(null);
   }, []);

   // Handle map drag start - close popover and cluster popup when user starts dragging
   const handleDragStart = useCallback(() => {
      if (selectedDriver) {
         handlePopoverClose();
      }
      if (clusteredDrivers.length > 0) {
         setClusteredDrivers([]);
      }
   }, [selectedDriver, clusteredDrivers.length, handlePopoverClose]);

   // Manual refresh
   const handleManualRefresh = useCallback(() => {
      // Update bounds from current map position before refetching
      updateBoundsFromMapCenter();
      refetch();
      setNextUpdate(Date.now() + MAP_CONSTANTS.POLLING_INTERVAL_MS);
      toast.success('Driver locations refreshed');
      // Trigger ripple animation on manual refresh
      if (!isInitialLoadRef.current) {
         triggerRippleAnimation();
      }
   }, [refetch, updateBoundsFromMapCenter, triggerRippleAnimation]);

   // Toggle polling
   const handleTogglePolling = useCallback(() => {
      setIsPollingEnabled(prev => {
         const newState = !prev;
         toast.info(newState ? 'Auto-refresh enabled' : 'Auto-refresh paused');
         if (newState) {
            setNextUpdate(Date.now() + MAP_CONSTANTS.POLLING_INTERVAL_MS);
         }
         return newState;
      });
   }, []);

   // Handle location search
   const handleLocationSelect = useCallback(
      (place: google.maps.places.PlaceResult) => {
         if (!mapRef.current || !place.geometry?.location) return;

         const location = place.geometry.location;
         const lat = location.lat();
         const lng = location.lng();

         // Pan to the selected location
         mapRef.current.panTo({ lat, lng });

         // Set zoom level based on the type of place
         if (place.geometry.viewport) {
            mapRef.current.fitBounds(place.geometry.viewport);
         } else {
            mapRef.current.setZoom(15);
         }

         // Update bounds for the new center
         updateBoundsFromMapCenter();

         toast.success(`Moved to ${place.formatted_address || place.name}`);
      },
      [updateBoundsFromMapCenter]
   );

   // Pause polling when tab is inactive
   useEffect(() => {
      const handleVisibilityChange = () => {
         if (document.visibilityState === 'hidden') {
            // Tab is inactive - polling is automatically paused by React Query
         } else {
            // Tab is active again - trigger a refresh
            if (isPollingEnabled) {
               refetch();
               setNextUpdate(Date.now() + MAP_CONSTANTS.POLLING_INTERVAL_MS);
            }
         }
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);
      return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
   }, [isPollingEnabled, refetch]);

   // Handle escape key to close popover
   useEffect(() => {
      const handleEscapeKey = (event: KeyboardEvent) => {
         if (event.key === 'Escape' && selectedDriver) {
            handlePopoverClose();
         }
      };

      document.addEventListener('keydown', handleEscapeKey);
      return () => document.removeEventListener('keydown', handleEscapeKey);
   }, [selectedDriver, handlePopoverClose]);

   // Update markers when drivers data changes
   useEffect(() => {
      if (!mapRef.current || !clustererRef.current) return;

      // Clear old markers
      markersRef.current.forEach(marker => {
         google.maps.event.clearInstanceListeners(marker);
         marker.setMap(null);
      });
      markersRef.current = [];

      // Create new markers for all drivers
      const drivers = data?.data?.drivers || [];
      const newMarkers = drivers.map(driver => createDriverMarker(driver));

      // Update clusterer with new markers
      clustererRef.current.clearMarkers();
      clustererRef.current.addMarkers(newMarkers);

      // Store markers for cleanup
      markersRef.current = newMarkers;
   }, [data, createDriverMarker]);

   // Cleanup ripple animation on unmount
   useEffect(() => {
      return () => {
         if (rippleAnimationRef.current) {
            cancelAnimationFrame(rippleAnimationRef.current);
         }
         if (rippleCircleRef.current) {
            rippleCircleRef.current.setMap(null);
         }
      };
   }, []);

   // Cleanup markers and clusterer on unmount
   useEffect(() => {
      return () => {
         // Clear all markers
         markersRef.current.forEach(marker => {
            google.maps.event.clearInstanceListeners(marker);
            marker.setMap(null);
         });
         markersRef.current = [];

         // Clear clusterer
         if (clustererRef.current) {
            clustererRef.current.clearMarkers();
            clustererRef.current = null;
         }
      };
   }, []);

   const drivers = data?.data?.drivers || [];

   // Handle map click to close popover and cluster popup
   const handleMapClick = useCallback(() => {
      if (selectedDriver) {
         handlePopoverClose();
      }
      if (clusteredDrivers.length > 0) {
         setClusteredDrivers([]);
      }
   }, [selectedDriver, clusteredDrivers.length, handlePopoverClose]);

   return (
      <div className='relative w-full h-full'>
         <GoogleMap
            mapContainerStyle={mapContainerStyle}
            onLoad={onLoad}
            onZoomChanged={handleZoomChanged}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            onClick={handleMapClick}
            options={mapOptions}
         >
            {/* Render status circle overlays for each driver */}
            {drivers.map(driver => (
               <StatusCircleOverlay
                  key={`circle-${driver.id}`}
                  position={{ lat: driver.lat, lng: driver.lng }}
                  status={driver.status}
                  map={mapRef.current}
               />
            ))}

            {/* Driver markers are managed by MarkerClusterer */}

            {/* Driver info popover */}
            <DriverInfoPopover
               driver={selectedDriver}
               isOpen={!!selectedDriver}
               onClose={handlePopoverClose}
               position={selectedPosition}
               currentZoom={mapZoom}
            />
         </GoogleMap>

         {/* Cluster drivers popup - shown at max zoom when clicking clusters */}
         <ClusterDriversPopup
            drivers={clusteredDrivers}
            isOpen={clusteredDrivers.length > 0}
            onClose={() => setClusteredDrivers([])}
            currentZoom={mapZoom}
         />

         {/* Location Search */}
         <div className='absolute top-4 left-1/2 -translate-x-1/2 z-10'>
            <LocationSearch onLocationSelect={handleLocationSelect} />
         </div>

         {/* Polling indicator */}
         <PollingIndicator
            isFetching={isFetching}
            nextUpdate={nextUpdate}
            onManualRefresh={handleManualRefresh}
            isPaused={!isPollingEnabled}
            onTogglePolling={handleTogglePolling}
         />

         {/* Map controls */}
         <MapControls drivers={drivers} />
      </div>
   );
};

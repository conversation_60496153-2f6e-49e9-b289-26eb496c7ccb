'use client';

import React from 'react';
import { RadarMap } from '../components/radar-map';

export function DriverRadarPage() {
  return (
    <div className='flex flex-col h-[calc(100vh-4rem)] w-full'>
      {/* Page Header */}
      <div className='bg-white border-b border-gray-200 px-6 py-4'>
        <div className='flex justify-between items-center'>
          <div>
            <h1 className='text-2xl font-semibold text-gray-900'>Driver Radar</h1>
            <p className='text-sm text-gray-500 mt-1'>
              Real-time driver location monitoring with 5-second updates
            </p>
          </div>
          <div className='flex items-center gap-2'>
            <span className='inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800'>
              <span className='w-2 h-2 bg-green-600 rounded-full mr-2 animate-pulse' />
              Live
            </span>
          </div>
        </div>
      </div>

      {/* Map Container */}
      <div className='flex-1 relative p-4'>
        <div className='w-full h-full rounded-lg overflow-hidden shadow-sm'>
          <RadarMap />
        </div>
      </div>
    </div>
  );
}

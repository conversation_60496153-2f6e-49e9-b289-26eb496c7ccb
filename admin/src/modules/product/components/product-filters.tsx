'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { Spinner } from '@/components/ui/spinner';
import { Search, X, Package, Settings } from 'lucide-react';
import { useEffect, useState, useRef } from 'react';
import { useListVehicleTypes, useListProductServices } from '../api/queries';

export interface ProductFiltersProps {
   onSearchChange: (search: string) => void;
   onVehicleTypeChange: (vehicleTypeId: string | undefined) => void;
   onProductServiceChange: (productServiceId: string | undefined) => void;
   onStatusChange: (isEnabled: string | undefined) => void;
   search: string;
   vehicleTypeId: string | undefined;
   productServiceId: string | undefined;
   isEnabled: string | undefined;
}

export function ProductFilters({
   onSearchChange,
   onVehicleTypeChange,
   onProductServiceChange,
   onStatusChange,
   search,
   vehicleTypeId,
   productServiceId,
   isEnabled,
   isLoading,
}: ProductFiltersProps & { isLoading?: boolean }) {
   const [searchValue, setSearchValue] = useState(search || '');
   const [isSearching, setIsSearching] = useState(false);
   const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

   // Fetch vehicle types and product services for the dropdowns
   const vehicleTypesQuery = useListVehicleTypes();
   const productServicesQuery = useListProductServices();

   // Update local search state when props change
   useEffect(() => {
      setSearchValue(search || '');
   }, [search]);

   // Clean up timeouts on unmount
   useEffect(() => {
      return () => {
         if (searchTimeoutRef.current) {
            clearTimeout(searchTimeoutRef.current);
         }
      };
   }, []);

   // Handle search input with debounce
   const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setSearchValue(value);

      // Show searching indicator
      setIsSearching(true);

      // Clear any existing timeout
      if (searchTimeoutRef.current) {
         clearTimeout(searchTimeoutRef.current);
      }

      // Set a new timeout
      searchTimeoutRef.current = setTimeout(() => {
         onSearchChange(value);
         searchTimeoutRef.current = null;
         setIsSearching(false);
      }, 500); // 500ms debounce time
   };

   // Clear all filters
   const handleClearFilters = () => {
      // Clear any pending timeouts
      if (searchTimeoutRef.current) {
         clearTimeout(searchTimeoutRef.current);
         searchTimeoutRef.current = null;
      }

      setIsSearching(false);
      setSearchValue('');
      onSearchChange('');
      onVehicleTypeChange(undefined);
      onProductServiceChange(undefined);
      onStatusChange(undefined);
   };

   // Check if any filters are active
   const hasActiveFilters = !!search || !!vehicleTypeId || !!productServiceId || !!isEnabled;

   return (
      <div className='flex flex-col space-y-4 mb-4'>
         <div className='flex justify-between items-center'>
            {/* Left container - Search field */}
            <div className='flex gap-2 items-center'>
               {/* Product Name Search */}
               <div className='relative w-[200px]'>
                  <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-gray-500' />
                  <Input
                     placeholder='Search products...'
                     value={searchValue}
                     onChange={handleSearchChange}
                     className='pl-8'
                  />
                  {(isSearching || (isLoading && searchValue)) && (
                     <div className='absolute right-2.5 top-2.5 text-gray-500'>
                        <Spinner className='h-4 w-4 text-primary' />
                     </div>
                  )}
                  {searchValue && !isSearching && !isLoading && (
                     <button
                        onClick={() => {
                           if (searchTimeoutRef.current) {
                              clearTimeout(searchTimeoutRef.current);
                              searchTimeoutRef.current = null;
                           }
                           setIsSearching(false);
                           setSearchValue('');
                           onSearchChange('');
                        }}
                        className='absolute right-2.5 top-2.5 text-gray-500 hover:text-gray-700'
                     >
                        <X className='h-4 w-4' />
                     </button>
                  )}
               </div>
            </div>

            {/* Right container - Status, Vehicle Type, Product Service filters and Clear button */}
            <div className='flex gap-2 items-center'>
               {/* Status Filter */}
               <Select
                  value={isEnabled || 'all'}
                  onValueChange={value => onStatusChange(value === 'all' ? undefined : value)}
               >
                  <SelectTrigger className='w-auto min-w-[100px] cursor-pointer'>
                     <Settings className='h-4 w-4 mr-1' />
                     <SelectValue placeholder='All Status' />
                  </SelectTrigger>
                  <SelectContent>
                     <SelectItem value='all'>All Status</SelectItem>
                     <SelectItem value='true'>Enabled</SelectItem>
                     <SelectItem value='false'>Disabled</SelectItem>
                  </SelectContent>
               </Select>

               {/* Vehicle Type Filter */}
               <Select
                  value={vehicleTypeId || 'all'}
                  onValueChange={value => onVehicleTypeChange(value === 'all' ? undefined : value)}
               >
                  <SelectTrigger className='w-auto min-w-[140px] cursor-pointer'>
                     <Package className='h-4 w-4 mr-1' />
                     <SelectValue placeholder='All Vehicle Types' />
                  </SelectTrigger>
                  <SelectContent>
                     <SelectItem value='all'>All Vehicle Types</SelectItem>
                     {vehicleTypesQuery.data?.data?.map(vehicleType => (
                        <SelectItem key={vehicleType.id} value={vehicleType.id}>
                           {vehicleType.name}
                        </SelectItem>
                     ))}
                  </SelectContent>
               </Select>

               {/* Product Service Filter */}
               <Select
                  value={productServiceId || 'all'}
                  onValueChange={value => onProductServiceChange(value === 'all' ? undefined : value)}
               >
                  <SelectTrigger className='w-auto min-w-[150px] cursor-pointer'>
                     <SelectValue placeholder='All Services' />
                  </SelectTrigger>
                  <SelectContent>
                     <SelectItem value='all'>All Services</SelectItem>
                     {productServicesQuery.data?.data?.map(service => (
                        <SelectItem key={service.id} value={service.id}>
                           {service.name}
                        </SelectItem>
                     ))}
                  </SelectContent>
               </Select>
            </div>
         </div>

         {/* Clear Filters Button - Below the filters */}
         {hasActiveFilters && (
            <div className='flex justify-end'>
               <Button
                  variant='outline'
                  size='sm'
                  onClick={handleClearFilters}
                  className='text-gray-700 border-gray-300'
               >
                  Clear Filters
               </Button>
            </div>
         )}
      </div>
   );
}
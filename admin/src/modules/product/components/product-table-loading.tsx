'use client';

import { Skeleton } from '@/components/ui/skeleton';

export function ProductTableLoading() {
  return (
    <div className='space-y-2'>
      <div className='rounded-md border'>
        <div className='overflow-x-auto'>
          <table className='w-full'>
            <thead>
              <tr className='border-b bg-gray-50'>
                <th className='h-11 px-4 text-left align-middle' style={{ width: 200 }}>
                  <div className='text-left font-semibold text-gray-600 text-sm'>Name</div>
                </th>
                <th className='h-11 px-4 text-left align-middle' style={{ width: 150 }}>
                  <div className='text-left font-semibold text-gray-600 text-sm'>Vehicle Type</div>
                </th>
                <th className='h-11 px-4 text-left align-middle' style={{ width: 150 }}>
                  <div className='text-left font-semibold text-gray-600 text-sm'>Service</div>
                </th>
                <th className='h-11 px-4 text-left align-middle' style={{ width: 250 }}>
                  <div className='text-left font-semibold text-gray-600 text-sm'>Description</div>
                </th>
                <th className='h-11 px-4 text-center align-middle' style={{ width: 80 }}>
                  <div className='text-center font-semibold text-gray-600 text-sm'>Icon</div>
                </th>
                <th className='h-11 px-4 text-center align-middle' style={{ width: 100 }}>
                  <div className='text-center font-semibold text-gray-600 text-sm'>Status</div>
                </th>
                <th className='h-11 px-4 text-center align-middle' style={{ width: 200 }}>
                  <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>
                </th>
              </tr>
            </thead>
            <tbody>
              {Array.from({ length: 6 }).map((_, index) => (
                <tr key={index} className='border-b'>
                  {/* Name */}
                  <td className='px-4 py-3 align-middle'>
                    <Skeleton className='h-4 w-32' />
                  </td>
                  {/* Vehicle Type */}
                  <td className='px-4 py-3 align-middle'>
                    <Skeleton className='h-4 w-24' />
                  </td>
                  {/* Service */}
                  <td className='px-4 py-3 align-middle'>
                    <Skeleton className='h-4 w-28' />
                  </td>
                  {/* Description */}
                  <td className='px-4 py-3 align-middle'>
                    <Skeleton className='h-4 w-48' />
                  </td>
                  {/* Icon */}
                  <td className='px-4 py-3 align-middle'>
                    <div className='flex justify-center'>
                      <Skeleton className='h-8 w-8 rounded' />
                    </div>
                  </td>
                  {/* Status */}
                  <td className='px-4 py-3 align-middle'>
                    <div className='flex justify-center'>
                      <Skeleton className='h-6 w-16 rounded-full' />
                    </div>
                  </td>
                  {/* Actions */}
                  <td className='px-4 py-3 align-middle'>
                    <div className='flex justify-center gap-1'>
                      <Skeleton className='h-8 w-12 rounded-md' />
                      <Skeleton className='h-8 w-20 rounded-md' />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { Spinner } from '@/components/ui/spinner';
import { Textarea } from '@/components/ui/textarea';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { FileUploadSection } from '@/components/file-upload-section';
import { toast } from '@/lib/toast';
import { useFileUpload } from '@/hooks/use-file-upload';
import { useFileUpload as useFileUploadMutation } from '@/lib/file-upload-api';
import { zodResolver } from '@hookform/resolvers/zod';
import { Plus } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useQueryClient } from '@tanstack/react-query';
import * as z from 'zod';
import { useCreateProduct, useUpdateProduct } from '../api/mutations';
import { useGetProduct, useListVehicleTypes, useListProductServices } from '../api/queries';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';

const productSchema = z.object({
   vehicleTypeId: z.string().min(1, 'Vehicle type is required'),
   productServiceId: z.string().min(1, 'Product service is required'),
   name: z
      .string()
      .min(1, 'Product name is required')
      .min(2, 'Product name must be at least 2 characters')
      .max(100, 'Product name must not exceed 100 characters')
      .regex(/^[a-zA-Z0-9\s]+$/, 'Product name can only include letters, numbers, and spaces'),
   description: z.string().max(500, 'Description must not exceed 500 characters').optional(),
   passengerLimit: z
      .number()
      .min(2, 'Passenger limit must be at least 2')
      .max(8, 'Passenger limit cannot exceed 8'),
});

type ProductFormValues = z.infer<typeof productSchema>;

interface ProductModalProps {
   productId?: string | null;
   isOpen?: boolean;
   onClose?: () => void;
   mode?: 'create' | 'edit';
}

export const ProductModal = ({
   productId,
   isOpen,
   onClose,
   mode = 'create',
}: ProductModalProps) => {
   const [internalOpen, setInternalOpen] = useState(false);
   const [fileError, setFileError] = useState<string>('');
   const [shouldRemoveIcon, setShouldRemoveIcon] = useState(false);
   const [selectKey, setSelectKey] = useState(0);
   const createProductMutation = useCreateProduct();
   const updateProductMutation = useUpdateProduct();
   const productQuery = useGetProduct(productId || null);
   const vehicleTypesQuery = useListVehicleTypes();
   const productServicesQuery = useListProductServices();
   const fileUploadMutation = useFileUploadMutation({ isPublic: true });
   const queryClient = useQueryClient();
   const { withPermission } = useRoleBasedAccess();

   // Use external open state if provided, otherwise use internal state
   const modalOpen = isOpen !== undefined ? isOpen : internalOpen;
   const setModalOpen = onClose ? (open: boolean) => !open && onClose() : setInternalOpen;

   const form = useForm<ProductFormValues>({
      resolver: zodResolver(productSchema),
      defaultValues: {
         vehicleTypeId: '',
         productServiceId: '',
         name: '',
         description: '',
         passengerLimit: 4,
      },
   });

   const {
      formState: { errors },
      reset,
      control,
   } = form;

   const maxSize = 10 * 1024 * 1024; // 10MB
   const acceptedTypes = '.jpg,.jpeg,.png,.svg';

   const [
      { files, isDragging, errors: uploadErrors },
      {
         handleDragEnter,
         handleDragLeave,
         handleDragOver,
         handleDrop,
         openFileDialog,
         removeFile,
         getInputProps,
         clearFiles,
         clearErrors,
         addFiles,
         handleFileChange,
      },
   ] = useFileUpload({
      maxSize,
      accept: acceptedTypes,
      multiple: false,
   });

   const file = files[0];

   const isDataLoading =
      productQuery.isLoading || vehicleTypesQuery.isLoading || productServicesQuery.isLoading;

   // Reset form when productId changes or modal opens
   useEffect(() => {
      if (mode === 'edit' && productQuery.data?.data && modalOpen && !isDataLoading) {
         const product = productQuery.data.data;
         reset({
            vehicleTypeId: product.vehicleTypeId,
            productServiceId: product.productServiceId,
            name: product.name,
            description: product.description || '',
            passengerLimit: product.passengerLimit,
         });
         // Force re-render of Select components to sync with new values
         setSelectKey(prev => prev + 1);
      } else if (mode === 'create') {
         reset({
            vehicleTypeId: '',
            productServiceId: '',
            name: '',
            description: '',
            passengerLimit: 4,
         });
      }
   }, [productQuery.data, reset, mode, modalOpen, isDataLoading]);

   // Clear files and errors when modal opens
   useEffect(() => {
      if (modalOpen) {
         clearFiles();
         clearErrors();
         setFileError('');
         setShouldRemoveIcon(false);
      }
   }, [modalOpen, clearFiles, clearErrors]);

   // Clear file error when file is selected
   useEffect(() => {
      if (file) {
         setFileError('');
         setShouldRemoveIcon(false); // Reset remove flag when new file is selected
      }
   }, [file]);

   const onSubmit = async (data: ProductFormValues) => {
      setFileError('');

      try {
         let iconUrl: string | null | undefined;

         if (file) {
            const uploadResponse = await fileUploadMutation.mutateAsync(file.file as File);
            iconUrl = uploadResponse.data.url;
         } else if (shouldRemoveIcon) {
            iconUrl = null;
         } else if (mode === 'edit' && productQuery.data?.data?.icon) {
            iconUrl = productQuery.data.data.icon;
         }

         const payload: any = {
            vehicleTypeId: data.vehicleTypeId,
            productServiceId: data.productServiceId,
            name: data.name,
            description: data.description || undefined,
            passengerLimit: data.passengerLimit,
         };

         if (file || shouldRemoveIcon) {
            payload.icon = iconUrl;
         }

         if (mode === 'create') {
            createProductMutation.mutate(payload, {
               onSuccess: () => {
                  toast.success('Product created successfully');
                  handleClose();
                  queryClient.invalidateQueries({ queryKey: ['products'] });
               },
            });
         } else if (mode === 'edit' && productId) {
            updateProductMutation.mutate(
               { id: productId, ...payload },
               {
                  onSuccess: () => {
                     toast.success('Product updated successfully');
                     handleClose();
                     queryClient.invalidateQueries({ queryKey: ['products'] });
                     queryClient.invalidateQueries({
                        queryKey: ['product', productId],
                     });
                  },
               }
            );
         }
      } catch (error: any) {
         console.error('Submit error:', error);
      }
   };

   const handleClose = () => {
      setModalOpen(false);
      reset();
      clearFiles();
      clearErrors();
      setFileError('');
      setShouldRemoveIcon(false);
   };

   const isLoading =
      mode === 'create'
         ? createProductMutation.isPending || fileUploadMutation.isPending
         : updateProductMutation.isPending || fileUploadMutation.isPending;

   // Show loading state for edit mode
   if (mode === 'edit' && productQuery.isLoading) {
      return (
         <Dialog open={modalOpen} onOpenChange={setModalOpen}>
            <DialogContent className='max-w-md'>
               <DialogHeader>
                  <DialogTitle>Loading...</DialogTitle>
                  <DialogDescription>Please wait while we load the product data.</DialogDescription>
               </DialogHeader>
               <div className='flex items-center justify-center py-8'>
                  <Spinner className='h-8 w-8' />
               </div>
            </DialogContent>
         </Dialog>
      );
   }

   // Show error state for edit mode
   if (mode === 'edit' && productQuery.error) {
      return (
         <Dialog open={modalOpen} onOpenChange={setModalOpen}>
            <DialogContent className='max-w-md'>
               <DialogHeader>
                  <DialogTitle>Error</DialogTitle>
                  <DialogDescription>Failed to load product data.</DialogDescription>
               </DialogHeader>
               <div className='text-center py-8'>
                  <p className='text-red-600'>Failed to load product data</p>
                  <Button onClick={handleClose} className='mt-4'>
                     Close
                  </Button>
               </div>
            </DialogContent>
         </Dialog>
      );
   }

   const content = (
      <DialogContent
         onInteractOutside={e => {
            e.preventDefault();
         }}
         className='max-w-md max-h-[90vh] overflow-y-auto'
      >
         <DialogHeader>
            <DialogTitle>{mode === 'create' ? 'Create New Product' : 'Edit Product'}</DialogTitle>
            <DialogDescription>
               {mode === 'create'
                  ? 'Add a new product to the system'
                  : 'Update the product information'}
            </DialogDescription>
         </DialogHeader>

         <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4 py-4'>
            <div className='grid grid-cols-2 gap-3'>
               <div className='flex flex-col gap-2'>
                  <Label htmlFor='vehicleTypeId'>Vehicle Type *</Label>
                  <Controller
                     control={control}
                     name='vehicleTypeId'
                     render={({ field }) => (
                        <Select
                           key={`vehicleType-${selectKey}`}
                           value={field.value}
                           onValueChange={field.onChange}
                        >
                           <SelectTrigger className='w-full'>
                              <SelectValue placeholder='Select vehicle type' />
                           </SelectTrigger>
                           <SelectContent>
                              {vehicleTypesQuery.data?.data?.map(vehicleType => (
                                 <SelectItem key={vehicleType.id} value={vehicleType.id}>
                                    {vehicleType.name}
                                 </SelectItem>
                              ))}
                           </SelectContent>
                        </Select>
                     )}
                  />
                  {errors.vehicleTypeId && <ErrorMessage error={errors.vehicleTypeId} />}
               </div>

               <div className='flex flex-col gap-2'>
                  <Label htmlFor='productServiceId'>Product Service *</Label>
                  <Controller
                     control={control}
                     name='productServiceId'
                     render={({ field }) => (
                        <Select
                           key={`productService-${selectKey}`}
                           value={field.value}
                           onValueChange={field.onChange}
                        >
                           <SelectTrigger className='w-full'>
                              <SelectValue placeholder='Select product service' />
                           </SelectTrigger>
                           <SelectContent>
                              {productServicesQuery.data?.data?.map(productService => (
                                 <SelectItem key={productService.id} value={productService.id}>
                                    {productService.name}
                                 </SelectItem>
                              ))}
                           </SelectContent>
                        </Select>
                     )}
                  />
                  {errors.productServiceId && <ErrorMessage error={errors.productServiceId} />}
               </div>
            </div>

            <div className='flex flex-col gap-2'>
               <Label htmlFor='name'>Product Name *</Label>
               <Controller
                  control={control}
                  name='name'
                  render={({ field }) => (
                     <Input
                        id='name'
                        placeholder='e.g. Economy Ride, Premium Service'
                        {...field}
                        className='w-full'
                     />
                  )}
               />
               {errors.name && <ErrorMessage error={errors.name} />}
            </div>

            <div className='flex flex-col gap-2'>
               <Label htmlFor='description'>Description</Label>
               <Controller
                  control={control}
                  name='description'
                  render={({ field }) => (
                     <Textarea
                        id='description'
                        placeholder='Enter product description'
                        {...field}
                        className='w-full min-h-[80px] resize-none'
                     />
                  )}
               />
               {errors.description && <ErrorMessage error={errors.description} />}
            </div>

            <div className='flex flex-col gap-2'>
               <Label htmlFor='passengerLimit'>Passenger Limit *</Label>
               <Controller
                  control={control}
                  name='passengerLimit'
                  render={({ field }) => (
                     <Select
                        key={`passengerLimit-${selectKey}`}
                        value={field.value?.toString()}
                        onValueChange={value => field.onChange(parseInt(value, 10))}
                     >
                        <SelectTrigger className='w-full'>
                           <SelectValue placeholder='Select passenger limit' />
                        </SelectTrigger>
                        <SelectContent>
                           {[2, 3, 4, 5, 6, 7, 8].map(limit => (
                              <SelectItem key={limit} value={limit.toString()}>
                                 {limit} passengers
                              </SelectItem>
                           ))}
                        </SelectContent>
                     </Select>
                  )}
               />
               {errors.passengerLimit && <ErrorMessage error={errors.passengerLimit} />}
            </div>

            {/* Icon Upload Section */}
            <FileUploadSection
               label='Product Icon'
               fieldName='icon'
               maxSize={maxSize}
               existingFileUrl={mode === 'edit' ? productQuery.data?.data?.icon : undefined}
               fileError={fileError}
               fileUploadState={{ files, isDragging, errors: uploadErrors }}
               fileUploadActions={{
                  handleDragEnter,
                  handleDragLeave,
                  handleDragOver,
                  handleDrop,
                  openFileDialog,
                  removeFile,
                  getInputProps,
                  addFiles,
                  clearFiles,
                  clearErrors,
                  handleFileChange,
               }}
               // Enhanced removal functionality
               onRemoveExisting={() => setShouldRemoveIcon(true)}
               isExistingFileMarkedForRemoval={shouldRemoveIcon}
               showRemovalIndicator={true}
            />

            <div className='flex gap-3 pt-4'>
               <Button type='button' variant='outline' onClick={handleClose} className='flex-1'>
                  Cancel
               </Button>
               <Button type='submit' disabled={isLoading} className='flex-1'>
                  {isLoading ? (
                     <>
                        {mode === 'create' ? 'Creating...' : 'Updating...'}
                        <Spinner className='ml-2 h-4 w-4' />
                     </>
                  ) : mode === 'create' ? (
                     'Create Product'
                  ) : (
                     'Update Product'
                  )}
               </Button>
            </div>
         </form>
      </DialogContent>
   );

   // For create mode, return button and dialog separately
   if (mode === 'create' && isOpen === undefined) {
      return (
         <>
            <Button
               className='cursor-pointer'
               variant='outline'
               onClick={() =>
                  withPermission(RBAC_PERMISSIONS.PRODUCT.CREATE, () => setModalOpen(true))
               }
            >
               <Plus />
               Add Product
            </Button>
            <Dialog open={modalOpen} onOpenChange={setModalOpen}>
               {content}
            </Dialog>
         </>
      );
   }

   // For edit mode or controlled create mode
   return (
      <Dialog open={modalOpen} onOpenChange={setModalOpen}>
         {content}
      </Dialog>
   );
};

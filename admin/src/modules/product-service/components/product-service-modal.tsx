'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Spinner } from '@/components/ui/spinner';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { FileUploadSection } from '@/components/file-upload-section';
import { toast } from '@/lib/toast';
import { useFileUpload } from '@/hooks/use-file-upload';
import { useFileUpload as useFileUploadMutation } from '@/lib/file-upload-api';
import { zodResolver } from '@hookform/resolvers/zod';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useQueryClient } from '@tanstack/react-query';
import * as z from 'zod';
import { useUpdateProductService } from '../api/mutations';
import { useGetProductService } from '../api/queries';

const productServiceSchema = z.object({
   name: z
      .string()
      .min(1, 'Product service name is required')
      .min(2, 'Product service name must be at least 2 characters')
      .max(100, 'Product service name must not exceed 100 characters'),
   description: z.string().max(500, 'Description must not exceed 500 characters').optional(),
});

type ProductServiceFormValues = z.infer<typeof productServiceSchema>;

interface ProductServiceModalProps {
   productServiceId?: string | null;
   isOpen?: boolean;
   onClose?: () => void;
}

export const ProductServiceModal = ({
   productServiceId,
   isOpen,
   onClose,
}: ProductServiceModalProps) => {
   const [fileError, setFileError] = useState<string>('');
   const [shouldRemoveIcon, setShouldRemoveIcon] = useState(false);
   const updateProductServiceMutation = useUpdateProductService();
   const productServiceQuery = useGetProductService(productServiceId || null);
   const fileUploadMutation = useFileUploadMutation({ isPublic: true });
   const queryClient = useQueryClient();

   const form = useForm<ProductServiceFormValues>({
      resolver: zodResolver(productServiceSchema),
      defaultValues: {
         name: '',
         description: '',
      },
   });

   const {
      formState: { errors },
      reset,
      control,
   } = form;

   const maxSize = 10 * 1024 * 1024; // 10MB
   const acceptedTypes = '.jpg,.jpeg,.png';

   const [
      { files, isDragging, errors: uploadErrors },
      {
         handleDragEnter,
         handleDragLeave,
         handleDragOver,
         handleDrop,
         openFileDialog,
         removeFile,
         getInputProps,
         clearFiles,
         clearErrors,
         addFiles,
         handleFileChange,
      },
   ] = useFileUpload({
      maxSize,
      accept: acceptedTypes,
      multiple: false,
   });

   const file = files[0];

   // Reset form when productServiceId changes or modal opens
   useEffect(() => {
      if (productServiceQuery.data?.data) {
         const productService = productServiceQuery.data.data;
         reset({
            name: productService.name,
            description: productService.description || '',
         });
      }
   }, [productServiceQuery.data, reset]);

   // Clear files and errors when modal opens
   useEffect(() => {
      if (isOpen) {
         clearFiles();
         clearErrors();
         setFileError('');
         setShouldRemoveIcon(false);
      }
   }, [isOpen, clearFiles, clearErrors]);

   // Clear file error when file is selected
   useEffect(() => {
      if (file) {
         setFileError('');
         setShouldRemoveIcon(false); // Reset remove flag when new file is selected
      }
   }, [file]);

   const onSubmit = async (data: ProductServiceFormValues) => {
      if (!productServiceId) return;

      setFileError('');

      try {
         let iconUrl: string | null | undefined;

         if (file) {
            const uploadResponse = await fileUploadMutation.mutateAsync(file.file as File);
            iconUrl = uploadResponse.data.url;
         } else if (shouldRemoveIcon) {
            iconUrl = null;
         } else if (productServiceQuery.data?.data?.icon) {
            iconUrl = productServiceQuery.data.data.icon;
         }

         const payload: any = {
            name: data.name,
            description: data.description || undefined,
         };

         if (file || shouldRemoveIcon) {
            payload.icon = iconUrl;
         }

         updateProductServiceMutation.mutate(
            { id: productServiceId, ...payload },
            {
               onSuccess: () => {
                  toast.success('Product service updated successfully');
                  handleClose();
                  queryClient.invalidateQueries({ queryKey: ['product-services'] });
                  queryClient.invalidateQueries({
                     queryKey: ['product-service', productServiceId],
                  });
               },
            }
         );
      } catch (error: any) {
         console.error('Submit error:', error);
         setFileError('Failed to upload icon. Please try again.');
      }
   };

   const handleClose = () => {
      if (onClose) onClose();
      reset();
      clearFiles();
      clearErrors();
      setFileError('');
      setShouldRemoveIcon(false);
   };

   const isLoading = updateProductServiceMutation.isPending || fileUploadMutation.isPending;

   // Show loading state
   if (productServiceQuery.isLoading) {
      return (
         <Dialog open={isOpen} onOpenChange={() => handleClose()}>
            <DialogContent className='max-w-md'>
               <DialogHeader>
                  <DialogTitle>Loading...</DialogTitle>
                  <DialogDescription>
                     Please wait while we load the product service data.
                  </DialogDescription>
               </DialogHeader>
               <div className='flex items-center justify-center py-8'>
                  <Spinner className='h-8 w-8' />
               </div>
            </DialogContent>
         </Dialog>
      );
   }

   // Show error state
   if (productServiceQuery.error) {
      return (
         <Dialog open={isOpen} onOpenChange={() => handleClose()}>
            <DialogContent className='max-w-md'>
               <DialogHeader>
                  <DialogTitle>Error</DialogTitle>
                  <DialogDescription>Failed to load product service data.</DialogDescription>
               </DialogHeader>
               <div className='text-center py-8'>
                  <p className='text-red-600'>Failed to load product service data</p>
                  <Button onClick={handleClose} className='mt-4'>
                     Close
                  </Button>
               </div>
            </DialogContent>
         </Dialog>
      );
   }

   return (
      <Dialog open={isOpen} onOpenChange={() => handleClose()}>
         <DialogContent
            onInteractOutside={e => {
               e.preventDefault();
            }}
            className='max-w-lg max-h-[90vh] overflow-y-auto'
         >
            <DialogHeader>
               <DialogTitle>Edit Product Service</DialogTitle>
               <DialogDescription>Update the product service information</DialogDescription>
            </DialogHeader>

            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4 py-4'>
               <div className='flex flex-col gap-2'>
                  <Label htmlFor='name'>Name *</Label>
                  <Controller
                     control={control}
                     name='name'
                     render={({ field }) => (
                        <Input
                           id='name'
                           placeholder='e.g. Ride Sharing'
                           {...field}
                           className='w-full'
                        />
                     )}
                  />
                  {errors.name && <ErrorMessage error={errors.name} />}
               </div>

               <div className='flex flex-col gap-2'>
                  <Label htmlFor='description'>Description</Label>
                  <Controller
                     control={control}
                     name='description'
                     render={({ field }) => (
                        <Textarea
                           id='description'
                           placeholder='e.g. On-demand ride sharing service'
                           {...field}
                           className='w-full'
                           rows={3}
                        />
                     )}
                  />
                  {errors.description && <ErrorMessage error={errors.description} />}
               </div>

               {/* Icon Upload Section */}
               <FileUploadSection
                  label='Product Service Icon'
                  fieldName='icon'
                  maxSize={maxSize}
                  existingFileUrl={productServiceQuery.data?.data?.icon}
                  fileError={fileError}
                  fileUploadState={{ files, isDragging, errors: uploadErrors }}
                  fileUploadActions={{
                     handleDragEnter,
                     handleDragLeave,
                     handleDragOver,
                     handleDrop,
                     openFileDialog,
                     removeFile,
                     getInputProps,
                     addFiles,
                     clearFiles,
                     clearErrors,
                     handleFileChange,
                  }}
                  // Enhanced removal functionality
                  onRemoveExisting={() => setShouldRemoveIcon(true)}
                  isExistingFileMarkedForRemoval={shouldRemoveIcon}
                  showRemovalIndicator={true}
               />

               <div className='flex gap-3 pt-4'>
                  <Button type='button' variant='outline' onClick={handleClose} className='flex-1'>
                     Cancel
                  </Button>
                  <Button type='submit' disabled={isLoading} className='flex-1'>
                     {isLoading ? (
                        <>
                           Updating...
                           <Spinner className='ml-2 h-4 w-4' />
                        </>
                     ) : (
                        'Update Product Service'
                     )}
                  </Button>
               </div>
            </form>
         </DialogContent>
      </Dialog>
   );
};

// ============================================
// CORE TYPES
// ============================================
export interface Location {
  lat: number;
  lng: number;
  address?: string;
}

// ============================================
// SEARCH RIDE TYPES
// ============================================
export enum RideSearchType {
  NOW = 'now',
  LATER = 'later',
}

export interface SearchRideRequest {
  pickup: Location;
  destination: Location;
  pickupType?: RideSearchType;
  pickupTime?: string;
  stops?: Location[];
}

export interface PickupToDestinationResult {
  durationInSeconds: number;
  distanceMeters: number;
}

export interface DriverToPickupResults {
  durationInSeconds: number;
  distanceMeters: number;
  estimatedArrivalTime: string | null;
}

export interface DriverToDestinationResults {
  durationInSeconds: number;
  distanceMeters: number;
  estimatedArrivalTime: string | null;
}

export interface CityProduct {
  id: string;
  cityId: string;
  productId: string;
}

export interface RideSearchResult {
  id: string; // This is the productId
  name: string;
  identifier: string;
  serviceName: string;
  description?: string;
  icon?: string;
  price: number;
  strikethroughPrice: number;
  passengerLimit: number;
  pickupToDestinationResult: PickupToDestinationResult;
  driverToPickupResults: DriverToPickupResults;
  driverToDestinationResults: DriverToDestinationResults;
  cityProduct: CityProduct;
}

export interface SearchRideResponse {
  success: boolean;
  message: string;
  data: RideSearchResult[];
  timestamp: number;
}

// ============================================
// CREATE RIDE TYPES
// ============================================
export interface RiderMeta {
  name: string;
  phoneNumber: string;
  email?: string;
}

export interface CreateRideRequest {
  pickup: Location;
  destination: Location;
  cityProductId: string;
  productId?: string;
}

export interface AdminCreateRideRequest {
  pickup: Location;
  destination: Location;
  stops?: Location[];
  cityProductId: string;
  productId?: string;
  riderId?: string; // For existing rider
  riderMeta: RiderMeta; // Always required for admin
  pickupType?: string;
  pickupTime?: string;
}

export interface CreateRideResponse {
  success: boolean;
  message: string;
  data: any; // Ride details - we don't need to define full type for now
  timestamp: number;
}

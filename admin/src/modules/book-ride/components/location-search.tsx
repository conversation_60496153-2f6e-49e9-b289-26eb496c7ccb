'use client';

import { useRef, useEffect, useState } from 'react';
import { Autocomplete } from '@react-google-maps/api';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { MapPin, Map } from 'lucide-react';
import { Location } from '../types/book-ride';
import { LocationMapPicker } from './location-map-picker';

interface LocationSearchProps {
  label: string;
  placeholder: string;
  value: string;
  onLocationSelect: (location: Location) => void;
  onInputChange: (value: string) => void;
  biasLocation?: Location | null;
  enableMapPicker?: boolean;
  currentLocation?: Location | null;
}

export function LocationSearch({
  label,
  placeholder,
  value,
  onLocationSelect,
  onInputChange,
  biasLocation,
  enableMapPicker = true,
  currentLocation,
}: LocationSearchProps) {
  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);
  const [isMapPickerOpen, setIsMapPickerOpen] = useState(false);

  const onLoad = (autocomplete: google.maps.places.Autocomplete) => {
    autocompleteRef.current = autocomplete;

    // Configure to search for all location types with optional location bias
    const options: google.maps.places.AutocompleteOptions = {
      fields: ['name', 'geometry', 'place_id', 'formatted_address'],
    };

    // If biasLocation is provided, prioritize results near that location
    if (biasLocation) {
      const center = new google.maps.LatLng(biasLocation.lat, biasLocation.lng);
      const circle = new google.maps.Circle({
        center: center,
        radius: 50000, // 50km radius in meters
      });
      options.bounds = circle.getBounds() || undefined;
      options.strictBounds = false; // Allow results outside bounds if no nearby matches
    }

    autocomplete.setOptions(options);
  };

  // Update autocomplete options when biasLocation changes
  useEffect(() => {
    if (autocompleteRef.current) {
      const options: google.maps.places.AutocompleteOptions = {
        fields: ['name', 'geometry', 'place_id', 'formatted_address'],
      };

      if (biasLocation) {
        const center = new google.maps.LatLng(biasLocation.lat, biasLocation.lng);
        const circle = new google.maps.Circle({
          center: center,
          radius: 50000, // 50km radius in meters
        });
        options.bounds = circle.getBounds() || undefined;
        options.strictBounds = false;
      }

      autocompleteRef.current.setOptions(options);
    }
  }, [biasLocation]);

  const onPlaceChanged = () => {
    if (autocompleteRef.current) {
      const place = autocompleteRef.current.getPlace();

      if (place.geometry?.location) {
        const location: Location = {
          lat: place.geometry.location.lat(),
          lng: place.geometry.location.lng(),
          address: place.formatted_address || place.name || '',
        };

        onLocationSelect(location);
        onInputChange(place.formatted_address || place.name || '');
      }
    }
  };

  const handleMapPickerSelect = (location: Location) => {
    onLocationSelect(location);
    onInputChange(location.address || '');
  };

  return (
    <>
      <div className='flex flex-col gap-2'>
        <div className='flex items-center justify-between'>
          <Label htmlFor={label.toLowerCase().replace(' ', '-')}>{label}</Label>
          {enableMapPicker && (
            <Button
              type='button'
              variant='outline'
              size='sm'
              className='h-7 text-xs gap-1.5'
              onClick={() => setIsMapPickerOpen(true)}
            >
              <Map className='h-3.5 w-3.5' />
              Set location on map
            </Button>
          )}
        </div>
        <div className='relative'>
          <MapPin className='absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 z-10' />
          <Autocomplete onLoad={onLoad} onPlaceChanged={onPlaceChanged}>
            <Input
              id={label.toLowerCase().replace(' ', '-')}
              type='text'
              placeholder={placeholder}
              value={value}
              onChange={e => onInputChange(e.target.value)}
              className='pl-10'
            />
          </Autocomplete>
        </div>
      </div>

      {enableMapPicker && (
        <LocationMapPicker
          open={isMapPickerOpen}
          onOpenChange={setIsMapPickerOpen}
          initialLocation={currentLocation}
          biasLocation={biasLocation}
          onLocationSelect={handleMapPickerSelect}
        />
      )}
    </>
  );
}

'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { GoogleMap, Autocomplete } from '@react-google-maps/api';
import { MapPin, Search, Loader2 } from 'lucide-react';
import { Dialog, DialogContent, DialogFooter, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { VisuallyHidden } from '@/components/ui/visually-hidden';
import { Location } from '../types/book-ride';
import { reverseGeocode, createLocation, formatCoordinates } from '../utils/geocoding';

interface LocationMapPickerProps {
   open: boolean;
   onOpenChange: (open: boolean) => void;
   initialLocation?: Location | null;
   biasLocation?: Location | null;
   onLocationSelect: (location: Location) => void;
}

const mapContainerStyle = {
   width: '100%',
   height: '100%',
};

const mapOptions: google.maps.MapOptions = {
   gestureHandling: 'greedy',
   disableDefaultUI: true,
   zoomControl: true,
   mapTypeControl: false,
   streetViewControl: false,
   fullscreenControl: false,
   minZoom: 3,
   maxZoom: 20,
};

// Default center (Kochi, India)
const DEFAULT_CENTER = {
   lat: 9.9312,
   lng: 76.2673,
};

const DEFAULT_ZOOM = 15;

export function LocationMapPicker({
   open,
   onOpenChange,
   initialLocation,
   biasLocation,
   onLocationSelect,
}: LocationMapPickerProps) {
   const mapRef = useRef<google.maps.Map | null>(null);
   const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);
   const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

   const [mapCenter, setMapCenter] = useState<{ lat: number; lng: number }>(
      initialLocation || DEFAULT_CENTER
   );
   const [currentAddress, setCurrentAddress] = useState<string>(
      initialLocation?.address || ''
   );
   const [isGeocoding, setIsGeocoding] = useState(false);
   const [searchInput, setSearchInput] = useState('');

   // Update center when initial location changes or dialog opens
   useEffect(() => {
      if (open && initialLocation) {
         setMapCenter({ lat: initialLocation.lat, lng: initialLocation.lng });
         setCurrentAddress(initialLocation.address || '');
      } else if (open && !initialLocation) {
         // Reset to default when opening without initial location
         setMapCenter(DEFAULT_CENTER);
         setCurrentAddress('');
      }
   }, [open, initialLocation]);

   // Handle map load
   const onMapLoad = useCallback(
      (map: google.maps.Map) => {
         mapRef.current = map;

         // Set initial center and zoom
         const center = initialLocation || DEFAULT_CENTER;
         map.setCenter(center);
         map.setZoom(DEFAULT_ZOOM);

         // Trigger initial geocoding if no address is provided
         if (initialLocation && !initialLocation.address) {
            performReverseGeocode(initialLocation.lat, initialLocation.lng);
         }
      },
      [initialLocation]
   );

   // Perform reverse geocoding
   const performReverseGeocode = async (lat: number, lng: number) => {
      setIsGeocoding(true);
      const address = await reverseGeocode(lat, lng);
      setCurrentAddress(address || formatCoordinates(lat, lng));
      setIsGeocoding(false);
   };

   // Handle map center changed (when user drags the map)
   const handleCenterChanged = useCallback(() => {
      if (!mapRef.current) return;

      const center = mapRef.current.getCenter();
      if (!center) return;

      const lat = center.lat();
      const lng = center.lng();

      setMapCenter({ lat, lng });

      // Debounce reverse geocoding to avoid excessive API calls
      if (debounceTimerRef.current) {
         clearTimeout(debounceTimerRef.current);
      }

      debounceTimerRef.current = setTimeout(() => {
         performReverseGeocode(lat, lng);
      }, 300); // 300ms debounce
   }, []);

   // Handle autocomplete place selection
   const handlePlaceSelect = useCallback(() => {
      if (!autocompleteRef.current) return;

      const place = autocompleteRef.current.getPlace();
      if (!place.geometry?.location) return;

      const location = place.geometry.location;
      const lat = location.lat();
      const lng = location.lng();

      // Pan to the selected location
      if (mapRef.current) {
         mapRef.current.panTo({ lat, lng });
         mapRef.current.setZoom(DEFAULT_ZOOM);
      }

      setMapCenter({ lat, lng });
      setCurrentAddress(place.formatted_address || place.name || '');
      setSearchInput('');
   }, []);

   // Handle autocomplete load
   const onAutocompleteLoad = useCallback(
      (autocomplete: google.maps.places.Autocomplete) => {
         autocompleteRef.current = autocomplete;
         autocomplete.setFields(['geometry', 'formatted_address', 'name', 'place_id']);

         // Apply location bias if provided
         if (biasLocation) {
            const center = new google.maps.LatLng(biasLocation.lat, biasLocation.lng);
            const circle = new google.maps.Circle({
               center: center,
               radius: 50000, // 50km radius in meters
            });
            const bounds = circle.getBounds();
            if (bounds) {
               autocomplete.setOptions({
                  bounds: bounds,
                  strictBounds: false, // Allow results outside bounds if no nearby matches
               });
            }
         }
      },
      [biasLocation]
   );

   // Update autocomplete options when biasLocation changes
   useEffect(() => {
      if (autocompleteRef.current && biasLocation) {
         const center = new google.maps.LatLng(biasLocation.lat, biasLocation.lng);
         const circle = new google.maps.Circle({
            center: center,
            radius: 50000, // 50km radius in meters
         });
         const bounds = circle.getBounds();
         if (bounds) {
            autocompleteRef.current.setOptions({
               bounds: bounds,
               strictBounds: false,
            });
         }
      }
   }, [biasLocation]);

   // Handle confirm button
   const handleConfirm = () => {
      const location = createLocation(mapCenter.lat, mapCenter.lng, currentAddress);
      onLocationSelect(location);
      onOpenChange(false);
   };

   // Handle cancel button
   const handleCancel = () => {
      onOpenChange(false);
   };

   // Cleanup debounce timer
   useEffect(() => {
      return () => {
         if (debounceTimerRef.current) {
            clearTimeout(debounceTimerRef.current);
         }
      };
   }, []);

   return (
      <Dialog open={open} onOpenChange={onOpenChange}>
         <DialogContent className='max-w-[60vw] h-[60vh] p-0 gap-0' preventOutsideClickClose={true}>
            <VisuallyHidden>
               <DialogTitle>Set Location on Map</DialogTitle>
            </VisuallyHidden>
            <div className='relative w-full h-full'>
               {/* Map Container */}
               <div className='w-full h-full'>
                  <GoogleMap
                     mapContainerStyle={mapContainerStyle}
                     onLoad={onMapLoad}
                     onCenterChanged={handleCenterChanged}
                     options={mapOptions}
                  />
               </div>

               {/* Search Box Overlay */}
               <div className='absolute top-4 left-1/2 -translate-x-1/2 w-[calc(100%-2rem)] max-w-md z-[1000]'>
                  <Autocomplete onLoad={onAutocompleteLoad} onPlaceChanged={handlePlaceSelect}>
                     <div className='relative'>
                        <Search className='absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none' />
                        <Input
                           type='text'
                           placeholder='Search for a location...'
                           value={searchInput}
                           onChange={e => setSearchInput(e.target.value)}
                           className='pl-9 bg-white shadow-md'
                        />
                     </div>
                  </Autocomplete>
               </div>

               {/* Center Pin (Fixed) */}
               <div className='absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-full z-20 pointer-events-none'>
                  <MapPin className='h-10 w-10 text-red-500 drop-shadow-lg' fill='currentColor' />
               </div>

               {/* Address Display Card */}
               <div className='absolute bottom-20 left-1/2 -translate-x-1/2 w-[calc(100%-2rem)] max-w-md z-10'>
                  <div className='bg-white rounded-lg shadow-lg p-4'>
                     <div className='flex items-start gap-3'>
                        <MapPin className='h-5 w-5 text-gray-600 flex-shrink-0 mt-0.5' />
                        <div className='flex-1 min-w-0'>
                           {isGeocoding ? (
                              <div className='flex items-center gap-2'>
                                 <Loader2 className='h-4 w-4 animate-spin text-gray-500' />
                                 <span className='text-sm text-gray-500'>Finding address...</span>
                              </div>
                           ) : (
                              <>
                                 <p className='text-sm text-gray-900 break-words'>
                                    {currentAddress || 'Move the map to select a location'}
                                 </p>
                                 {currentAddress && (
                                    <p className='text-xs text-gray-500 mt-1.5'>
                                       Drag the map to adjust the pin location
                                    </p>
                                 )}
                              </>
                           )}
                        </div>
                     </div>
                  </div>
               </div>

               {/* Action Buttons */}
               <DialogFooter className='absolute bottom-4 left-1/2 -translate-x-1/2 w-[calc(100%-2rem)] max-w-md z-10 flex-row gap-2 sm:justify-center'>
                  <Button onClick={handleCancel} variant='outline' className='flex-1 bg-white'>
                     Cancel
                  </Button>
                  <Button
                     onClick={handleConfirm}
                     disabled={!currentAddress || isGeocoding}
                     className='flex-1'
                  >
                     Confirm Location
                  </Button>
               </DialogFooter>
            </div>
         </DialogContent>
      </Dialog>
   );
}

import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { AdminCreateRideRequest, CreateRideResponse } from '../types/book-ride';

/**
 * Hook for creating a new ride booking (Admin)
 */
export const useCreateRide = () => {
  return useMutation({
    mutationFn: async (data: AdminCreateRideRequest): Promise<CreateRideResponse> => {
      return apiClient.post('/rides/request/admin', data);
    },
  });
};

'use client';

import { CustomPagination } from '@/components/pagination';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { DailyEarningsResponse, DailyDriverEarning } from '../types/driver-earnings';
import { DailyEarningsTableLoading } from './daily-earnings-table-loading';
import { DailyEarningsTableEmpty } from './daily-earnings-table-empty';
import { format } from 'date-fns';

// ============================================
// COLUMN DEFINITIONS
// ============================================
const columns: ColumnDef<DailyDriverEarning>[] = [
   {
      accessorKey: 'date',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Date</div>,
      cell: ({ row }) => {
         const earning = row.original;
         const date = new Date(earning.date);
         return (
            <div className='text-left max-w-[150px]'>
               <div className='text-sm font-medium'>
                  {format(date, 'MMM dd, yyyy')}
               </div>
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'totalFare',
      header: () => <div className='text-right font-semibold text-gray-600 text-sm'>Total Fare</div>,
      cell: ({ row }) => {
         const earning = row.original;
         return (
            <div className='text-right'>
               <div className='text-sm font-medium'>₹{earning.totalFare.toFixed(2)}</div>
            </div>
         );
      },
      size: 130,
   },
   {
      accessorKey: 'totalCommission',
      header: () => <div className='text-right font-semibold text-gray-600 text-sm'>Commission</div>,
      cell: ({ row }) => {
         const earning = row.original;
         return (
            <div className='text-right'>
               <div className='text-sm text-gray-600'>₹{earning.totalCommission.toFixed(2)}</div>
            </div>
         );
      },
      size: 130,
   },
   {
      accessorKey: 'totalTaxOnCommission',
      header: () => (
         <div className='text-right font-semibold text-gray-600 text-sm'>Tax on Commission</div>
      ),
      cell: ({ row }) => {
         const earning = row.original;
         return (
            <div className='text-right'>
               <div className='text-sm text-gray-600'>₹{earning.totalTaxOnCommission.toFixed(2)}</div>
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'netDriverEarnings',
      header: () => <div className='text-right font-semibold text-gray-600 text-sm'>Net Earnings</div>,
      cell: ({ row }) => {
         const earning = row.original;
         return (
            <div className='text-right'>
               <div className='text-sm font-semibold text-green-600'>
                  ₹{earning.netDriverEarnings.toFixed(2)}
               </div>
            </div>
         );
      },
      size: 140,
   },
   {
      accessorKey: 'completedRides',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Rides</div>,
      cell: ({ row }) => {
         const earning = row.original;
         return (
            <div className='text-center'>
               <div className='text-sm font-medium'>{earning.completedRides}</div>
            </div>
         );
      },
      size: 100,
   },
];

// ============================================
// TABLE COMPONENT
// ============================================
interface DailyEarningsTableProps {
   data: DailyEarningsResponse | undefined;
   isLoading: boolean;
   currentPage: number;
   onPageChange: (page: number) => void;
}

export function DailyEarningsTable({
   data,
   isLoading,
   currentPage,
   onPageChange,
}: DailyEarningsTableProps) {
   const table = useReactTable({
      data: data?.data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   if (isLoading) {
      return <DailyEarningsTableLoading />;
   }

   if (!data?.data?.length) {
      return <DailyEarningsTableEmpty />;
   }

   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full table-fixed'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize(), maxWidth: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(header.column.columnDef.header, header.getContext())}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td
                                 key={cell.id}
                                 className='px-4 py-3 align-middle'
                                 style={{
                                    width: cell.column.getSize(),
                                    maxWidth: cell.column.getSize(),
                                 }}
                              >
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>

         {data && data.meta && data.meta.totalPages > 1 && (
            <CustomPagination
               currentPage={currentPage}
               totalPages={data.meta.totalPages}
               onPageChange={onPageChange}
               hasNext={currentPage < data.meta.totalPages}
               hasPrev={currentPage > 1}
            />
         )}
      </div>
   );
}

import { Skeleton } from '@/components/ui/skeleton';

export function AggregatedEarningsTableLoading() {
   return (
      <div className='space-y-4'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full'>
                  <thead>
                     <tr className='border-b bg-gray-50'>
                        <th className='h-11 px-4 text-left'>
                           <Skeleton className='h-4 w-28' />
                        </th>
                        <th className='h-11 px-4 text-left'>
                           <Skeleton className='h-4 w-20' />
                        </th>
                        <th className='h-11 px-4 text-right'>
                           <Skeleton className='h-4 w-24 ml-auto' />
                        </th>
                        <th className='h-11 px-4 text-right'>
                           <Skeleton className='h-4 w-24 ml-auto' />
                        </th>
                        <th className='h-11 px-4 text-right'>
                           <Skeleton className='h-4 w-32 ml-auto' />
                        </th>
                        <th className='h-11 px-4 text-right'>
                           <Skeleton className='h-4 w-28 ml-auto' />
                        </th>
                        <th className='h-11 px-4 text-center'>
                           <Skeleton className='h-4 w-12 mx-auto' />
                        </th>
                        <th className='h-11 px-4 text-center'>
                           <Skeleton className='h-4 w-16 mx-auto' />
                        </th>
                     </tr>
                  </thead>
                  <tbody>
                     {[...Array(5)].map((_, index) => (
                        <tr key={index} className='border-b'>
                           <td className='px-4 py-3'>
                              <Skeleton className='h-4 w-36' />
                           </td>
                           <td className='px-4 py-3'>
                              <Skeleton className='h-4 w-24' />
                           </td>
                           <td className='px-4 py-3 text-right'>
                              <Skeleton className='h-4 w-20 ml-auto' />
                           </td>
                           <td className='px-4 py-3 text-right'>
                              <Skeleton className='h-4 w-20 ml-auto' />
                           </td>
                           <td className='px-4 py-3 text-right'>
                              <Skeleton className='h-4 w-20 ml-auto' />
                           </td>
                           <td className='px-4 py-3 text-right'>
                              <Skeleton className='h-4 w-24 ml-auto' />
                           </td>
                           <td className='px-4 py-3 text-center'>
                              <Skeleton className='h-4 w-8 mx-auto' />
                           </td>
                           <td className='px-4 py-3'>
                              <div className='flex justify-center'>
                                 <Skeleton className='h-8 w-24' />
                              </div>
                           </td>
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>
      </div>
   );
}

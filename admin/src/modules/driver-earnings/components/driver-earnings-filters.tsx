'use client';

import { useState, useRef, useEffect } from 'react';
import { format, startOfToday } from 'date-fns';
import { CalendarIcon, Download, Phone, X } from 'lucide-react';
import { DateRange } from 'react-day-picker';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { Spinner } from '@/components/ui/spinner';
import { cn } from '@/lib/utils';
import { City } from '@/modules/city/types/city';

interface DriverEarningsFiltersProps {
   fromDate: string;
   toDate: string;
   cityId: string;
   phoneNumber: string;
   cities: City[];
   onFromDateChange: (value: string) => void;
   onToDateChange: (value: string) => void;
   onCityIdChange: (value: string) => void;
   onPhoneNumberChange: (value: string) => void;
   onClearFilters: () => void;
   onExportClick: () => void;
   showCityFilter?: boolean;
}

export function DriverEarningsFilters({
   fromDate,
   toDate,
   cityId,
   phoneNumber,
   cities,
   onFromDateChange,
   onToDateChange,
   onCityIdChange,
   onPhoneNumberChange,
   onClearFilters,
   onExportClick,
   showCityFilter = true,
}: DriverEarningsFiltersProps) {
   const [phoneValue, setPhoneValue] = useState(phoneNumber || '');
   const [isPhoneSearching, setIsPhoneSearching] = useState(false);
   const phoneTimeoutRef = useRef<NodeJS.Timeout | null>(null);

   // Update local phone state when props change
   useEffect(() => {
      setPhoneValue(phoneNumber || '');
   }, [phoneNumber]);

   // Clean up timeout on unmount
   useEffect(() => {
      return () => {
         if (phoneTimeoutRef.current) {
            clearTimeout(phoneTimeoutRef.current);
         }
      };
   }, []);

   const hasActiveFilters = fromDate || toDate || (cityId && cityId !== 'all') || phoneNumber;

   const dateRange: DateRange | undefined =
      fromDate || toDate
         ? {
              from: fromDate ? new Date(fromDate) : undefined,
              to: toDate ? new Date(toDate) : undefined,
           }
         : undefined;

   const handleDateRangeChange = (range: DateRange | undefined) => {
      onFromDateChange(range?.from ? format(range.from, 'yyyy-MM-dd') : '');
      onToDateChange(range?.to ? format(range.to, 'yyyy-MM-dd') : '');
   };

   const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setPhoneValue(value);
      setIsPhoneSearching(true);

      if (phoneTimeoutRef.current) {
         clearTimeout(phoneTimeoutRef.current);
      }

      phoneTimeoutRef.current = setTimeout(() => {
         onPhoneNumberChange(value);
         phoneTimeoutRef.current = null;
         setIsPhoneSearching(false);
      }, 500);
   };

   return (
      <div className='bg-white rounded-lg border border-gray-200 p-4'>
         <div className='flex items-end justify-between gap-4'>
            <div className='flex items-end gap-4'>
               {/* Date Range Picker */}
               <div className='flex flex-col gap-2 min-w-[280px]'>
                  <Label>Date Range</Label>
                  <div className='relative'>
                     <Popover>
                        <PopoverTrigger asChild>
                           <Button
                              variant='outline'
                              className={cn(
                                 'w-full justify-start text-left font-normal pr-10',
                                 !dateRange && 'text-muted-foreground'
                              )}
                           >
                              <CalendarIcon className='mr-2 h-4 w-4' />
                              {dateRange?.from ? (
                                 dateRange.to ? (
                                    <>
                                       {format(dateRange.from, 'MMM dd, yyyy')} -{' '}
                                       {format(dateRange.to, 'MMM dd, yyyy')}
                                    </>
                                 ) : (
                                    format(dateRange.from, 'MMM dd, yyyy')
                                 )
                              ) : (
                                 'Pick a date range'
                              )}
                           </Button>
                        </PopoverTrigger>
                        <PopoverContent className='w-auto p-2' align='start'>
                           <Calendar
                              mode='range'
                              selected={dateRange}
                              onSelect={handleDateRangeChange}
                              disabled={{ after: startOfToday() }}
                           />
                        </PopoverContent>
                     </Popover>
                     {dateRange && (
                        <button
                           type='button'
                           onClick={() => handleDateRangeChange(undefined)}
                           className='absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 flex items-center justify-center rounded-md hover:bg-gray-100 transition-colors'
                           aria-label='Clear date range'
                        >
                           <svg
                              xmlns='http://www.w3.org/2000/svg'
                              width='14'
                              height='14'
                              viewBox='0 0 24 24'
                              fill='none'
                              stroke='currentColor'
                              strokeWidth='2'
                              strokeLinecap='round'
                              strokeLinejoin='round'
                              className='text-gray-500'
                           >
                              <line x1='18' y1='6' x2='6' y2='18'></line>
                              <line x1='6' y1='6' x2='18' y2='18'></line>
                           </svg>
                        </button>
                     )}
                  </div>
               </div>

               {/* City Filter - Conditional */}
               {showCityFilter && (
                  <div className='flex flex-col gap-2 min-w-[200px]'>
                     <Label>City</Label>
                     <Select value={cityId} onValueChange={onCityIdChange}>
                        <SelectTrigger className='w-full'>
                           <SelectValue placeholder='All Cities' />
                        </SelectTrigger>
                        <SelectContent>
                           <SelectItem value='all'>All Cities</SelectItem>
                           {cities.map(city => (
                              <SelectItem key={city.id} value={city.id}>
                                 {city.name}
                              </SelectItem>
                           ))}
                        </SelectContent>
                     </Select>
                  </div>
               )}

               {/* Phone Number Filter */}
               <div className='flex flex-col gap-2 min-w-[200px]'>
                  <Label>Phone Number</Label>
                  <div className='relative'>
                     <Phone className='absolute left-2.5 top-2.5 h-4 w-4 text-gray-500' />
                     <Input
                        placeholder='Search by phone...'
                        value={phoneValue}
                        onChange={handlePhoneChange}
                        className='pl-8 pr-8'
                     />
                     {isPhoneSearching && (
                        <div className='absolute right-2.5 top-2.5 text-gray-500'>
                           <Spinner className='h-4 w-4 text-primary' />
                        </div>
                     )}
                     {phoneValue && !isPhoneSearching && (
                        <button
                           type='button'
                           onClick={() => {
                              if (phoneTimeoutRef.current) {
                                 clearTimeout(phoneTimeoutRef.current);
                                 phoneTimeoutRef.current = null;
                              }
                              setIsPhoneSearching(false);
                              setPhoneValue('');
                              onPhoneNumberChange('');
                           }}
                           className='absolute right-2.5 top-2.5 text-gray-500 hover:text-gray-700'
                           aria-label='Clear phone number'
                        >
                           <X className='h-4 w-4' />
                        </button>
                     )}
                  </div>
               </div>

               {/* Clear Filters Button */}
               {hasActiveFilters && (
                  <Button onClick={onClearFilters} variant='outline' className='min-w-[120px]'>
                     Clear Filters
                  </Button>
               )}
            </div>

            {/* Export CSV Button */}
            <Button onClick={onExportClick} variant='outline' className='cursor-pointer min-w-[140px]'>
               <Download className='h-4 w-4 mr-2' />
               Export CSV
            </Button>
         </div>
      </div>
   );
}

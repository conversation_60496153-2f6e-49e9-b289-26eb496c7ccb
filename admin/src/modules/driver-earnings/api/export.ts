import { apiClient } from '@/lib/api-client';
import { ExportAggregatedCSVParams, ExportDailyCSVParams } from '../types/driver-earnings';

/**
 * Download aggregated driver earnings as CSV
 */
export const downloadAggregatedCSV = async ({
   fromDate,
   toDate,
   cityId,
}: ExportAggregatedCSVParams): Promise<void> => {
   try {
      // Make request using apiClient with responseType blob
      // Note: apiClient's response interceptor returns response.data automatically
      const data = (await apiClient.post(
         '/driver-earnings/reports/aggregated/csv',
         {},
         {
            params: {
               fromDate,
               toDate,
               ...(cityId && { cityId }),
            },
            headers: {
               Accept: 'text/csv',
            },
            responseType: 'blob',
         }
      )) as Blob;

      // data is already the blob because the interceptor returns response.data
      const blob = data instanceof Blob ? data : new Blob([data as BlobPart], { type: 'text/csv' });

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `driver-earnings-aggregated-${fromDate}-to-${toDate}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
   } catch (error) {
      console.error('Error downloading aggregated CSV:', error);
      throw error;
   }
};

/**
 * Download daily driver earnings as CSV for a specific driver
 */
export const downloadDailyCSV = async ({
   driverId,
   fromDate,
   toDate,
}: ExportDailyCSVParams): Promise<void> => {
   try {
      // Make request using apiClient with responseType blob
      // Note: apiClient's response interceptor returns response.data automatically
      const data = (await apiClient.post(
         `/driver-earnings/reports/daily/csv/${driverId}`,
         {},
         {
            params: {
               driverId,
               fromDate,
               toDate,
            },
            headers: {
               Accept: 'text/csv',
            },
            responseType: 'blob',
         }
      )) as Blob;

      // data is already the blob because the interceptor returns response.data
      const blob = data instanceof Blob ? data : new Blob([data as BlobPart], { type: 'text/csv' });

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `driver-earnings-daily-${driverId}-${fromDate}-to-${toDate}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
   } catch (error) {
      console.error('Error downloading daily CSV:', error);
      throw error;
   }
};

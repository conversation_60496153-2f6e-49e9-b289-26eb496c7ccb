'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Mail, Phone } from 'lucide-react';
import { useListAggregatedEarnings, useListDailyEarnings } from '../api/queries';
import { useListCities } from '@/modules/city/api/queries';
import { useGetDriver } from '@/modules/driver/api/queries';
import { DriverEarningsFilters } from '../components/driver-earnings-filters';
import { AggregatedEarningsTable } from '../components/aggregated-earnings-table';
import { DailyEarningsTable } from '../components/daily-earnings-table';
import { DriverEarningsExportModal } from '../components/driver-earnings-export-modal';

export function DriverEarningsPage() {
   const router = useRouter();
   const searchParams = useSearchParams();
   const driverId = searchParams.get('driverId');

   const [page, setPage] = useState(1);
   const [limit] = useState(10);
   const [fromDate, setFromDate] = useState('');
   const [toDate, setToDate] = useState('');
   const [cityId, setCityId] = useState('all');
   const [phoneNumber, setPhoneNumber] = useState('');
   const [isExportModalOpen, setIsExportModalOpen] = useState(false);

   // Clear filters when switching between views
   useEffect(() => {
      setFromDate('');
      setToDate('');
      setCityId('all');
      setPhoneNumber('');
      setPage(1);
   }, [driverId]);

   // Fetch cities for filter
   const citiesQuery = useListCities({
      page: 1,
      limit: 100,
      status: 'active',
   });

   // Fetch aggregated earnings (when no driverId)
   const aggregatedQuery = useListAggregatedEarnings({
      page,
      limit,
      fromDate: fromDate || undefined,
      toDate: toDate || undefined,
      cityId: cityId && cityId !== 'all' ? cityId : undefined,
      phoneNumber: phoneNumber || undefined,
   });

   // Fetch daily earnings (when driverId is present)
   const dailyQuery = useListDailyEarnings({
      driverId: driverId || '',
      page,
      limit,
      fromDate: fromDate || undefined,
      toDate: toDate || undefined,
   });

   // Fetch driver details (when driverId is present)
   const driverQuery = useGetDriver(driverId);

   const handleClearFilters = () => {
      setFromDate('');
      setToDate('');
      setCityId('all');
      setPhoneNumber('');
      setPage(1);
   };

   const handleViewDetails = (selectedDriverId: string) => {
      router.push(`/dashboard/driver-earnings?driverId=${selectedDriverId}`);
   };

   const handleBackToList = () => {
      router.push('/dashboard/driver-earnings');
   };

   const handleExportClick = () => {
      setIsExportModalOpen(true);
   };

   // Show initial loading state only for driver data
   if (driverId && driverQuery.isLoading) {
      return (
         <div className='flex flex-1 flex-col gap-4 p-6'>
            <div className='flex items-center gap-3'>
               <Button variant='ghost' size='sm' disabled>
                  <ArrowLeft className='h-4 w-4' />
               </Button>
               <h2 className='text-2xl font-semibold text-gray-900'>Loading...</h2>
            </div>
            <div className='space-y-4'>
               <div className='h-6 bg-gray-200 rounded animate-pulse w-32' />
               <div className='h-4 bg-gray-200 rounded animate-pulse w-48' />
               <div className='h-4 bg-gray-200 rounded animate-pulse w-full' />
               <div className='h-4 bg-gray-200 rounded animate-pulse w-full' />
            </div>
         </div>
      );
   }

   // Render daily view when driverId is present
   if (driverId) {
      const driver = driverQuery.data?.data;
      const driverName =
         driver?.firstName || driver?.lastName
            ? `${driver.firstName || ''} ${driver.lastName || ''}`.trim()
            : 'Unknown Driver';

      return (
         <div className='flex flex-1 flex-col gap-4 p-6'>
            <div className='flex items-center gap-3'>
               <Button
                  variant='ghost'
                  size='sm'
                  onClick={handleBackToList}
                  className='cursor-pointer'
               >
                  <ArrowLeft className='h-4 w-4' />
               </Button>
               <div className='flex flex-col'>
                  <h2 className='text-2xl font-semibold text-gray-900 capitalize'>
                     {driverName} Daily Earnings
                  </h2>

                  <div className='flex flex-wrap items-center gap-x-3 gap-y-1 text-sm text-gray-500'>
                     {driver?.email && (
                        <span className='flex items-center gap-1'>
                           <Mail className='h-3.5 w-3.5' />
                           {driver.email}
                        </span>
                     )}
                     {driver?.phoneNumber && (
                        <span className='flex items-center gap-1'>
                           <Phone className='h-3.5 w-3.5' />
                           {driver.phoneNumber}
                        </span>
                     )}
                  </div>
               </div>
            </div>

            <DriverEarningsFilters
               fromDate={fromDate}
               toDate={toDate}
               cityId={cityId}
               phoneNumber={phoneNumber}
               cities={citiesQuery.data?.data || []}
               onFromDateChange={setFromDate}
               onToDateChange={setToDate}
               onCityIdChange={setCityId}
               onPhoneNumberChange={setPhoneNumber}
               onClearFilters={handleClearFilters}
               onExportClick={handleExportClick}
               showCityFilter={false}
            />

            <DailyEarningsTable
               data={dailyQuery.data}
               isLoading={dailyQuery.isFetching}
               currentPage={page}
               onPageChange={(newPage: number) => setPage(newPage)}
            />

            <DriverEarningsExportModal
               isOpen={isExportModalOpen}
               onClose={() => setIsExportModalOpen(false)}
               mode='daily'
               driverId={driverId}
            />
         </div>
      );
   }

   // Render aggregated view (default)
   return (
      <div className='flex flex-1 flex-col gap-4 p-6'>
         <div className='flex justify-between items-center'>
            <h2 className='text-2xl font-semibold text-gray-900'>Driver Earnings</h2>
         </div>

         <DriverEarningsFilters
            fromDate={fromDate}
            toDate={toDate}
            cityId={cityId}
            phoneNumber={phoneNumber}
            cities={citiesQuery.data?.data || []}
            onFromDateChange={setFromDate}
            onToDateChange={setToDate}
            onCityIdChange={setCityId}
            onPhoneNumberChange={setPhoneNumber}
            onClearFilters={handleClearFilters}
            onExportClick={handleExportClick}
            showCityFilter={true}
         />

         <AggregatedEarningsTable
            data={aggregatedQuery.data}
            isLoading={aggregatedQuery.isLoading}
            currentPage={page}
            onPageChange={(newPage: number) => setPage(newPage)}
            onViewDetails={handleViewDetails}
         />

         <DriverEarningsExportModal
            isOpen={isExportModalOpen}
            onClose={() => setIsExportModalOpen(false)}
            mode='aggregated'
         />
      </div>
   );
}

import { describe, it, expect } from 'vitest';
import {
   localTimeToUtcMinutes,
   utcMinutesToLocalTime,
   isOvernightRange,
   validateTimeRange,
   generateJsonLogic,
   parseJsonLogic,
   generateJsonLogicFromBlocks,
   parseJsonLogicToBlocks,
   validateConditionBlock,
   type ConditionRange,
} from './condition-helpers';
import { ConditionType, type ConditionBlock } from '../types/charge';

describe('UTC Conversion Functions', () => {
   describe('localTimeToUtcMinutes', () => {
      it('should convert local time to UTC minutes', () => {
         // This test will work regardless of timezone
         // It just verifies that the conversion happens
         const result = localTimeToUtcMinutes('14:30');
         expect(typeof result).toBe('number');
         expect(result).toBeGreaterThanOrEqual(0);
         expect(result).toBeLessThan(1440); // Less than 24 hours in minutes
      });

      it('should handle midnight (00:00)', () => {
         const result = localTimeToUtcMinutes('00:00');
         expect(typeof result).toBe('number');
         expect(result).toBeGreaterThanOrEqual(0);
         expect(result).toBeLessThan(1440);
      });

      it('should handle noon (12:00)', () => {
         const result = localTimeToUtcMinutes('12:00');
         expect(typeof result).toBe('number');
         expect(result).toBeGreaterThanOrEqual(0);
         expect(result).toBeLessThan(1440);
      });

      it('should handle end of day (23:59)', () => {
         const result = localTimeToUtcMinutes('23:59');
         expect(typeof result).toBe('number');
         expect(result).toBeGreaterThanOrEqual(0);
         expect(result).toBeLessThan(1440);
      });
   });

   describe('utcMinutesToLocalTime', () => {
      it('should convert UTC minutes back to local time string', () => {
         const result = utcMinutesToLocalTime(570); // 9:30 UTC
         expect(typeof result).toBe('string');
         expect(result).toMatch(/^\d{2}:\d{2}$/); // HH:mm format
      });

      it('should handle 0 minutes (midnight UTC)', () => {
         const result = utcMinutesToLocalTime(0);
         expect(typeof result).toBe('string');
         expect(result).toMatch(/^\d{2}:\d{2}$/);
      });

      it('should handle 1439 minutes (23:59 UTC)', () => {
         const result = utcMinutesToLocalTime(1439);
         expect(typeof result).toBe('string');
         expect(result).toMatch(/^\d{2}:\d{2}$/);
      });

      it('should handle 720 minutes (noon UTC)', () => {
         const result = utcMinutesToLocalTime(720); // 12:00 UTC
         expect(typeof result).toBe('string');
         expect(result).toMatch(/^\d{2}:\d{2}$/);
      });
   });

   describe('Round-trip conversion', () => {
      it('should preserve local time after round-trip conversion', () => {
         const originalTime = '14:30';
         const utcMinutes = localTimeToUtcMinutes(originalTime);
         const convertedBack = utcMinutesToLocalTime(utcMinutes);

         expect(convertedBack).toBe(originalTime);
      });

      it('should work for midnight', () => {
         const originalTime = '00:00';
         const utcMinutes = localTimeToUtcMinutes(originalTime);
         const convertedBack = utcMinutesToLocalTime(utcMinutes);

         expect(convertedBack).toBe(originalTime);
      });

      it('should work for various times throughout the day', () => {
         const times = ['06:00', '09:15', '12:00', '15:45', '18:30', '21:00', '23:59'];

         times.forEach(time => {
            const utcMinutes = localTimeToUtcMinutes(time);
            const convertedBack = utcMinutesToLocalTime(utcMinutes);
            expect(convertedBack).toBe(time);
         });
      });
   });
});

describe('Helper Functions', () => {
   describe('isOvernightRange', () => {
      it('should return true for overnight ranges', () => {
         expect(isOvernightRange('22:00', '06:00')).toBe(true);
         expect(isOvernightRange('23:00', '01:00')).toBe(true);
         expect(isOvernightRange('20:00', '04:00')).toBe(true);
      });

      it('should return false for same-day ranges', () => {
         expect(isOvernightRange('08:00', '17:00')).toBe(false);
         expect(isOvernightRange('09:00', '18:00')).toBe(false);
         expect(isOvernightRange('00:00', '23:59')).toBe(false);
      });

      it('should return false when start and end are equal', () => {
         expect(isOvernightRange('12:00', '12:00')).toBe(false);
      });
   });

   describe('validateTimeRange', () => {
      it('should return null for valid time ranges', () => {
         const validRange: ConditionRange = {
            startTime: '09:00',
            endTime: '17:00',
            operator: 'or',
         };
         expect(validateTimeRange(validRange)).toBeNull();
      });

      it('should return error for missing start time', () => {
         const invalidRange = {
            startTime: '',
            endTime: '17:00',
            operator: 'or' as const,
         };
         const error = validateTimeRange(invalidRange);
         expect(error).toBeTruthy();
         expect(error).toContain('required');
      });

      it('should return error for missing end time', () => {
         const invalidRange = {
            startTime: '09:00',
            endTime: '',
            operator: 'or' as const,
         };
         const error = validateTimeRange(invalidRange);
         expect(error).toBeTruthy();
         expect(error).toContain('required');
      });

      it('should return error for invalid time format', () => {
         const invalidRange = {
            startTime: '9:00', // Should be 09:00
            endTime: '17:00',
            operator: 'or' as const,
         };
         const error = validateTimeRange(invalidRange);
         expect(error).toBeTruthy();
         expect(error).toContain('Invalid');
      });

      it('should return error when start and end times are the same', () => {
         const invalidRange = {
            startTime: '12:00',
            endTime: '12:00',
            operator: 'or' as const,
         };
         const error = validateTimeRange(invalidRange);
         expect(error).toBeTruthy();
         expect(error).toContain('cannot be the same');
      });
   });
});

describe('JSON Logic Generation (Legacy)', () => {
   describe('generateJsonLogic', () => {
      it('should generate JSON logic for a single time range', () => {
         const ranges: ConditionRange[] = [
            { startTime: '09:00', endTime: '17:00', operator: 'or' },
         ];
         const result = generateJsonLogic(ranges);

         expect(result).toBeDefined();
         expect(result.and).toBeDefined();
         expect(Array.isArray(result.and)).toBe(true);
      });

      it('should generate JSON logic for overnight range', () => {
         const ranges: ConditionRange[] = [
            { startTime: '22:00', endTime: '06:00', operator: 'or' },
         ];
         const result = generateJsonLogic(ranges);

         expect(result).toBeDefined();
         expect(result.or).toBeDefined(); // Overnight ranges use OR
         expect(Array.isArray(result.or)).toBe(true);
      });

      it('should return null for empty ranges', () => {
         const result = generateJsonLogic([]);
         expect(result).toBeNull();
      });

      it('should combine multiple ranges with operators', () => {
         const ranges: ConditionRange[] = [
            { startTime: '09:00', endTime: '12:00', operator: 'or' },
            { startTime: '14:00', endTime: '17:00', operator: 'or' },
         ];
         const result = generateJsonLogic(ranges);

         expect(result).toBeDefined();
         expect(result.or).toBeDefined();
      });
   });

   describe('parseJsonLogic', () => {
      it('should parse JSON logic back to time ranges', () => {
         const ranges: ConditionRange[] = [
            { startTime: '09:00', endTime: '17:00', operator: 'or' },
         ];
         const jsonLogic = generateJsonLogic(ranges);
         const parsed = parseJsonLogic(jsonLogic);

         expect(parsed).toHaveLength(1);
         expect(parsed[0].startTime).toBe('09:00');
         expect(parsed[0].endTime).toBe('17:00');
      });

      it('should return empty array for null condition', () => {
         const result = parseJsonLogic(null);
         expect(result).toEqual([]);
      });

      it('should return empty array for invalid condition', () => {
         const result = parseJsonLogic({ invalid: 'data' });
         expect(result).toEqual([]);
      });
   });

   describe('generateJsonLogic and parseJsonLogic integration', () => {
      it('should round-trip correctly', () => {
         const originalRanges: ConditionRange[] = [
            { startTime: '09:00', endTime: '12:00', operator: 'or' },
            { startTime: '14:00', endTime: '17:00', operator: 'or' },
         ];

         const jsonLogic = generateJsonLogic(originalRanges);
         const parsedRanges = parseJsonLogic(jsonLogic);

         expect(parsedRanges).toHaveLength(originalRanges.length);
         parsedRanges.forEach((range, index) => {
            expect(range.startTime).toBe(originalRanges[index].startTime);
            expect(range.endTime).toBe(originalRanges[index].endTime);
         });
      });
   });
});

describe('JSON Logic Generation (New Block-Based)', () => {
   describe('generateJsonLogicFromBlocks', () => {
      it('should generate JSON logic for a single booking time block', () => {
         const blocks: ConditionBlock[] = [
            {
               type: ConditionType.BOOKING_TIME,
               config: [{ startTime: '09:00', endTime: '17:00', operator: 'or' }],
               operator: 'or',
            },
         ];
         const result = generateJsonLogicFromBlocks(blocks);

         expect(result).toBeDefined();
         expect(result.and).toBeDefined();
      });

      it('should return null for empty blocks', () => {
         const result = generateJsonLogicFromBlocks([]);
         expect(result).toBeNull();
      });

      it('should handle multiple condition blocks', () => {
         const blocks: ConditionBlock[] = [
            {
               type: ConditionType.BOOKING_TIME,
               config: [{ startTime: '09:00', endTime: '12:00', operator: 'or' }],
               operator: 'and',
            },
            {
               type: ConditionType.BOOKING_TIME,
               config: [{ startTime: '14:00', endTime: '17:00', operator: 'or' }],
               operator: 'or',
            },
         ];
         const result = generateJsonLogicFromBlocks(blocks);

         expect(result).toBeDefined();
         expect(result.and).toBeDefined();
      });

      it('should handle blocks with multiple time ranges', () => {
         const blocks: ConditionBlock[] = [
            {
               type: ConditionType.BOOKING_TIME,
               config: [
                  { startTime: '09:00', endTime: '12:00', operator: 'or' },
                  { startTime: '14:00', endTime: '17:00', operator: 'or' },
               ],
               operator: 'or',
            },
         ];
         const result = generateJsonLogicFromBlocks(blocks);

         expect(result).toBeDefined();
      });
   });

   describe('parseJsonLogicToBlocks', () => {
      it('should parse JSON logic back to condition blocks', () => {
         const blocks: ConditionBlock[] = [
            {
               type: ConditionType.BOOKING_TIME,
               config: [{ startTime: '09:00', endTime: '17:00', operator: 'or' }],
               operator: 'or',
            },
         ];
         const jsonLogic = generateJsonLogicFromBlocks(blocks);
         const parsed = parseJsonLogicToBlocks(jsonLogic);

         expect(parsed).toHaveLength(1);
         expect(parsed[0].type).toBe(ConditionType.BOOKING_TIME);
         expect(parsed[0].config[0].startTime).toBe('09:00');
         expect(parsed[0].config[0].endTime).toBe('17:00');
      });

      it('should return empty array for null condition', () => {
         const result = parseJsonLogicToBlocks(null);
         expect(result).toEqual([]);
      });
   });

   describe('validateConditionBlock', () => {
      it('should return null for valid booking time block', () => {
         const block: ConditionBlock = {
            type: ConditionType.BOOKING_TIME,
            config: [{ startTime: '09:00', endTime: '17:00', operator: 'or' }],
            operator: 'or',
         };
         const error = validateConditionBlock(block);
         expect(error).toBeNull();
      });

      it('should return error for empty config', () => {
         const block: ConditionBlock = {
            type: ConditionType.BOOKING_TIME,
            config: [],
            operator: 'or',
         };
         const error = validateConditionBlock(block);
         expect(error).toBeTruthy();
         expect(error).toContain('required');
      });

      it('should return error for invalid time range in config', () => {
         const block: ConditionBlock = {
            type: ConditionType.BOOKING_TIME,
            config: [{ startTime: '12:00', endTime: '12:00', operator: 'or' }],
            operator: 'or',
         };
         const error = validateConditionBlock(block);
         expect(error).toBeTruthy();
         expect(error).toContain('cannot be the same');
      });
   });

   describe('Complete workflow integration', () => {
      it('should handle complete create → generate → parse → validate workflow', () => {
         // Create condition blocks with a single time range
         // Note: Blocks with multiple time ranges will be split during parsing
         const blocks: ConditionBlock[] = [
            {
               type: ConditionType.BOOKING_TIME,
               config: [
                  { startTime: '09:00', endTime: '17:00', operator: 'or' },
               ],
               operator: 'or',
            },
         ];

         // Validate blocks
         blocks.forEach(block => {
            const validationError = validateConditionBlock(block);
            expect(validationError).toBeNull();
         });

         // Generate JSON Logic
         const jsonLogic = generateJsonLogicFromBlocks(blocks);
         expect(jsonLogic).toBeDefined();

         // Parse back to blocks
         const parsedBlocks = parseJsonLogicToBlocks(jsonLogic);
         expect(parsedBlocks.length).toBeGreaterThan(0);

         // Validate parsed blocks
         parsedBlocks.forEach(block => {
            const validationError = validateConditionBlock(block);
            expect(validationError).toBeNull();
         });

         // Verify structure is preserved
         expect(parsedBlocks[0].type).toBe(blocks[0].type);
         expect(parsedBlocks[0].config[0].startTime).toBe('09:00');
         expect(parsedBlocks[0].config[0].endTime).toBe('17:00');
      });

      it('should handle overnight ranges in complete workflow', () => {
         const blocks: ConditionBlock[] = [
            {
               type: ConditionType.BOOKING_TIME,
               config: [{ startTime: '22:00', endTime: '06:00', operator: 'or' }],
               operator: 'or',
            },
         ];

         const jsonLogic = generateJsonLogicFromBlocks(blocks);
         const parsedBlocks = parseJsonLogicToBlocks(jsonLogic);

         expect(parsedBlocks[0].config[0].startTime).toBe('22:00');
         expect(parsedBlocks[0].config[0].endTime).toBe('06:00');
         expect(isOvernightRange('22:00', '06:00')).toBe(true);
      });
   });
});

describe('UTC Conversion with Overnight Ranges', () => {
   it('should handle overnight ranges correctly with UTC conversion', () => {
      const startTime = '22:00';
      const endTime = '06:00';

      // Verify it's detected as overnight
      expect(isOvernightRange(startTime, endTime)).toBe(true);

      // Convert to UTC
      const startMinutes = localTimeToUtcMinutes(startTime);
      const endMinutes = localTimeToUtcMinutes(endTime);

      // Convert back
      const convertedStart = utcMinutesToLocalTime(startMinutes);
      const convertedEnd = utcMinutesToLocalTime(endMinutes);

      // Should preserve original times
      expect(convertedStart).toBe(startTime);
      expect(convertedEnd).toBe(endTime);
   });

   it('should maintain overnight range property after UTC round-trip', () => {
      const ranges: ConditionRange[] = [
         { startTime: '20:00', endTime: '04:00', operator: 'or' },
      ];

      const jsonLogic = generateJsonLogic(ranges);
      const parsed = parseJsonLogic(jsonLogic);

      expect(parsed[0].startTime).toBe('20:00');
      expect(parsed[0].endTime).toBe('04:00');
      expect(isOvernightRange(parsed[0].startTime, parsed[0].endTime)).toBe(true);
   });
});

import { ConditionBlock, ConditionType, TimeRangeConfig } from '../types/charge';
import { setHours, setMinutes, setSeconds, setMilliseconds } from 'date-fns';
import { toZonedTime, fromZonedTime } from 'date-fns-tz';

// Legacy export for backward compatibility during migration
export interface ConditionRange {
   startTime: string;
   endTime: string;
   operator: 'or' | 'and';
}

/**
 * Convert local time string to minutes since midnight in UTC
 * @param time - Time in HH:mm format in local timezone (e.g., "20:00" local)
 * @returns Minutes since midnight in UTC (e.g., if "20:00" local is "15:00" UTC, returns 900)
 * @example
 * // User in UTC+5 timezone enters 20:00
 * localTimeToUtcMinutes('20:00') // Returns 900 (15:00 UTC = 900 minutes)
 */
export function localTimeToUtcMinutes(time: string): number {
   const [hours, minutes] = time.split(':').map(Number);

   // Create a date object with the local time (using today's date)
   let localDate = new Date();
   localDate = setHours(localDate, hours);
   localDate = setMinutes(localDate, minutes);
   localDate = setSeconds(localDate, 0);
   localDate = setMilliseconds(localDate, 0);

   // Get the browser's local timezone
   const localTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

   // Convert local time to UTC using date-fns-tz
   const utcDate = fromZonedTime(localDate, localTimezone);

   // Extract UTC hours and minutes
   const utcHours = utcDate.getUTCHours();
   const utcMinutes = utcDate.getUTCMinutes();

   // Return minutes since midnight in UTC
   return utcHours * 60 + utcMinutes;
}

/**
 * Convert UTC minutes since midnight back to local time string
 * @param minutes - Minutes since midnight in UTC (e.g., 900 means "15:00" UTC)
 * @returns Time in HH:mm format in local timezone (e.g., "20:00")
 * @example
 * // User in UTC+5 timezone, backend sends 900 UTC minutes (15:00 UTC)
 * utcMinutesToLocalTime(900) // Returns "20:00" (local time)
 */
export function utcMinutesToLocalTime(minutes: number): string {
   const utcHours = Math.floor(minutes / 60);
   const utcMinutes = minutes % 60;

   // Create a UTC date with the given time (using today's date)
   const utcDate = new Date();
   utcDate.setUTCHours(utcHours);
   utcDate.setUTCMinutes(utcMinutes);
   utcDate.setUTCSeconds(0);
   utcDate.setUTCMilliseconds(0);

   // Get the browser's local timezone
   const localTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

   // Convert UTC to local time using date-fns-tz
   const localDate = toZonedTime(utcDate, localTimezone);

   // Extract local hours and minutes
   const localHours = localDate.getHours();
   const localMinutes = localDate.getMinutes();

   // Format as HH:mm
   return `${localHours.toString().padStart(2, '0')}:${localMinutes.toString().padStart(2, '0')}`;
}

/**
 * Check if a time range crosses midnight (in local time)
 * @param startTime - Start time in HH:mm format
 * @param endTime - End time in HH:mm format
 * @returns True if the range crosses midnight (e.g., 20:00 to 06:00)
 */
export function isOvernightRange(startTime: string, endTime: string): boolean {
   // Parse local time directly without UTC conversion
   // to determine if range crosses midnight in local timezone
   const [startHours, startMins] = startTime.split(':').map(Number);
   const [endHours, endMins] = endTime.split(':').map(Number);

   const startMinutesLocal = startHours * 60 + startMins;
   const endMinutesLocal = endHours * 60 + endMins;

   return startMinutesLocal > endMinutesLocal;
}

/**
 * Generate JSON Logic for a single time range
 * @param startTime - Start time in HH:mm format
 * @param endTime - End time in HH:mm format
 * @returns JSON Logic object for this time range
 */
function generateSingleRangeLogic(startTime: string, endTime: string): any {
   const startMinutes = localTimeToUtcMinutes(startTime);
   const endMinutes = localTimeToUtcMinutes(endTime);

   if (isOvernightRange(startTime, endTime)) {
      // Overnight range: time >= start OR time < end
      // Example: 8PM-6AM becomes: time >= 1200 OR time < 360
      return {
         or: [
            { '>=': [{ toMinutes: [{ var: 'time' }] }, startMinutes] },
            { '<': [{ toMinutes: [{ var: 'time' }] }, endMinutes] },
         ],
      };
   } else {
      // Same-day range: time >= start AND time < end
      // Example: 8AM-5PM becomes: time >= 480 AND time < 1020
      return {
         and: [
            { '>=': [{ toMinutes: [{ var: 'time' }] }, startMinutes] },
            { '<': [{ toMinutes: [{ var: 'time' }] }, endMinutes] },
         ],
      };
   }
}

/**
 * Generate complete JSON Logic from multiple condition ranges
 * @param ranges - Array of condition ranges
 * @returns Complete JSON Logic object or null if no ranges
 */
export function generateJsonLogic(ranges: ConditionRange[]): any {
   if (!ranges || ranges.length === 0) {
      return null;
   }

   // Generate logic for each range
   const rangeLogics = ranges.map(range => generateSingleRangeLogic(range.startTime, range.endTime));

   if (rangeLogics.length === 1) {
      return rangeLogics[0];
   }

   // Combine multiple ranges using their operators
   // Build nested structure based on operators
   let result = rangeLogics[0];
   for (let i = 1; i < rangeLogics.length; i++) {
      const operator = ranges[i - 1].operator; // Use operator from previous range
      result = {
         [operator]: [result, rangeLogics[i]],
      };
   }

   return result;
}

/**
 * Parse JSON Logic to extract time range information
 * This handles the reverse operation of generateJsonLogic
 * @param condition - JSON Logic condition object
 * @returns Array of condition ranges
 */
export function parseJsonLogic(condition: any): ConditionRange[] {
   if (!condition || typeof condition !== 'object') {
      return [];
   }

   const ranges: ConditionRange[] = [];

   /**
    * Parse a single range logic (either overnight OR or same-day AND)
    */
   function parseSingleRange(logic: any): { startTime: string; endTime: string } | null {
      if (!logic) return null;

      // Overnight range: { "or": [{ ">=": [...] }, { "<": [...] }] }
      if (logic.or && Array.isArray(logic.or) && logic.or.length === 2) {
         const [condition1, condition2] = logic.or;

         // Extract start time from >= condition
         if (condition1['>='] && Array.isArray(condition1['>=']) && condition1['>='].length === 2) {
            const startMinutes = condition1['>='][1];

            // Extract end time from < condition
            if (condition2['<'] && Array.isArray(condition2['<']) && condition2['<'].length === 2) {
               const endMinutes = condition2['<'][1];

               return {
                  startTime: utcMinutesToLocalTime(startMinutes),
                  endTime: utcMinutesToLocalTime(endMinutes),
               };
            }
         }
      }

      // Same-day range: { "and": [{ ">=": [...] }, { "<": [...] }] }
      if (logic.and && Array.isArray(logic.and) && logic.and.length === 2) {
         const [condition1, condition2] = logic.and;

         // Extract start time from >= condition
         if (condition1['>='] && Array.isArray(condition1['>=']) && condition1['>='].length === 2) {
            const startMinutes = condition1['>='][1];

            // Extract end time from < condition
            if (condition2['<'] && Array.isArray(condition2['<']) && condition2['<'].length === 2) {
               const endMinutes = condition2['<'][1];

               return {
                  startTime: utcMinutesToLocalTime(startMinutes),
                  endTime: utcMinutesToLocalTime(endMinutes),
               };
            }
         }
      }

      return null;
   }

   /**
    * Recursively parse nested logic to extract all ranges and operators
    */
   function parseNestedLogic(logic: any, currentOperator: 'or' | 'and' = 'or'): void {
      // Try to parse as a single range first
      const singleRange = parseSingleRange(logic);
      if (singleRange) {
         ranges.push({
            ...singleRange,
            operator: currentOperator,
         });
         return;
      }

      // Check if this is a combining operator (or/and at top level)
      if (logic.or && Array.isArray(logic.or) && logic.or.length === 2) {
         // Check if first element is a range
         const firstRange = parseSingleRange(logic.or[0]);
         if (firstRange) {
            ranges.push({
               ...firstRange,
               operator: 'or',
            });
            parseNestedLogic(logic.or[1], 'or');
            return;
         }
      }

      if (logic.and && Array.isArray(logic.and) && logic.and.length === 2) {
         // Check if first element is a range
         const firstRange = parseSingleRange(logic.and[0]);
         if (firstRange) {
            ranges.push({
               ...firstRange,
               operator: 'and',
            });
            parseNestedLogic(logic.and[1], 'and');
            return;
         }
      }
   }

   parseNestedLogic(condition);

   // Remove operator from last range (not needed for UI)
   if (ranges.length > 0) {
      ranges[ranges.length - 1].operator = 'or'; // Default for last item
   }

   return ranges;
}

/**
 * Validate that a time range is valid
 * @param range - Condition range to validate
 * @returns Error message if invalid, null if valid
 */
export function validateTimeRange(range: ConditionRange | TimeRangeConfig): string | null {
   if (!range.startTime || !range.endTime) {
      return 'Both start and end times are required';
   }

   // Validate time format (HH:mm)
   const timeRegex = /^([0-1][0-9]|2[0-3]):[0-5][0-9]$/;
   if (!timeRegex.test(range.startTime)) {
      return 'Invalid start time format. Use HH:mm (e.g., 20:00)';
   }
   if (!timeRegex.test(range.endTime)) {
      return 'Invalid end time format. Use HH:mm (e.g., 06:00)';
   }

   // Additional validation: start and end can't be the same
   if (range.startTime === range.endTime) {
      return 'Start and end times cannot be the same';
   }

   return null;
}

// ============================================================================
// NEW CONDITION BLOCK FUNCTIONS
// ============================================================================

/**
 * Generate JSON Logic for a booking time condition block
 * @param timeRanges - Array of time ranges for this condition
 * @returns JSON Logic object for the booking time condition
 */
function generateBookingTimeLogic(timeRanges: TimeRangeConfig[]): any {
   if (!timeRanges || timeRanges.length === 0) {
      return null;
   }

   // Generate logic for each time range
   const rangeLogics = timeRanges.map(range => generateSingleRangeLogic(range.startTime, range.endTime));

   if (rangeLogics.length === 1) {
      return rangeLogics[0];
   }

   // Combine multiple ranges using their operators
   let result = rangeLogics[0];
   for (let i = 1; i < rangeLogics.length; i++) {
      const operator = timeRanges[i - 1].operator; // Use operator from previous range
      result = {
         [operator]: [result, rangeLogics[i]],
      };
   }

   return result;
}

/**
 * Generate JSON Logic for a single condition block based on its type
 * @param block - Condition block to convert
 * @returns JSON Logic object for this condition
 */
function generateConditionBlockLogic(block: ConditionBlock): any {
   switch (block.type) {
      case ConditionType.BOOKING_TIME:
         return generateBookingTimeLogic(block.config);
      // Future condition types can be added here:
      // case ConditionType.DAY_OF_WEEK:
      //    return generateDayOfWeekLogic(block.config);
      default:
         return null;
   }
}

/**
 * Generate complete JSON Logic from multiple condition blocks
 * @param blocks - Array of condition blocks
 * @returns Complete JSON Logic object or null if no blocks
 */
export function generateJsonLogicFromBlocks(blocks: ConditionBlock[]): any {
   if (!blocks || blocks.length === 0) {
      return null;
   }

   // Generate logic for each block
   const blockLogics = blocks.map(block => generateConditionBlockLogic(block)).filter(Boolean);

   if (blockLogics.length === 0) {
      return null;
   }

   if (blockLogics.length === 1) {
      return blockLogics[0];
   }

   // Combine multiple blocks using their operators
   let result = blockLogics[0];
   for (let i = 1; i < blockLogics.length; i++) {
      const operator = blocks[i - 1].operator; // Use operator from previous block
      result = {
         [operator]: [result, blockLogics[i]],
      };
   }

   return result;
}

/**
 * Parse booking time JSON logic to extract time ranges
 * @param logic - JSON Logic for booking time condition
 * @returns Array of time range configs
 */
function parseBookingTimeLogic(logic: any): TimeRangeConfig[] {
   if (!logic || typeof logic !== 'object') {
      return [];
   }

   const ranges: TimeRangeConfig[] = [];

   /**
    * Parse a single range logic (either overnight OR or same-day AND)
    */
   function parseSingleRange(rangeLogic: any): { startTime: string; endTime: string } | null {
      if (!rangeLogic) return null;

      // Overnight range: { "or": [{ ">=": [...] }, { "<": [...] }] }
      if (rangeLogic.or && Array.isArray(rangeLogic.or) && rangeLogic.or.length === 2) {
         const [condition1, condition2] = rangeLogic.or;

         if (condition1['>='] && Array.isArray(condition1['>=']) && condition1['>='].length === 2) {
            const startMinutes = condition1['>='][1];

            if (condition2['<'] && Array.isArray(condition2['<']) && condition2['<'].length === 2) {
               const endMinutes = condition2['<'][1];

               return {
                  startTime: utcMinutesToLocalTime(startMinutes),
                  endTime: utcMinutesToLocalTime(endMinutes),
               };
            }
         }
      }

      // Same-day range: { "and": [{ ">=": [...] }, { "<": [...] }] }
      if (rangeLogic.and && Array.isArray(rangeLogic.and) && rangeLogic.and.length === 2) {
         const [condition1, condition2] = rangeLogic.and;

         if (condition1['>='] && Array.isArray(condition1['>=']) && condition1['>='].length === 2) {
            const startMinutes = condition1['>='][1];

            if (condition2['<'] && Array.isArray(condition2['<']) && condition2['<'].length === 2) {
               const endMinutes = condition2['<'][1];

               return {
                  startTime: utcMinutesToLocalTime(startMinutes),
                  endTime: utcMinutesToLocalTime(endMinutes),
               };
            }
         }
      }

      return null;
   }

   /**
    * Recursively parse nested logic to extract all ranges and operators
    */
   function parseNestedLogic(nestedLogic: any, currentOperator: 'or' | 'and' = 'or'): void {
      // Try to parse as a single range first
      const singleRange = parseSingleRange(nestedLogic);
      if (singleRange) {
         ranges.push({
            ...singleRange,
            operator: currentOperator,
         });
         return;
      }

      // Check if this is a combining operator (or/and at top level)
      if (nestedLogic.or && Array.isArray(nestedLogic.or) && nestedLogic.or.length === 2) {
         const firstRange = parseSingleRange(nestedLogic.or[0]);
         if (firstRange) {
            ranges.push({
               ...firstRange,
               operator: 'or',
            });
            parseNestedLogic(nestedLogic.or[1], 'or');
            return;
         }
      }

      if (nestedLogic.and && Array.isArray(nestedLogic.and) && nestedLogic.and.length === 2) {
         const firstRange = parseSingleRange(nestedLogic.and[0]);
         if (firstRange) {
            ranges.push({
               ...firstRange,
               operator: 'and',
            });
            parseNestedLogic(nestedLogic.and[1], 'and');
            return;
         }
      }
   }

   parseNestedLogic(logic);

   // Remove operator from last range (not needed for UI)
   if (ranges.length > 0) {
      ranges[ranges.length - 1].operator = 'or';
   }

   return ranges;
}

/**
 * Check if a JSON logic object represents a single time range
 */
function isSingleTimeRange(logic: any): boolean {
   if (!logic || typeof logic !== 'object') return false;

   // Overnight range: { "or": [{ ">=": [...] }, { "<": [...] }] }
   if (logic.or && Array.isArray(logic.or) && logic.or.length === 2) {
      const [condition1, condition2] = logic.or;
      if (condition1['>='] && condition2['<']) {
         return true;
      }
   }

   // Same-day range: { "and": [{ ">=": [...] }, { "<": [...] }] }
   if (logic.and && Array.isArray(logic.and) && logic.and.length === 2) {
      const [condition1, condition2] = logic.and;
      if (condition1['>='] && condition2['<']) {
         return true;
      }
   }

   return false;
}

/**
 * Parse JSON Logic to extract condition blocks
 * Now properly handles multiple condition blocks combined with AND/OR
 * @param condition - JSON Logic condition object
 * @returns Array of condition blocks
 */
export function parseJsonLogicToBlocks(condition: any): ConditionBlock[] {
   if (!condition || typeof condition !== 'object') {
      return [];
   }

   const blocks: ConditionBlock[] = [];

   /**
    * Recursively extract condition blocks from nested logic
    */
   function extractBlocks(logic: any, currentOperator: 'or' | 'and' = 'or'): void {
      // Check if this is a single time range (base case)
      if (isSingleTimeRange(logic)) {
         const timeRanges = parseBookingTimeLogic(logic);
         if (timeRanges.length > 0) {
            blocks.push({
               type: ConditionType.BOOKING_TIME,
               config: timeRanges,
               operator: currentOperator,
            });
         }
         return;
      }

      // Check if this is a combining operator
      if (logic.or && Array.isArray(logic.or) && logic.or.length === 2) {
         const [first, second] = logic.or;
         extractBlocks(first, 'or');
         extractBlocks(second, 'or');
         return;
      }

      if (logic.and && Array.isArray(logic.and) && logic.and.length === 2) {
         const [first, second] = logic.and;
         extractBlocks(first, 'and');
         extractBlocks(second, 'and');
         return;
      }
   }

   extractBlocks(condition);

   // Remove operator from last block (not needed for UI)
   if (blocks.length > 0) {
      blocks[blocks.length - 1].operator = 'or';
   }

   return blocks;
}

/**
 * Validate a condition block based on its type
 * @param block - Condition block to validate
 * @returns Error message if invalid, null if valid
 */
export function validateConditionBlock(block: ConditionBlock): string | null {
   switch (block.type) {
      case ConditionType.BOOKING_TIME:
         if (!block.config || block.config.length === 0) {
            return 'At least one time range is required';
         }
         // Validate each time range
         for (const range of block.config) {
            const error = validateTimeRange(range);
            if (error) {
               return error;
            }
         }
         return null;
      // Future condition types validation:
      // case ConditionType.DAY_OF_WEEK:
      //    return validateDayOfWeekBlock(block);
      default:
         return 'Unknown condition type';
   }
}

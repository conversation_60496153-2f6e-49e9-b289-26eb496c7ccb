'use client';

import {
   <PERSON><PERSON>,
   Dialog<PERSON>ontent,
   Di<PERSON><PERSON>eader,
   DialogTitle,
   DialogFooter,
   DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';

interface TaxGroupDetachModalProps {
   isOpen: boolean;
   onClose: () => void;
   onConfirm: () => void;
   isLoading: boolean;
   chargeName: string;
   taxGroupName: string;
}

export function TaxGroupDetachModal({
   isOpen,
   onClose,
   onConfirm,
   isLoading,
   chargeName,
   taxGroupName,
}: TaxGroupDetachModalProps) {
   return (
      <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent className='sm:max-w-[500px]'>
            <DialogHeader>
               <DialogTitle>Remove Tax Group</DialogTitle>
               <DialogDescription className='text-sm text-muted-foreground mt-2'>
                  Are you sure you want to remove tax group "{taxGroupName}" from "{chargeName}"?
               </DialogDescription>
            </DialogHeader>

            <DialogFooter>
               <Button type='button' variant='outline' onClick={onClose} disabled={isLoading}>
                  Cancel
               </Button>
               <Button type='button' variant='destructive' onClick={onConfirm} disabled={isLoading}>
                  {isLoading ? (
                     <>
                        <Spinner className='h-4 w-4 mr-2' />
                        Removing...
                     </>
                  ) : (
                     'Remove Tax Group'
                  )}
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
}

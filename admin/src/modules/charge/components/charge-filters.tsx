'use client';

import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { Search, Plus, X, Loader2 } from 'lucide-react';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

interface ChargeFiltersProps {
  search: string;
  onSearchChange: (value: string) => void;
  onClearSearch?: () => void;
  isLoading?: boolean;
  onAddCharge?: () => void;
}

export function ChargeFilters({ search, onSearchChange, onClearSearch, isLoading, onAddCharge }: ChargeFiltersProps) {
   const { withPermission } = useRoleBasedAccess();

   return (
      <div className='flex justify-between items-center gap-4 mb-4'>
         <div className='relative flex-1 max-w-md'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
            <Input
               type='text'
               placeholder='Search charges...'
               value={search}
               onChange={e => onSearchChange(e.target.value)}
               className='pl-10 pr-10'
            />
            {isLoading && search && (
               <Loader2 className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 animate-spin' />
            )}
            {!isLoading && search && onClearSearch && (
               <button
                  onClick={onClearSearch}
                  className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors'
               >
                  <X className='h-4 w-4' />
               </button>
            )}
         </div>
         <div className='flex gap-2'>
            {onAddCharge && (
               <Button
                  className='cursor-pointer'
                  variant='outline'
                  onClick={() => withPermission(RBAC_PERMISSIONS.CHARGE.CREATE, onAddCharge)}
               >
                  <Plus />
                  Add New Charge
               </Button>
            )}
         </div>
      </div>
   );
}

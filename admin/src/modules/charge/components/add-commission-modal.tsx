'use client';

import { useState, useEffect, useId } from 'react';
import { CheckIcon, ChevronDownIcon } from 'lucide-react';
import {
   Dialog,
   DialogContent,
   DialogHeader,
   DialogTitle,
   DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
   Command,
   CommandEmpty,
   CommandGroup,
   CommandInput,
   CommandItem,
   CommandList,
} from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Spinner } from '@/components/ui/spinner';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { useAttachCommission } from '../api/charge-commission-mutations';
import { useGetAllCommissions } from '@/modules/commissions/api/queries';
import { cn } from '@/lib/utils';
import { Commission } from '@/modules/commissions/types/commissions';

interface AttachCommissionModalProps {
   chargeId: string;
   chargeName: string;
   isOpen: boolean;
   onClose: () => void;
}

export function AttachCommissionModal({
   chargeId,
   chargeName,
   isOpen,
   onClose
}: AttachCommissionModalProps) {
   const id = useId();
   const [open, setOpen] = useState<boolean>(false);
   const [selectedCommissionId, setSelectedCommissionId] = useState<string>('');

   const queryClient = useQueryClient();
   const attachCommissionMutation = useAttachCommission();
   const { data: commissionsData, isLoading: isLoadingCommissions } = useGetAllCommissions();

   const availableCommissions = commissionsData?.data || [];

   // Reset form when modal opens
   useEffect(() => {
      if (isOpen) {
         setSelectedCommissionId('');
         setOpen(false);
      }
   }, [isOpen]);

   const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();

      if (!selectedCommissionId) {
         toast.error('Please select a commission');
         return;
      }

      attachCommissionMutation.mutate(
         {
            chargeId,
            commissionId: selectedCommissionId,
         },
         {
            onSuccess: () => {
               toast.success('Commission attached successfully');
               queryClient.invalidateQueries({ queryKey: ['charges'] });
               queryClient.invalidateQueries({ queryKey: ['all-charges'] });
               onClose();
            },
            onError: (error: any) => {
               const errorMessage = error?.response?.data?.message || 'Failed to add commission';
               toast.error(errorMessage);
            },
         }
      );
   };

   const getCommissionDisplay = (commission: Commission) => {
      const typeLabel = commission.type === 'percentage' ? 'Percentage' : 'Flat';
      const value = commission.type === 'percentage'
         ? `${commission.percentageValue}%`
         : `${commission.flatValue}`;
      return `${commission.name} (${typeLabel}: ${value})`;
   };

   return (
      <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent className='sm:max-w-[500px]'>
            <DialogHeader>
               <DialogTitle>Add Commission</DialogTitle>
               <p className='text-sm text-muted-foreground mt-2'>
                  Select a commission to add to "{chargeName}"
               </p>
            </DialogHeader>

            <form onSubmit={handleSubmit}>
               <div className='space-y-4 py-4'>
                  {/* Commission Selection */}
                  <div className='space-y-2'>
                     <Label htmlFor={id}>Select Commission</Label>
                     <Popover open={open} onOpenChange={setOpen}>
                        <PopoverTrigger asChild>
                           <Button
                              id={id}
                              variant='outline'
                              role='combobox'
                              aria-expanded={open}
                              className='bg-background hover:bg-background border-input w-full justify-between px-3 font-normal outline-offset-0 outline-none focus-visible:outline-[3px]'
                           >
                              <span
                                 className={cn(
                                    'truncate',
                                    !selectedCommissionId && 'text-muted-foreground'
                                 )}
                              >
                                 {selectedCommissionId
                                    ? getCommissionDisplay(
                                         availableCommissions.find(
                                            (c: Commission) => c.id === selectedCommissionId
                                         )!
                                      )
                                    : 'Select a commission'}
                              </span>
                              <ChevronDownIcon
                                 size={16}
                                 className='text-muted-foreground/80 shrink-0'
                                 aria-hidden='true'
                              />
                           </Button>
                        </PopoverTrigger>
                        <PopoverContent
                           className='border-input w-full min-w-[var(--radix-popper-anchor-width)] p-0'
                           align='start'
                        >
                           <Command shouldFilter={true}>
                              <CommandInput
                                 placeholder='Search commission...'
                              />
                              {isLoadingCommissions && (
                                 <div className='flex items-center justify-center py-4'>
                                    <Spinner className='h-4 w-4' />
                                 </div>
                              )}
                              <CommandList>
                                 <CommandEmpty>No commission found.</CommandEmpty>
                                 <CommandGroup>
                                    {availableCommissions.map((commission: Commission) => (
                                       <CommandItem
                                          key={commission.id}
                                          value={`${commission.name} ${commission.type} ${commission.percentageValue || commission.flatValue}`}
                                          onSelect={() => {
                                             setSelectedCommissionId(commission.id);
                                             setOpen(false);
                                          }}
                                       >
                                          <div className='flex flex-col flex-1'>
                                             <span className='font-medium'>{commission.name}</span>
                                             <span className='text-xs text-muted-foreground'>
                                                {commission.type === 'percentage'
                                                   ? `${commission.percentageValue}% (Percentage)`
                                                   : `${commission.flatValue} (Flat)`}
                                             </span>
                                          </div>
                                          {selectedCommissionId === commission.id && (
                                             <CheckIcon size={16} className='ml-auto' />
                                          )}
                                       </CommandItem>
                                    ))}
                                 </CommandGroup>
                              </CommandList>
                           </Command>
                        </PopoverContent>
                     </Popover>
                  </div>
               </div>

               <DialogFooter>
                  <Button
                     type='button'
                     variant='outline'
                     onClick={onClose}
                     disabled={attachCommissionMutation.isPending}
                  >
                     Cancel
                  </Button>
                  <Button
                     type='submit'
                     disabled={attachCommissionMutation.isPending || !selectedCommissionId}
                  >
                     {attachCommissionMutation.isPending ? (
                        <>
                           <Spinner className='h-4 w-4 mr-2' />
                           Attaching...
                        </>
                     ) : (
                        'Add Commission'
                     )}
                  </Button>
               </DialogFooter>
            </form>
         </DialogContent>
      </Dialog>
   );
}

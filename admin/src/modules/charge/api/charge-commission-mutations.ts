import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { ChargeResponse, AttachCommissionRequest, DetachCommissionRequest } from '../types/charge';

/**
 * Hook for attaching a commission to a charge
 */
export const useAttachCommission = () => {
  return useMutation({
    mutationFn: async (request: AttachCommissionRequest): Promise<ChargeResponse> => {
      return apiClient.post('/charges/attach-commission', request);
    },
  });
};

/**
 * Hook for detaching a commission from a charge
 */
export const useDetachCommission = () => {
  return useMutation({
    mutationFn: async (request: DetachCommissionRequest): Promise<ChargeResponse> => {
      return apiClient.post('/charges/detach-commission', request);
    },
  });
};

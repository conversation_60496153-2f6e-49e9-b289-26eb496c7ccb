import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import {
  ChargeResponse,
  AttachTaxGroupRequest,
  DetachTaxGroupRequest,
} from '../types/charge';

/**
 * Hook for attaching a tax group to a charge
 */
export const useAttachTaxGroup = () => {
  return useMutation({
    mutationFn: async (request: AttachTaxGroupRequest): Promise<ChargeResponse> => {
      return apiClient.post('/charges/attach-taxGroup', request);
    },
  });
};

/**
 * Hook for detaching a tax group from a charge
 */
export const useDetachTaxGroup = () => {
  return useMutation({
    mutationFn: async (request: DetachTaxGroupRequest): Promise<ChargeResponse> => {
      return apiClient.post('/charges/detach-taxGroup', request);
    },
  });
};

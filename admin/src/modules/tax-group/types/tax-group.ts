// Tax subcategory interface for forms (create/update)
export interface TaxSubcategory {
  name: string;
  percentage: number;
}

// Tax subcategory response from API
export interface TaxSubcategoryResponse {
  id: string;
  taxGroupId: string;
  name: string;
  percentage: string; // API returns as string
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

// Tax group interface for API responses
export interface TaxGroup {
  id: string;
  name: string;
  description?: string | null;
  totalPercentage: string; // API returns as string
  subcategories: TaxSubcategoryResponse[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
}

// API response structure for listing tax groups
export interface ListTaxGroupResponse {
  success: boolean;
  message: string;
  data: TaxGroup[]; // Array directly, not nested
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  timestamp: number;
}

// API response structure for single tax group
export interface TaxGroupResponse {
  success: boolean;
  message: string;
  data: TaxGroup;
  timestamp: number;
}

// Request for creating tax group
export interface CreateTaxGroupRequest {
  name: string;
  description?: string;
  subcategories: TaxSubcategory[];
}

// Request for updating tax group
export interface UpdateTaxGroupRequest {
  name?: string;
  description?: string;
  subcategories?: TaxSubcategory[];
}

// Parameters for listing tax groups with pagination
export interface ListTaxGroupParams {
  page?: number;
  limit?: number;
  search?: string;
}

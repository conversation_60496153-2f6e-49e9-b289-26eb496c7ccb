import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import {
  TaxGroupResponse,
  ListTaxGroupParams,
  ListTaxGroupResponse,
} from '../types/tax-group';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

/**
 * Hook for listing tax groups with pagination and filters
 */
export const useListTaxGroup = ({
  page,
  limit,
  search,
}: ListTaxGroupParams) => {
  const { hasPermission } = useRoleBasedAccess();

  return useQuery({
    placeholderData: keepPreviousData,
    enabled: hasPermission(RBAC_PERMISSIONS.TAX_GROUP.LIST),
    queryKey: [
      'tax-groups',
      page,
      limit,
      search,
    ],
    refetchOnWindowFocus: false,
    queryFn: (): Promise<ListTaxGroupResponse> => {
      return apiClient.get('/tax-groups/paginated', {
        params: {
          page,
          limit,
          search,
        },
      });
    },
  });
};

/**
 * Hook for getting a single tax group by ID
 */
export const useGetTaxGroup = (id: string | null) => {
  const { hasPermission } = useRoleBasedAccess();

  return useQuery({
    queryKey: ['tax-group', id],
    queryFn: (): Promise<TaxGroupResponse> => {
      return apiClient.get(`/tax-groups/${id || ''}`);
    },
    enabled: !!id && hasPermission(RBAC_PERMISSIONS.TAX_GROUP.EDIT),
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook for getting all active tax groups without pagination
 */
export const useGetActiveTaxGroups = () => {
  const { hasPermission } = useRoleBasedAccess();

  return useQuery({
    enabled: hasPermission(RBAC_PERMISSIONS.TAX_GROUP.LIST),
    queryKey: ['tax-groups-active'],
    queryFn: (): Promise<{ success: boolean; message: string; data: any[]; timestamp: number }> => {
      return apiClient.get('/tax-groups/active');
    },
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes - active tax groups don't change often
  });
};

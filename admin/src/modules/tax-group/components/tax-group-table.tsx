'use client';

import { CustomPagination } from '@/components/pagination';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { useState } from 'react';
import { useDeleteTaxGroup } from '../api/mutations';
import { ListTaxGroupResponse, TaxGroup } from '../types/tax-group';
import { TaxGroupModal } from './tax-group-modal';
import { TaxGroupTableEmpty } from './tax-group-table-empty';
import { TaxGroupTableLoading } from './tax-group-table-loading';
import { TaxGroupDeleteModal } from './tax-group-delete-modal';
import {
   USE_ROLE_BASED_ACCESS,
   useRoleBasedAccess,
} from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

const getColumns = ({
   deleteTaxGroupMutation,
   handleEditClick,
   handleDeleteClick,
   taxGroupToDelete,
   withPermission,
}: {
   handleEditClick: (id: string) => void;
   handleDeleteClick: (taxGroup: TaxGroup) => void;
   deleteTaxGroupMutation: any;
   taxGroupToDelete: TaxGroup | null;
   withPermission: USE_ROLE_BASED_ACCESS['withPermission'];
}): ColumnDef<TaxGroup>[] => [
   {
      accessorKey: 'name',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Name</div>,
      cell: ({ row }) => {
         const taxGroup = row.original as TaxGroup;
         return (
            <div className='text-left max-w-[140px]'>
               <div className='text-sm font-medium break-words'>{taxGroup.name}</div>
            </div>
         );
      },
      size: 140,
   },
   {
      accessorKey: 'description',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Description</div>
      ),
      cell: ({ row }) => {
         const taxGroup = row.original as TaxGroup;
         return (
            <div className='text-left max-w-[160px]'>
               <div className='text-sm text-gray-600 break-words'>
                  {taxGroup.description || 'No description'}
               </div>
            </div>
         );
      },
      size: 160,
   },
   {
      accessorKey: 'subcategories',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Subcategories</div>
      ),
      cell: ({ row }) => {
         const taxGroup = row.original as TaxGroup;
         return (
            <div className='text-left max-w-[240px]'>
               <div className='flex flex-wrap gap-1'>
                  {taxGroup.subcategories.map((sub) => (
                     <span
                        key={sub.id}
                        className='px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200'
                     >
                        {sub.name}: {sub.percentage}%
                     </span>
                  ))}
               </div>
            </div>
         );
      },
      size: 240,
   },
   {
      accessorKey: 'totalTax',
      header: () => (
         <div className='text-center font-semibold text-gray-600 text-sm'>Total Tax</div>
      ),
      cell: ({ row }) => {
         const taxGroup = row.original as TaxGroup;
         const totalTax = taxGroup.subcategories.reduce(
            (sum, sub) => sum + parseFloat(sub.percentage),
            0
         );
         return (
            <div className='flex justify-center'>
               <span className='px-3 py-1 rounded-md text-sm font-semibold bg-green-50 text-green-700 border border-green-200'>
                  {totalTax.toFixed(2)}%
               </span>
            </div>
         );
      },
      size: 100,
   },
   {
      accessorKey: 'isActive',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Status</div>,
      cell: ({ row }) => {
         const taxGroup = row.original as TaxGroup;

         return (
            <div className='flex justify-center'>
               <span
                  className={`px-2 py-1 rounded-full text-xs font-medium ${
                     taxGroup.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}
               >
                  {taxGroup.isActive ? 'Active' : 'Inactive'}
               </span>
            </div>
         );
      },
      size: 90,
   },
   {
      accessorKey: 'createdAt',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Created Date</div>
      ),
      cell: ({ row }) => {
         const taxGroup = row.original as TaxGroup;
         const date = new Date(taxGroup.createdAt);
         return (
            <div className='text-left'>
               <div className='text-sm text-gray-600'>
                  {date.toLocaleDateString('en-US', {
                     year: 'numeric',
                     month: 'short',
                     day: 'numeric',
                  })}
               </div>
            </div>
         );
      },
      size: 130,
   },
   {
      id: 'actions',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const taxGroup = row.original as TaxGroup;
         const isDeleting =
            taxGroupToDelete?.id === taxGroup.id && deleteTaxGroupMutation.isPending;

         return (
            <div className='flex justify-center gap-1'>
               <button
                  className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() => {
                     withPermission(RBAC_PERMISSIONS.TAX_GROUP.EDIT, () =>
                        handleEditClick(taxGroup.id)
                     );
                  }}
                  disabled={isDeleting}
               >
                  Edit
               </button>
               <button
                  className='text-sm font-medium text-red-600 hover:text-red-700 border border-red-300 hover:border-red-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() => {
                     withPermission(RBAC_PERMISSIONS.TAX_GROUP.DELETE, () =>
                        handleDeleteClick(taxGroup)
                     );
                  }}
                  disabled={isDeleting}
               >
                  {isDeleting ? '...' : 'Delete'}
               </button>
            </div>
         );
      },
      size: 140,
   },
];

interface TaxGroupTableProps {
   data: ListTaxGroupResponse | undefined;
   isLoading: boolean;
   currentPage: number;
   onPageChange: (page: number) => void;
   hasFilters?: boolean;
   hasSearch?: boolean;
   onClearFilters?: () => void;
}

export function TaxGroupTable({
   data,
   isLoading,
   currentPage,
   onPageChange,
   hasFilters: _hasFilters,
   hasSearch: _hasSearch,
   onClearFilters: _onClearFilters,
}: TaxGroupTableProps) {
   const [taxGroupToEdit, setTaxGroupToEdit] = useState<string | null>(null);
   const [taxGroupToDelete, setTaxGroupToDelete] = useState<TaxGroup | null>(null);
   const deleteTaxGroupMutation = useDeleteTaxGroup();
   const queryClient = useQueryClient();
   const { withPermission } = useRoleBasedAccess();

   const handleEditClick = (id: string) => {
      setTaxGroupToEdit(id);
   };

   const handleDeleteClick = (taxGroup: TaxGroup) => {
      setTaxGroupToDelete(taxGroup);
   };

   const handleDeleteConfirm = () => {
      if (!taxGroupToDelete) return;

      deleteTaxGroupMutation.mutate(taxGroupToDelete.id, {
         onSuccess: () => {
            toast.success('Tax group deleted successfully');
            queryClient.invalidateQueries({ queryKey: ['tax-groups'] });
         },
         onSettled: () => {
            setTaxGroupToDelete(null);
         },
      });
   };

   const columns = getColumns({
      deleteTaxGroupMutation,
      handleEditClick,
      handleDeleteClick,
      taxGroupToDelete,
      withPermission,
   });

   const table = useReactTable({
      data: data?.data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   if (isLoading) {
      return <TaxGroupTableLoading />;
   }

   if (!data?.data?.length) {
      return <TaxGroupTableEmpty />;
   }

   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full table-fixed'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize(), maxWidth: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                         header.column.columnDef.header,
                                         header.getContext()
                                      )}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td
                                 key={cell.id}
                                 className='px-4 py-3 align-middle'
                                 style={{
                                    width: cell.column.getSize(),
                                    maxWidth: cell.column.getSize(),
                                 }}
                              >
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>

         {data && data.meta && data.meta.totalPages > 1 && (
            <CustomPagination
               currentPage={currentPage}
               totalPages={data.meta.totalPages}
               onPageChange={onPageChange}
               hasNext={data.meta.hasNextPage}
               hasPrev={data.meta.hasPrevPage}
            />
         )}

         {/* Delete Confirmation Modal */}
         <TaxGroupDeleteModal
            isOpen={!!taxGroupToDelete}
            onClose={() => setTaxGroupToDelete(null)}
            onConfirm={handleDeleteConfirm}
            isLoading={deleteTaxGroupMutation.isPending}
            taxGroupName={taxGroupToDelete?.name || ''}
         />

         {/* Edit Modal */}
         <TaxGroupModal
            mode='edit'
            taxGroupId={taxGroupToEdit}
            isOpen={!!taxGroupToEdit}
            onClose={() => setTaxGroupToEdit(null)}
         />
      </div>
   );
}

'use client';

import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface TaxGroupToggleModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading: boolean;
  taxGroupName: string;
  currentStatus: boolean; // true = active, false = inactive
}

export const TaxGroupToggleModal = ({
  isOpen,
  onClose,
  onConfirm,
  isLoading,
  taxGroupName,
  currentStatus,
}: TaxGroupToggleModalProps) => {
  const action = currentStatus ? 'deactivate' : 'activate';
  const actionCapitalized = currentStatus ? 'Deactivate' : 'Activate';

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-sm'>
        <DialogHeader>
          <DialogTitle>{actionCapitalized} Tax Group</DialogTitle>
          <DialogDescription>
            Are you sure you want to {action} the tax group "{taxGroupName}"?
            {currentStatus
              ? ' This will make the tax group unavailable for use.'
              : ' This will make the tax group available for use.'
            }
          </DialogDescription>
        </DialogHeader>

        <div className='flex gap-3 pt-4'>
          <Button
            type='button'
            variant='outline'
            onClick={onClose}
            className='flex-1'
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type='button'
            onClick={onConfirm}
            className={`flex-1 ${
              !currentStatus
                ? 'bg-green-600 hover:bg-green-700 text-white'
                : ''
            }`}
            variant={currentStatus ? 'destructive' : 'default'}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                {actionCapitalized}...
                <Spinner className='ml-2 h-4 w-4' />
              </>
            ) : (
              actionCapitalized
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Helper function for number input onChange - handles decimals and clearing
 * Restricts decimal places to a maximum of 2 digits for monetary values
 *
 * @param value - The input string value
 * @param onChange - The form field onChange handler
 *
 * @example
 * ```tsx
 * <Input
 *   type="number"
 *   step="0.01"
 *   onChange={e => handleNumberInputChange(e.target.value, field.onChange)}
 * />
 * ```
 */
export const handleNumberInputChange = (value: string, onChange: (val: any) => void) => {
  if (value === '') {
    onChange('');
    return;
  }

  // Check if the value has more than 2 decimal places
  const decimalParts = value.split('.');
  if (decimalParts.length === 2 && decimalParts[1].length > 2) {
    // Don't update if more than 2 decimal places
    return;
  }

  const num = parseFloat(value);
  onChange(isNaN(num) ? '' : num);
};

/**
 * Helper function to get display value for number inputs
 * Handles undefined, null, and empty string values
 *
 * @param value - The form field value
 * @returns Empty string for falsy values, otherwise the value itself
 *
 * @example
 * ```tsx
 * <Input
 *   type="number"
 *   value={getNumberDisplayValue(field.value)}
 * />
 * ```
 */
export const getNumberDisplayValue = (value: any): string | number => {
  if (value === undefined || value === null || value === '') {
    return '';
  }
  return value;
};

/**
 * Helper function for integer input onChange - handles clearing
 * Only accepts whole numbers (no decimals)
 *
 * @param value - The input string value
 * @param onChange - The form field onChange handler
 *
 * @example
 * ```tsx
 * <Input
 *   type="number"
 *   step="1"
 *   onChange={e => handleIntegerInputChange(e.target.value, field.onChange)}
 * />
 * ```
 */
export const handleIntegerInputChange = (value: string, onChange: (val: any) => void) => {
  if (value === '') {
    onChange('');
    return;
  }

  const num = parseInt(value, 10);
  onChange(isNaN(num) ? '' : num);
};

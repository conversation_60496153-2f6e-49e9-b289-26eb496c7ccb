'use client';

import { LoadScript } from '@react-google-maps/api';
import { ReactNode } from 'react';

const libraries: ('drawing' | 'places')[] = ['drawing', 'places'];

interface GoogleMapsProviderProps {
   children: ReactNode;
}

export function GoogleMapsProvider({ children }: GoogleMapsProviderProps) {
   const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

   if (!apiKey) {
      console.error('Google Maps API key is not configured');
      return <>{children}</>;
   }

   return (
      <LoadScript
         googleMapsApiKey={apiKey}
         libraries={libraries}
         loadingElement={<div></div>}
      >
         {children}
      </LoadScript>
   );
}

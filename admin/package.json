{"name": "admin", "version": "0.1.0", "private": true, "scripts": {"dev": "dotenv -e .env -- sh -c 'next dev --turbopack -p ${PORT:-3000}'", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui"}, "dependencies": {"@googlemaps/markerclusterer": "^2.6.2", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@react-google-maps/api": "^2.20.7", "@sentry/nextjs": "^10", "@tanstack/react-query": "^5.83.0", "@tanstack/react-table": "^8.21.3", "@turf/boolean-intersects": "^7.2.0", "@turf/turf": "^7.2.0", "axios": "^1.10.0", "buffer": "^6.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "input-otp": "^1.4.2", "lucide-react": "^0.548.0", "next": "15.4.1", "nextjs-toploader": "^3.8.16", "radix-ui": "^1.4.2", "react": "^19.0.0", "react-day-picker": "^9.8.1", "react-dom": "^19.0.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.60.0", "react-phone-number-input": "^3.4.12", "react-simple-wysiwyg": "^3.4.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "wkx": "^0.5.0", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitest/ui": "^3.2.4", "dotenv-cli": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.3.5", "eslint-plugin-react-hooks": "^7.0.1", "jsdom": "^27.0.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5", "vitest": "^3.2.4"}, "packageManager": "pnpm@9.15.5+sha512.845196026aab1cc3f098a0474b64dfbab2afe7a1b4e91dd86895d8e4aa32a7a6d03049e2d0ad770bbe4de023a7122fb68c1a1d6e0d033c7076085f9d5d4800d4"}